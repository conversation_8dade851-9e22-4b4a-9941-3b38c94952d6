<?php

namespace App\Services;

use App\Models\User;
use App\Models\Referral;
use App\Models\UserCredit;
use App\Models\CreditTransaction;
use App\Models\EmailTemplate;
use App\Models\UserActivityLog;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class ReferralService
{
    protected $creditService;
    protected $userActivityService;

    public function __construct(CreditService $creditService, UserActivityService $userActivityService)
    {
        $this->creditService = $creditService;
        $this->userActivityService = $userActivityService;
    }

    /**
     * Generate a unique referral code for a user
     *
     * @param User $user
     * @return string
     */
    public function generateReferralCode(User $user)
    {
        // Check if user already has a referral code
        if ($user->referral_code) {
            return $user->referral_code;
        }

        // Generate a unique code based on user's name and a random string
        $namePrefix = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $user->name), 0, 3));
        $randomString = strtoupper(Str::random(5));
        $referralCode = $namePrefix . $randomString;

        // Make sure it's unique
        while (User::where('referral_code', $referralCode)->exists()) {
            $randomString = strtoupper(Str::random(5));
            $referralCode = $namePrefix . $randomString;
        }

        // Save the referral code to the user
        $user->referral_code = $referralCode;
        $user->save();

        return $referralCode;
    }

    /**
     * Create a new referral
     *
     * @param User $referrer
     * @param string|null $email
     * @return Referral
     */
    public function createReferral(User $referrer, ?string $email = null)
    {
        // Make sure the user has a referral code
        if (!$referrer->referral_code) {
            $this->generateReferralCode($referrer);
        }

        // Create a new referral record
        $referral = new Referral([
            'referrer_id' => $referrer->id,
            'referral_code' => $referrer->referral_code,
            'email' => $email,
            'status' => 'pending',
            'credit_amount' => 3.00, // Default $3 credit
        ]);

        $referral->save();

        return $referral;
    }

    /**
     * Process a referral when a new user registers
     *
     * @param string $referralCode
     * @param User $newUser
     * @return bool
     */
    public function processReferral(string $referralCode, User $newUser)
    {
        try {
            Log::info("Processing referral", [
                'referral_code' => $referralCode,
                'new_user_id' => $newUser->id,
                'new_user_email' => $newUser->email
            ]);

            // Find the referrer by code
            $referrer = User::where('referral_code', $referralCode)->first();

            if (!$referrer) {
                Log::warning("Invalid referral code: {$referralCode}", [
                    'new_user_id' => $newUser->id
                ]);
                return false;
            }

            // Check if the user is trying to refer themselves
            if ($referrer->id === $newUser->id) {
                Log::warning("User tried to refer themselves", [
                    'user_id' => $newUser->id,
                    'referral_code' => $referralCode
                ]);
                return false;
            }

            // Check if the user has already been referred
            if ($newUser->referred_by) {
                Log::warning("User already has a referrer", [
                    'user_id' => $newUser->id,
                    'existing_referrer_id' => $newUser->referred_by,
                    'attempted_referrer_id' => $referrer->id
                ]);
                return false;
            }

            // Start a transaction to ensure data consistency
            DB::beginTransaction();

            try {
                // Find or create a referral record
                // First try to find an existing referral with the user's email
                $referral = Referral::where('referrer_id', $referrer->id)
                    ->where('referral_code', $referralCode)
                    ->where('email', $newUser->email)
                    ->where('status', 'pending')
                    ->first();

                // If no email-specific referral found, try to find a generic one (without email)
                if (!$referral) {
                    $referral = Referral::where('referrer_id', $referrer->id)
                        ->where('referral_code', $referralCode)
                        ->whereNull('email')
                        ->where('status', 'pending')
                        ->first();
                }

                if (!$referral) {
                    Log::info("Creating new referral record", [
                        'referrer_id' => $referrer->id,
                        'new_user_id' => $newUser->id,
                        'new_user_email' => $newUser->email
                    ]);

                    $referral = new Referral([
                        'referrer_id' => $referrer->id,
                        'referral_code' => $referralCode,
                        'email' => $newUser->email,
                        'status' => 'pending',
                        'credit_amount' => 3.00, // Default $3 credit
                    ]);
                }

                // Update the referral with the new user's ID
                $referral->referred_id = $newUser->id;
                $referral->status = 'completed';
                $referral->completed_at = now();
                $referral->save();

                // Update the new user with the referrer ID
                $newUser->referred_by = $referrer->id;
                $newUser->save();

                // We'll award credit only after 3 consecutive days of app usage
                // The credit will be awarded by a scheduled task
                // For now, just mark the referral as completed

                Log::info("Referral completed but credit not yet awarded - waiting for 3 days of activity", [
                    'referral_id' => $referral->id,
                    'referrer_id' => $referrer->id,
                    'new_user_id' => $newUser->id
                ]);

                DB::commit();

                Log::info("Successfully processed referral", [
                    'referral_id' => $referral->id,
                    'referrer_id' => $referrer->id,
                    'new_user_id' => $newUser->id,
                    'status' => 'completed'
                ]);

                return true;
            } catch (\Exception $innerException) {
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $e) {
            Log::error("Error processing referral: " . $e->getMessage(), [
                'referral_code' => $referralCode,
                'new_user_id' => $newUser->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Award credit to the referrer
     *
     * @param Referral $referral
     * @param bool $forceAward Bypass the 3-day activity check
     * @return bool
     */
    public function awardReferralCredit(Referral $referral, bool $forceAward = false)
    {
        // Check if credit has already been awarded
        if ($referral->credit_awarded) {
            Log::info("Credit already awarded for referral ID: {$referral->id}");
            return false;
        }

        DB::beginTransaction();

        try {
            // Make sure the referrer exists
            $referrer = User::find($referral->referrer_id);
            if (!$referrer) {
                Log::error("Referrer not found for referral ID: {$referral->id}");
                DB::rollBack();
                return false;
            }

            // Make sure the referred user exists if set
            $referredName = 'a new user';
            if ($referral->referred_id) {
                $referred = User::find($referral->referred_id);
                if ($referred) {
                    $referredName = $referred->name;

                    // Check if the referred user has been active for 3 consecutive days
                    // Skip this check if forceAward is true
                    if (!$forceAward) {
                        $hasConsecutiveActivity = $this->userActivityService->hasConsecutiveDaysActivity($referred, 3);

                        if (!$hasConsecutiveActivity) {
                            Log::info("Not awarding referral credit yet - user hasn't been active for 3 consecutive days", [
                                'referral_id' => $referral->id,
                                'referred_id' => $referred->id,
                                'referrer_id' => $referrer->id
                            ]);
                            DB::rollBack();
                            return false;
                        }
                    }
                } else {
                    Log::warning("Referred user not found for referral ID: {$referral->id}");
                }
            }

            // Award credit to the referrer
            $description = "Referral credit for inviting {$referredName}";

            Log::info("Awarding referral credit", [
                'referral_id' => $referral->id,
                'referrer_id' => $referral->referrer_id,
                'amount' => $referral->credit_amount,
                'description' => $description
            ]);

            $transaction = $this->creditService->addCredit(
                $referral->referrer_id,
                $referral->credit_amount,
                'referral',
                $referral->id,
                $description
            );

            // Mark the referral as credited
            $referral->credit_awarded = true;
            $referral->save();

            DB::commit();

            Log::info("Successfully awarded referral credit", [
                'referral_id' => $referral->id,
                'transaction_id' => $transaction->id,
                'amount' => $referral->credit_amount
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error awarding referral credit: " . $e->getMessage(), [
                'referral_id' => $referral->id,
                'referrer_id' => $referral->referrer_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get all referrals for a user
     *
     * @param User $user
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserReferrals(User $user)
    {
        return Referral::where('referrer_id', $user->id)
            ->with('referred')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Send a referral invitation email
     *
     * @param User $referrer
     * @param string $email
     * @param string $referralCode
     * @return bool
     */
    public function sendReferralInvitation(User $referrer, string $email, string $referralCode)
    {
        try {
            Log::info('Starting referral invitation email send', [
                'referrer_id' => $referrer->id,
                'referrer_name' => $referrer->name,
                'email' => $email,
                'referral_code' => $referralCode
            ]);

            // Create a template if it doesn't exist
            $template = EmailTemplate::where('slug', 'referral-invitation')->first();

            if (!$template) {
                Log::info('Creating referral invitation email template');
                // Create the template
                $template = new EmailTemplate([
                    'name' => 'Referral Invitation',
                    'slug' => 'referral-invitation',
                    'subject' => '🎉 You\'re Invited to Join Medroid AI - Exclusive Healthcare Access!',
                    'description' => 'Email sent to invite friends to join Medroid',
                    'content' => 'Personalized invitation email with referral code, benefits explanation, and signup incentives.',
                    'is_active' => true
                ]);
                $template->save();
                Log::info('Created referral invitation email template');
            }

            // Check if email template file exists
            $templatePath = resource_path('views/emails/referral-invitation.blade.php');
            if (!file_exists($templatePath)) {
                Log::error('Referral invitation email template file not found', [
                    'path' => $templatePath
                ]);
                throw new \Exception('Email template file not found');
            }

            // Send the email using Mail facade
            Mail::send('emails.referral-invitation', [
                'user' => $referrer,
                'referralCode' => $referralCode
            ], function ($message) use ($email, $referrer) {
                $message->to($email)
                    ->subject('🎉 You\'re Invited to Join Medroid AI - Invitation from ' . $referrer->name);
            });

            Log::info('Referral invitation email sent successfully', [
                'referrer_id' => $referrer->id,
                'email' => $email,
                'referral_code' => $referralCode
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send referral invitation email', [
                'referrer_id' => $referrer->id,
                'email' => $email,
                'referral_code' => $referralCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw the exception so the controller can handle it
            throw $e;
        }
    }
}

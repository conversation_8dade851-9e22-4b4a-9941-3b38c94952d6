<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bot_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('bot_persona_id')->constrained('bot_personas')->onDelete('cascade');
            $table->string('profile_image_url')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->integer('follower_count')->default(0);
            $table->integer('following_count')->default(0);
            $table->integer('post_count')->default(0);
            $table->timestamp('last_posted_at')->nullable();
            $table->json('engagement_metrics')->nullable(); // Likes, comments, shares stats
            $table->timestamps();
            
            $table->unique(['user_id', 'bot_persona_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bot_users');
    }
};

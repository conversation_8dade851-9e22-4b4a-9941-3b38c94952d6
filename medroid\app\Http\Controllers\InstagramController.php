<?php

namespace App\Http\Controllers;

use App\Services\InstagramService;
use App\Services\MediaCleanupService;
use App\Models\InstagramAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class InstagramController extends Controller
{
    private $instagramService;

    public function __construct(InstagramService $instagramService)
    {
        $this->instagramService = $instagramService;
    }

    /**
     * Get Instagram authorization URL
     */
    public function getAuthUrl(Request $request)
    {
        try {
            $state = base64_encode(json_encode([
                'user_id' => Auth::id(),
                'timestamp' => time(),
            ]));

            $authUrl = $this->instagramService->getAuthorizationUrl($state);

            return response()->json([
                'success' => true,
                'auth_url' => $authUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram auth URL generation failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate authorization URL',
            ], 500);
        }
    }

    /**
     * Handle Instagram OAuth callback
     */
    public function handleCallback(Request $request)
    {
        try {
            $code = $request->get('code');
            $state = $request->get('state');
            $error = $request->get('error');

            if ($error) {
                return redirect('/discover?instagram_error=' . urlencode($error));
            }

            if (!$code || !$state) {
                return redirect('/discover?instagram_error=missing_parameters');
            }

            // Decode and validate state
            $stateData = json_decode(base64_decode($state), true);
            if (!$stateData || !isset($stateData['user_id'])) {
                return redirect('/discover?instagram_error=invalid_state');
            }

            // Find the user
            $user = \App\Models\User::find($stateData['user_id']);
            if (!$user) {
                return redirect('/discover?instagram_error=user_not_found');
            }

            // Connect the account
            $result = $this->instagramService->connectAccount($user, $code);

            if ($result['success']) {
                return redirect('/discover?instagram_success=1');
            } else {
                return redirect('/discover?instagram_error=' . urlencode($result['message']));
            }
        } catch (\Exception $e) {
            Log::error('Instagram callback failed', ['error' => $e->getMessage()]);
            return redirect('/discover?instagram_error=callback_failed');
        }
    }

    /**
     * Get user's Instagram account status
     */
    public function getAccountStatus(Request $request)
    {
        try {
            $user = Auth::user();

            // Check all accounts for this user (active and inactive)
            $allAccounts = InstagramAccount::where('user_id', $user->id)->get();
            $activeAccount = $allAccounts->where('is_active', true)->first();

            Log::info('Instagram account status check', [
                'user_id' => $user->id,
                'total_accounts' => $allAccounts->count(),
                'active_accounts' => $allAccounts->where('is_active', true)->count(),
                'inactive_accounts' => $allAccounts->where('is_active', false)->count(),
                'active_account_found' => $activeAccount !== null,
                'active_account_id' => $activeAccount?->id,
                'username' => $activeAccount?->username,
                'all_accounts_debug' => $allAccounts->map(function($acc) {
                    return [
                        'id' => $acc->id,
                        'username' => $acc->username,
                        'is_active' => $acc->is_active,
                        'created_at' => $acc->created_at
                    ];
                })->toArray()
            ]);

            if (!$activeAccount) {
                return response()->json([
                    'success' => true,
                    'connected' => false,
                    'message' => 'No active Instagram account found',
                ])->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                  ->header('Pragma', 'no-cache')
                  ->header('Expires', '0');
            }

            return response()->json([
                'success' => true,
                'connected' => true,
                'account' => [
                    'username' => $activeAccount->username,
                    'account_type' => $activeAccount->account_type,
                    'account_type_display' => $activeAccount->account_type_display,
                    'media_count' => $activeAccount->media_count,
                    'last_synced_at' => $activeAccount->last_sync_at?->diffForHumans(),
                    'needs_sync' => $activeAccount->needsSync(),
                    'token_expired' => $activeAccount->isTokenExpired(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram account status check failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to check account status',
            ], 500);
        }
    }

    /**
     * Disconnect Instagram account
     */
    public function disconnect(Request $request)
    {
        try {
            $user = Auth::user();

            // Get ALL Instagram accounts for this user (active and inactive)
            $accounts = InstagramAccount::where('user_id', $user->id)->get();

            if ($accounts->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No Instagram account found',
                ], 404);
            }

            // Get the active account for logging
            $activeAccount = $accounts->where('is_active', true)->first();
            $accountInfo = $activeAccount ?: $accounts->first();

            Log::info('Disconnecting Instagram account(s) and cleaning up content', [
                'user_id' => $user->id,
                'total_accounts' => $accounts->count(),
                'active_accounts' => $accounts->where('is_active', true)->count(),
                'account_ids' => $accounts->pluck('id')->toArray(),
                'usernames' => $accounts->pluck('username')->toArray(),
                'primary_account_id' => $accountInfo->id,
                'primary_username' => $accountInfo->username
            ]);

            // Get all media URLs before deleting records for file cleanup
            $allMediaUrls = [];
            
            // Get SocialContent media URLs
            $socialContentRecords = \App\Models\SocialContent::where('user_id', $user->id)
                ->where('source', 'instagram')
                ->get();
            
            foreach ($socialContentRecords as $content) {
                $urls = array_filter([
                    $content->media_url,
                    $content->thumbnail_url,
                    $content->video_url
                ]);
                $allMediaUrls = array_merge($allMediaUrls, $urls);
            }
            
            // Get SocialMediaPost media URLs (including computed ones)
            $socialPostRecords = \App\Models\SocialMediaPost::where('user_id', $user->id)
                ->where('platform', 'instagram')
                ->get();
            
            foreach ($socialPostRecords as $post) {
                $urls = array_filter([
                    $post->media_url,
                    $post->thumbnail_url,
                    $post->getDisplayMediaUrlAttribute(),
                    $post->getVideoUrlAttribute()
                ]);
                $allMediaUrls = array_merge($allMediaUrls, $urls);
            }
            
            // Remove duplicates
            $allMediaUrls = array_unique($allMediaUrls);

            // Remove all SocialContent entries from this account (from discover feed)
            $socialContentDeleted = \App\Models\SocialContent::where('user_id', $user->id)
                ->where('source', 'instagram')
                ->delete();

            // Remove all SocialMediaPost entries from this account
            $socialMediaPostsDeleted = \App\Models\SocialMediaPost::where('user_id', $user->id)
                ->where('platform', 'instagram')
                ->delete();

            // Clean up associated media files using robust service
            $filesDeleted = \App\Services\MediaCleanupService::cleanupMediaFiles(
                $allMediaUrls, 
                'instagram_disconnect_user_' . $user->id
            );

            // Clear all Instagram-related cache for this user
            $cacheKeys = [
                "instagram_progress_{$user->id}",
                "user_feed_changed_{$user->id}",
            ];

            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            // ROBUST FIX: Completely delete ALL Instagram accounts for this user
            // This prevents duplicate account issues when reconnecting
            $deletedAccountIds = $accounts->pluck('id')->toArray();
            $deletedCount = InstagramAccount::where('user_id', $user->id)->delete();

            Log::info('Completely removed all Instagram accounts for user to prevent duplicates', [
                'user_id' => $user->id,
                'deleted_account_ids' => $deletedAccountIds,
                'deleted_count' => $deletedCount
            ]);

            Log::info('Instagram account(s) completely disconnected and all data cleaned up', [
                'user_id' => $user->id,
                'deleted_account_ids' => $deletedAccountIds,
                'accounts_deleted' => $deletedCount,
                'social_content_deleted' => $socialContentDeleted,
                'social_media_posts_deleted' => $socialMediaPostsDeleted,
                'media_files_deleted' => $filesDeleted,
                'cache_cleared' => count($cacheKeys)
            ]);

            // Set a flag for frontend to detect feed changes
            Cache::put("user_feed_changed_{$user->id}", true, 300); // 5 minutes

            // Additional cleanup: Remove any orphaned data that might exist
            $this->cleanupOrphanedInstagramData($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Instagram account completely disconnected and all data removed successfully',
                'accounts_removed' => $deletedCount,
                'removed_posts' => $socialMediaPostsDeleted,
                'removed_feed_content' => $socialContentDeleted,
                'media_files_deleted' => $filesDeleted,
                'cache_cleared' => true,
                'requires_feed_reload' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram disconnect failed', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to disconnect Instagram account',
            ], 500);
        }
    }

    /**
     * Get Instagram connection progress
     */
    public function getConnectionProgress(Request $request)
    {
        try {
            $user = Auth::user();
            $cacheKey = "instagram_progress_{$user->id}";

            $progress = Cache::get($cacheKey, [
                'status' => 'idle',
                'step' => '',
                'progress' => 0,
                'message' => '',
                'imported_count' => 0
            ]);

            return response()->json([
                'success' => true,
                'progress' => $progress
            ])->header('Cache-Control', 'no-cache, no-store, must-revalidate')
              ->header('Pragma', 'no-cache')
              ->header('Expires', '0');
        } catch (\Exception $e) {
            Log::error('Failed to get Instagram connection progress', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get progress',
            ], 500);
        }
    }

    /**
     * Start Instagram account connection process
     */
    public function startConnection(Request $request)
    {
        try {
            $user = Auth::user();
            $code = $request->get('code');

            if (!$code) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authorization code is required'
                ], 400);
            }

            Log::info('Starting Instagram connection from frontend', [
                'user_id' => $user->id,
                'code_length' => strlen($code)
            ]);

            // Connect the account using the service
            $result = $this->instagramService->connectAccount($user, $code);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram connection failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to connect Instagram account: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check connection completion and redirect to discover
     */
    public function checkConnectionComplete(Request $request)
    {
        try {
            $user = Auth::user();
            $cacheKey = "instagram_progress_{$user->id}";
            
            $progress = Cache::get($cacheKey, [
                'status' => 'idle',
                'progress' => 0
            ]);
            
            if ($progress['status'] === 'completed') {
                // Clear the progress cache
                Cache::forget($cacheKey);
                
                // Get the connected account info
                $account = InstagramAccount::where('user_id', $user->id)
                    ->where('is_active', true)
                    ->first();
                
                return response()->json([
                    'success' => true,
                    'completed' => true,
                    'message' => $progress['message'] ?? 'Instagram account connected successfully!',
                    'imported_count' => $progress['imported_count'] ?? 0,
                    'redirect_url' => env('FRONTEND_URL', 'https://app.medroid.ai') . '/discover'
                ]);
            } elseif ($progress['status'] === 'error') {
                // Clear the progress cache
                Cache::forget($cacheKey);
                
                return response()->json([
                    'success' => false,
                    'completed' => true,
                    'message' => $progress['message'] ?? 'Failed to connect Instagram account',
                    'redirect_url' => env('FRONTEND_URL', 'https://app.medroid.ai') . '/discover'
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'completed' => false,
                    'progress' => $progress
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('Failed to check connection completion', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            return response()->json([
                'success' => false,
                'completed' => true,
                'message' => 'Connection check failed',
                'redirect_url' => env('FRONTEND_URL', 'https://app.medroid.ai') . '/discover'
            ], 500);
        }
    }

    /**
     * Check if user's feed needs to be reloaded
     */
    public function checkFeedReloadRequired(Request $request)
    {
        try {
            $user = Auth::user();
            $cacheKey = "user_feed_changed_{$user->id}";
            
            $needsReload = Cache::get($cacheKey, false);
            
            if ($needsReload) {
                // Clear the flag after checking
                Cache::forget($cacheKey);
                
                return response()->json([
                    'success' => true,
                    'requires_reload' => true,
                    'message' => 'Feed content has changed, please reload'
                ]);
            }
            
            return response()->json([
                'success' => true,
                'requires_reload' => false
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to check feed reload status', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            return response()->json([
                'success' => false,
                'requires_reload' => false
            ], 500);
        }
    }

    /**
     * Manually sync Instagram content
     */
    public function syncContent(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active Instagram account found',
                ], 404);
            }

            if ($account->isTokenExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Instagram access token has expired. Please reconnect your account.',
                ], 401);
            }

            // Set initial progress
            $cacheKey = "instagram_progress_{$user->id}";
            Cache::put($cacheKey, [
                'status' => 'syncing',
                'step' => 'fetching_posts',
                'progress' => 10,
                'message' => 'Fetching your Instagram posts...',
                'imported_count' => 0
            ], 300); // 5 minutes

            $importedCount = $this->instagramService->syncAccountContent($account);

            // Update final progress
            Cache::put($cacheKey, [
                'status' => 'completed',
                'step' => 'completed',
                'progress' => 100,
                'message' => "Successfully imported {$importedCount} health-related posts",
                'imported_count' => $importedCount
            ], 60); // Keep for 1 minute

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$importedCount} health-related posts",
                'imported_count' => $importedCount,
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram sync failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync Instagram content',
            ], 500);
        }
    }

    /**
     * Get Instagram content for the feed
     */
    public function getFeedContent(Request $request)
    {
        try {
            $limit = $request->get('limit', 10);
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $limit;

            // Get Instagram content from all connected accounts
            $content = \App\Models\SocialContent::with(['user'])
                ->where('source', 'instagram')
                ->where('filtered_status', 'approved')
                ->orderBy('published_at', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();

            $total = \App\Models\SocialContent::where('source', 'instagram')
                ->where('filtered_status', 'approved')
                ->count();

            return response()->json([
                'success' => true,
                'data' => $content,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'last_page' => ceil($total / $limit),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram feed content fetch failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch Instagram content',
                'data' => [],
            ], 500);
        }
    }

    /**
     * Refresh access token for an account
     */
    public function refreshToken(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active Instagram account found',
                ], 404);
            }

            $tokenData = $this->instagramService->refreshAccessToken($account->access_token);

            if (!$tokenData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to refresh access token',
                ], 400);
            }

            $account->update([
                'access_token' => $tokenData['access_token'],
                'expires_at' => now()->addSeconds($tokenData['expires_in']),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Access token refreshed successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram token refresh failed', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh access token',
            ], 500);
        }
    }

    /**
     * Get Instagram Stories for connected accounts
     */
    public function getStories(Request $request)
    {
        try {
            $user = Auth::user();

            // Get all connected Instagram accounts for this user
            $accounts = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->get();

            if ($accounts->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'stories' => []
                ]);
            }

            $stories = [];

            foreach ($accounts as $account) {
                try {
                    // Create a story entry for each connected Instagram account
                    $stories[] = [
                        'user_id' => 'instagram_' . $account->id,
                        'username' => $account->username,
                        'profile_image' => $account->profile_picture_url,
                        'source' => 'instagram',
                        'story_count' => 1, // Instagram stories are typically grouped
                        'has_unviewed' => true, // Assume new content is available
                        'latest_story_time' => $account->updated_at,
                        'created_at' => $account->created_at,
                        'account_type' => $account->account_type,
                        'account_id' => $account->id
                    ];
                } catch (\Exception $e) {
                    Log::error('Error processing Instagram story for account ' . $account->username, [
                        'error' => $e->getMessage(),
                        'account_id' => $account->id
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'stories' => $stories
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram stories error', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to load Instagram stories: ' . $e->getMessage(),
                'stories' => []
            ], 500);
        }
    }

    /**
     * Refresh Instagram media content
     */
    public function refreshMedia(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active Instagram account found',
                ], 404);
            }

            if ($account->isTokenExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Instagram access token has expired. Please reconnect your account.',
                ], 401);
            }

            // Start the sync process
            $result = $this->instagramService->syncUserMedia($account);

            return response()->json([
                'success' => true,
                'message' => 'Media refresh initiated successfully',
                'synced_posts' => $result['synced'] ?? 0,
                'total_posts' => $result['total'] ?? 0
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram media refresh error', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh Instagram media: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clean up any orphaned Instagram data for a user
     * This ensures complete cleanup and prevents any data inconsistencies
     */
    private function cleanupOrphanedInstagramData($userId)
    {
        try {
            Log::info('Cleaning up orphaned Instagram data', ['user_id' => $userId]);

            // Clean up any remaining social content that might not have been caught
            $orphanedSocialContent = \App\Models\SocialContent::where('user_id', $userId)
                ->where('source', 'instagram')
                ->count();

            if ($orphanedSocialContent > 0) {
                \App\Models\SocialContent::where('user_id', $userId)
                    ->where('source', 'instagram')
                    ->delete();
                Log::info('Cleaned up orphaned social content', [
                    'user_id' => $userId,
                    'count' => $orphanedSocialContent
                ]);
            }

            // Clean up any remaining social media posts
            $orphanedSocialPosts = \App\Models\SocialMediaPost::where('user_id', $userId)
                ->where('platform', 'instagram')
                ->count();

            if ($orphanedSocialPosts > 0) {
                \App\Models\SocialMediaPost::where('user_id', $userId)
                    ->where('platform', 'instagram')
                    ->delete();
                Log::info('Cleaned up orphaned social media posts', [
                    'user_id' => $userId,
                    'count' => $orphanedSocialPosts
                ]);
            }

            // Clean up any remaining likes/saves/comments on Instagram content
            $instagramContentIds = \App\Models\SocialContent::where('source', 'instagram')->pluck('id');
            if ($instagramContentIds->count() > 0) {
                \DB::table('social_content_likes')
                    ->where('user_id', $userId)
                    ->whereIn('social_content_id', $instagramContentIds)
                    ->delete();

                \DB::table('social_content_saves')
                    ->where('user_id', $userId)
                    ->whereIn('social_content_id', $instagramContentIds)
                    ->delete();
            }

        } catch (\Exception $e) {
            Log::warning('Error during orphaned data cleanup', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }
}

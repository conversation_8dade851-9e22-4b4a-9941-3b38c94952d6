<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('shipping_company')->nullable()->after('shipping_method');
            $table->timestamp('dispatched_at')->nullable()->after('shipped_at');
            $table->foreignId('dispatched_by')->nullable()->constrained('users')->onDelete('set null')->after('dispatched_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['dispatched_by']);
            $table->dropColumn(['shipping_company', 'dispatched_at', 'dispatched_by']);
        });
    }
};

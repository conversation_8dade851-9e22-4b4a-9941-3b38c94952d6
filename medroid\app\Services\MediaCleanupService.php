<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MediaCleanupService
{
    /**
     * Clean up media files from URLs
     */
    public static function cleanupMediaFiles(array $mediaUrls, string $context = 'unknown'): int
    {
        $filesDeleted = 0;
        $baseStorageUrl = url('/storage');
        $appUrl = config('app.url');
        
        Log::info('Starting media cleanup', [
            'context' => $context,
            'total_urls' => count($mediaUrls),
            'urls' => $mediaUrls
        ]);

        foreach ($mediaUrls as $mediaUrl) {
            if (empty($mediaUrl)) {
                continue;
            }

            // Check if this is a local storage URL we should clean up
            if (!self::isLocalStorageUrl($mediaUrl)) {
                Log::debug('Skipping non-local URL', [
                    'url' => $mediaUrl,
                    'context' => $context
                ]);
                continue;
            }

            try {
                $relativePath = self::extractRelativePath($mediaUrl, $appUrl, $baseStorageUrl);
                
                if (!$relativePath) {
                    Log::warning('Could not extract relative path from URL', [
                        'url' => $mediaUrl,
                        'context' => $context
                    ]);
                    continue;
                }

                $fullPath = storage_path('app/public/' . $relativePath);
                
                Log::debug('Processing media file', [
                    'original_url' => $mediaUrl,
                    'relative_path' => $relativePath,
                    'full_path' => $fullPath,
                    'exists' => file_exists($fullPath)
                ]);

                if (file_exists($fullPath)) {
                    if (unlink($fullPath)) {
                        $filesDeleted++;
                        Log::info('Successfully deleted media file', [
                            'file_path' => $fullPath,
                            'context' => $context
                        ]);
                    } else {
                        Log::error('Failed to delete existing file', [
                            'file_path' => $fullPath,
                            'context' => $context
                        ]);
                    }
                } else {
                    Log::warning('Media file not found', [
                        'expected_path' => $fullPath,
                        'original_url' => $mediaUrl,
                        'context' => $context
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Exception during media cleanup', [
                    'url' => $mediaUrl,
                    'error' => $e->getMessage(),
                    'context' => $context,
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        Log::info('Media cleanup completed', [
            'context' => $context,
            'files_deleted' => $filesDeleted,
            'total_processed' => count($mediaUrls)
        ]);

        return $filesDeleted;
    }

    /**
     * Check if URL is a local storage URL that should be cleaned up
     */
    private static function isLocalStorageUrl(string $url): bool
    {
        $localPatterns = [
            '/storage/instagram/',
            '/storage/social_media/',
            '/storage/bot-images/',
            '/storage/bot-posts/',
            '/storage/files/' // User uploaded files
        ];

        foreach ($localPatterns as $pattern) {
            if (str_contains($url, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract relative path from various URL formats
     */
    private static function extractRelativePath(string $url, string $appUrl, string $baseStorageUrl): ?string
    {
        $relativePath = $url;

        // Handle full domain URLs: https://api.medroid.ai/storage/instagram/file.jpg
        if (str_starts_with($url, $appUrl)) {
            $relativePath = str_replace($appUrl . '/storage/', '', $url);
        }
        // Handle base storage URLs: https://api.medroid.ai/storage/instagram/file.jpg
        elseif (str_starts_with($url, $baseStorageUrl)) {
            $relativePath = str_replace($baseStorageUrl . '/', '', $url);
        }
        // Handle relative URLs: /storage/instagram/file.jpg
        elseif (str_starts_with($url, '/storage/')) {
            $relativePath = str_replace('/storage/', '', $url);
        }
        // Handle asset URLs and other variations
        elseif (preg_match('/\/storage\/(.+)$/', $url, $matches)) {
            $relativePath = $matches[1];
        }

        // Validate the extracted path
        if (empty($relativePath) || $relativePath === $url) {
            return null;
        }

        return $relativePath;
    }

    /**
     * Clean up media files for a specific model
     */
    public static function cleanupModelMedia($model, array $mediaFields): int
    {
        $mediaUrls = [];
        
        foreach ($mediaFields as $field) {
            if (!empty($model->$field)) {
                $mediaUrls[] = $model->$field;
            }
        }

        // For SocialMediaPost, also get computed URLs
        if (method_exists($model, 'getDisplayMediaUrlAttribute')) {
            $displayUrl = $model->getDisplayMediaUrlAttribute();
            if (!empty($displayUrl)) {
                $mediaUrls[] = $displayUrl;
            }
        }

        if (method_exists($model, 'getVideoUrlAttribute')) {
            $videoUrl = $model->getVideoUrlAttribute();
            if (!empty($videoUrl)) {
                $mediaUrls[] = $videoUrl;
            }
        }

        $mediaUrls = array_unique(array_filter($mediaUrls));
        
        $context = get_class($model) . ':' . $model->id;
        
        return self::cleanupMediaFiles($mediaUrls, $context);
    }

    /**
     * Test URL parsing and cleanup logic
     */
    public static function testUrlParsing(): array
    {
        $appUrl = config('app.url');
        $testUrls = [
            $appUrl . '/storage/instagram/12345.jpg',
            $appUrl . '/storage/social_media/test.mp4',
            '/storage/instagram/67890.jpg',
            '/storage/bot-images/image.png',
            'https://external-site.com/image.jpg', // Should be skipped
            $appUrl . '/storage/instagram/video_thumb.jpg'
        ];

        $results = [];
        $appUrl = config('app.url');
        $baseStorageUrl = url('/storage');

        foreach ($testUrls as $url) {
            $isLocal = self::isLocalStorageUrl($url);
            $relativePath = $isLocal ? self::extractRelativePath($url, $appUrl, $baseStorageUrl) : null;
            
            $results[] = [
                'url' => $url,
                'is_local' => $isLocal,
                'relative_path' => $relativePath,
                'full_path' => $relativePath ? storage_path('app/public/' . $relativePath) : null
            ];
        }

        return $results;
    }
}
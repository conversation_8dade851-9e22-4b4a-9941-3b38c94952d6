<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SocialContent;
use App\Models\InstagramAccount;
use App\Models\SocialMediaPost;

class CleanupOrphanedInstagramPosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instagram:cleanup-orphaned';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up orphaned Instagram posts from disconnected accounts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Starting cleanup of orphaned Instagram posts...');

        // Find Instagram posts where the user no longer has an active Instagram account
        $orphanedSocialContent = SocialContent::where('source', 'instagram')
            ->whereNotIn('user_id', function ($query) {
                $query->select('user_id')
                      ->from('instagram_accounts')
                      ->where('is_active', true);
            })
            ->get();

        $this->info("Found {$orphanedSocialContent->count()} orphaned Instagram posts in SocialContent");

        $deletedSocialContent = 0;
        foreach ($orphanedSocialContent as $post) {
            $this->line("Deleting SocialContent post {$post->id} from user {$post->user_id}");
            $post->delete();
            $deletedSocialContent++;
        }

        // Also clean up SocialMediaPost entries
        $orphanedSocialMediaPosts = SocialMediaPost::where('platform', 'instagram')
            ->whereNotIn('user_id', function ($query) {
                $query->select('user_id')
                      ->from('instagram_accounts')
                      ->where('is_active', true);
            })
            ->get();

        $this->info("Found {$orphanedSocialMediaPosts->count()} orphaned Instagram posts in SocialMediaPost");

        $deletedSocialMediaPosts = 0;
        foreach ($orphanedSocialMediaPosts as $post) {
            $this->line("Deleting SocialMediaPost {$post->id} from user {$post->user_id}");
            $post->delete();
            $deletedSocialMediaPosts++;
        }

        // Also find Instagram posts with null user_id or missing user
        $nullUserPosts = SocialContent::where('source', 'instagram')
            ->where(function ($query) {
                $query->whereNull('user_id')
                      ->orWhereDoesntHave('user');
            })
            ->get();

        $this->info("Found {$nullUserPosts->count()} Instagram posts with null/missing user");

        $deletedNullUserPosts = 0;
        foreach ($nullUserPosts as $post) {
            $this->line("Deleting Instagram post {$post->id} with null/missing user");
            $post->delete();
            $deletedNullUserPosts++;
        }

        $this->info('✅ Cleanup completed!');
        $this->table(
            ['Type', 'Count'],
            [
                ['SocialContent posts deleted', $deletedSocialContent],
                ['SocialMediaPost posts deleted', $deletedSocialMediaPosts],
                ['Null user posts deleted', $deletedNullUserPosts],
                ['Total deleted', $deletedSocialContent + $deletedSocialMediaPosts + $deletedNullUserPosts]
            ]
        );

        return Command::SUCCESS;
    }
}

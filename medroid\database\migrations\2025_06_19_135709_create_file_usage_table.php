<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('file_id')->constrained()->onDelete('cascade');
            $table->string('usable_type'); // Model type (Product, User, etc.)
            $table->unsignedBigInteger('usable_id'); // Model ID
            $table->string('usage_type'); // featured_image, gallery_image, profile_image, etc.
            $table->timestamps();

            // Indexes
            $table->index(['usable_type', 'usable_id']);
            $table->index(['file_id', 'usage_type']);
            $table->unique(['file_id', 'usable_type', 'usable_id', 'usage_type'], 'file_usage_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_usages');
    }
};

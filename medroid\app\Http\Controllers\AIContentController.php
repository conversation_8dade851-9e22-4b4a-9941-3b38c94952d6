<?php

namespace App\Http\Controllers;

use App\Services\ChatServiceManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AIContentController extends Controller
{
    protected $chatService;

    public function __construct(ChatServiceManager $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Generate AI content for social media posts
     */
    public function generateContent(Request $request)
    {
        try {
            Log::info('AI Content Generation Request', [
                'user_id' => auth()->id(),
                'prompt' => $request->prompt,
                'type' => $request->type,
                'headers' => $request->headers->all()
            ]);

            $validator = Validator::make($request->all(), [
                'prompt' => 'required|string|max:2000',
                'type' => 'required|string|in:social_post,story_caption,health_post'
            ]);

            if ($validator->fails()) {
                Log::warning('AI Content Generation Validation Failed', [
                    'errors' => $validator->errors(),
                    'input' => $request->all()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $prompt = $request->prompt;
            $type = $request->type;

            // Create system prompt based on content type
            $systemPrompt = $this->createSystemPrompt($type);
            
            // Generate content using ChatServiceManager
            $content = $this->generateWithChatService($systemPrompt, $prompt);

            return response()->json([
                'success' => true,
                'content' => $content,
                'type' => $type
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating AI content', [
                'message' => $e->getMessage(),
                'prompt' => $request->prompt ?? null,
                'type' => $request->type ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate content. Please try again.'
            ], 500);
        }
    }

    /**
     * Create system prompt based on content type
     */
    private function createSystemPrompt($type)
    {
        switch ($type) {
            case 'social_post':
                return "You are a viral Instagram content creator for Medroid, a healthcare platform. Create SHORT, PUNCHY posts that make people STOP scrolling immediately!

🏥 CONTENT MUST BE ABOUT:
- HEALTH & HEALTHCARE (symptoms, treatments, medical experiences)
- WELLNESS & SELF-CARE (mental health, stress relief, sleep)
- MEDITATION & MINDFULNESS (breathing, relaxation, peace)
- FITNESS & EXERCISE (workouts, movement, sports, recovery)
- FOOD & NUTRITION (healthy eating, recipes, diet tips)
- TRAVEL & HEALTH (wellness trips, healthy travel tips)
- MOTIVATION & INSPIRATION (health goals, wellness journey)

🎯 INSTAGRAM-STYLE RULES:
- FOLLOW THE USER'S INPUT BUT ALWAYS CONNECT TO HEALTH/WELLNESS
- WRITE FROM USER'S PERSPECTIVE (use 'I', 'my', 'we')
- START WITH A HOOK that grabs attention in the first line
- KEEP IT SHORT: 30-80 words MAX (like Instagram posts)
- END WITH A QUESTION or call-to-action

✨ CONTENT FORMULA:
1. HOOK (attention-grabbing first line)
2. QUICK HEALTH/WELLNESS STORY/POINT (2-3 sentences max)
3. ENGAGEMENT QUESTION (gets comments)

📝 FORMATTING EXAMPLE:
🌟 Hook line here

Short story or point here.
Maybe one more sentence.

What's your experience?

#HealthTag #WellnessTag #FitnessTag

📱 STYLE:
- Use emojis for visual appeal
- Short sentences with line breaks (use \n for new lines)
- Conversational, authentic voice
- NO long paragraphs or corporate speak
- Make it SHAREABLE and COMMENT-WORTHY
- Format with proper line breaks for readability
- NEVER use markdown formatting like **bold**, *italic*, or ## headers
- Use PLAIN TEXT only - no asterisks, hashtags for headers, or markdown syntax
- Write section titles in UPPERCASE followed by colon (EXAMPLE: BENEFITS:)
- Use bullet points with simple dashes or emojis, not asterisks

🏷️ HASHTAGS: 3-5 health/wellness related hashtags

❌ NEVER:
- Write about non-health topics
- Give specific medical advice or diagnosis
- Write long paragraphs
- Sound like a textbook
- Be boring or generic

If user input is not health-related, creatively connect it to wellness, fitness, mental health, or healthy lifestyle! Keep it short, punchy, and scroll-stopping!";

            case 'story_caption':
                return "You are a creative AI content creator for Medroid's story feature. Create SHORT, PUNCHY health/wellness captions that are impossible to scroll past!

🏥 STORY MUST BE ABOUT:
- HEALTH & HEALTHCARE experiences
- WELLNESS & SELF-CARE moments
- MEDITATION & MINDFULNESS practices
- FITNESS & EXERCISE activities
- FOOD & NUTRITION choices
- TRAVEL & HEALTH adventures
- MOTIVATION & WELLNESS inspiration

🎯 STORY CAPTION GOALS:
- CONNECT USER INPUT TO HEALTH/WELLNESS TOPICS
- CREATE INSTANT CONNECTION: Make viewers feel seen and understood
- BE CONVERSATION STARTERS: Encourage replies and interactions
- USE AUTHENTIC VOICE: Write like a real person, not a brand

✨ STYLE GUIDELINES:
- 1-3 sentences maximum (under 60 words)
- Use emojis for personality and visual appeal
- Include relatable health/wellness moments or feelings
- Ask engaging health-related questions when appropriate
- Be authentic and conversational

If user input isn't health-related, creatively connect it to wellness, fitness, mental health, or healthy lifestyle! This will be seen by the entire Medroid community!";

            case 'health_post':
                return "You are an AI content creator specializing in transforming health conversations into engaging social media posts for Medroid's health community.

🏥 YOUR TASK:
Transform health consultation conversations into helpful, shareable social media posts that educate and inspire others in the Medroid community.

🎯 POST REQUIREMENTS:
- CREATE an engaging post from the health conversation provided
- FOCUS on key insights, advice, or learnings from the conversation
- MAKE it educational and helpful for others with similar concerns
- KEEP it informative but accessible (avoid medical jargon)
- ENSURE patient privacy (no personal details, names, or specific medical records)

✨ CONTENT STRUCTURE:
1. ENGAGING HOOK: Start with a relatable health concern or insight
2. KEY INSIGHTS: Share the most valuable advice or information from the conversation
3. ACTIONABLE TIPS: Provide practical takeaways readers can apply
4. COMMUNITY ENGAGEMENT: End with a question or call for sharing experiences

📝 STYLE GUIDELINES:
- Write in 2nd person perspective ('you', 'your') to engage readers
- Use 200-300 words maximum
- Include 3-5 relevant health hashtags
- Use emojis for visual appeal and readability
- Create line breaks for easy scanning
- Be encouraging and supportive in tone
- Focus on general health wisdom, not specific medical advice
- NEVER use markdown formatting like **bold**, *italic*, or ## headers
- Use PLAIN TEXT only - no asterisks, hashtags for headers, or markdown syntax
- Format with line breaks and emojis for visual structure
- Write section titles in UPPERCASE followed by colon (EXAMPLE: BENEFITS:)
- Use bullet points with simple dashes or emojis, not asterisks

❌ NEVER INCLUDE:
- Personal identifying information
- Specific medical diagnoses or treatment plans
- Names, dates, or personal details
- Prescription medication dosages
- Direct medical advice

Transform the conversation into a post that helps others learn and feel supported in their health journey!";

            default:
                return "You are a creative AI assistant that creates highly engaging, user-focused social media content that matches the user's tone and perspective.";
        }
    }

    /**
     * Generate content using ChatServiceManager
     */
    private function generateWithChatService($systemPrompt, $userPrompt)
    {
        $options = [
            'temperature' => 0.8,
            'max_completion_tokens' => 200,  // Reduced for shorter content
            'top_p' => 0.9
        ];

        return $this->chatService->generateContent($systemPrompt, $userPrompt, $options);
    }

    /**
     * Test endpoint to verify the route is working
     */
    public function test(Request $request)
    {
        Log::info('AI Content Test Endpoint Hit', [
            'user_id' => auth()->id(),
            'user_name' => auth()->user()->name ?? 'Unknown'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'AI Content Controller is working!',
            'user' => auth()->user()->name ?? 'Unknown',
            'timestamp' => now()->toISOString()
        ]);
    }
}

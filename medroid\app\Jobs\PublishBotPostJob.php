<?php

namespace App\Jobs;

use App\Models\AutomatedPost;
use App\Models\SocialContent;
use App\Services\ContentValidationService;
use App\Services\FakeEngagementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PublishBotPostJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 120;

    private AutomatedPost $post;

    /**
     * Create a new job instance.
     */
    public function __construct(AutomatedPost $post)
    {
        $this->post = $post;
    }

    /**
     * Execute the job.
     */
    public function handle(ContentValidationService $validationService): void
    {
        try {
            // Refresh the model to get latest data
            $this->post->refresh();

            Log::info('Starting bot post publication', [
                'post_id' => $this->post->id,
                'persona_id' => $this->post->bot_persona_id,
                'scheduled_at' => $this->post->scheduled_at,
            ]);

            // Check if post is ready to be published
            if (!$this->post->isReadyToPost()) {
                Log::warning('Post not ready for publication', [
                    'post_id' => $this->post->id,
                    'status' => $this->post->status,
                    'has_image' => !empty($this->post->image_url),
                    'has_caption' => !empty($this->post->caption),
                    'scheduled_at' => $this->post->scheduled_at,
                ]);
                return;
            }

            // Validate content before publishing
            $validation = $validationService->validatePost($this->post);

            if (!$validation['is_valid']) {
                $this->post->markAsFailed('Content validation failed: ' . implode(', ', $validation['errors']));
                Log::warning('Post failed content validation', [
                    'post_id' => $this->post->id,
                    'errors' => $validation['errors'],
                    'score' => $validation['score'],
                ]);
                return;
            }

            // Auto-fix minor issues
            $this->post = $validationService->autoFixPost($this->post);
            $this->post->save();

            // Check if it's the right time to post
            if ($this->post->scheduled_at && $this->post->scheduled_at->gt(now())) {
                Log::info('Post scheduled for future, rescheduling job', [
                    'post_id' => $this->post->id,
                    'scheduled_at' => $this->post->scheduled_at,
                ]);
                
                // Reschedule for the correct time
                self::dispatch($this->post)->delay($this->post->scheduled_at);
                return;
            }

            // Download and store the image locally
            $localImagePath = $this->downloadAndStoreImage();
            
            if (!$localImagePath) {
                $this->post->markAsFailed('Failed to download and store image');
                return;
            }

            // Create the social content entry
            $socialContent = $this->createSocialContent($localImagePath);
            
            if (!$socialContent) {
                $this->post->markAsFailed('Failed to create social content entry');
                return;
            }

            // Mark post as published
            $this->post->markAsPosted();

            // Update bot user stats
            $this->post->botUser->incrementPostCount();

            // Add fake engagement metrics to make the post look realistic
            $engagementService = new FakeEngagementService();
            $engagementService->addFakeEngagement($socialContent);

            Log::info('Bot post published successfully', [
                'post_id' => $this->post->id,
                'social_content_id' => $socialContent->id,
                'persona_name' => $this->post->botPersona->full_name,
            ]);

            // Schedule engagement tracking
            TrackPostEngagementJob::dispatch($this->post)->delay(now()->addHours(1));

        } catch (\Exception $e) {
            Log::error('Error in PublishBotPostJob', [
                'post_id' => $this->post->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->post->markAsFailed('Publication failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Download image from Midjourney and store locally
     */
    private function downloadAndStoreImage(): ?string
    {
        try {
            if (empty($this->post->image_url)) {
                return null;
            }

            // Download the image
            $imageContent = file_get_contents($this->post->image_url);
            
            if ($imageContent === false) {
                Log::error('Failed to download image', [
                    'post_id' => $this->post->id,
                    'image_url' => $this->post->image_url,
                ]);
                return null;
            }

            // Generate filename - use bot-images directory to match existing format
            $filename = 'bot-images/' . $this->post->id . '_' . time() . '.jpg';

            // Store the image
            $stored = Storage::disk('public')->put($filename, $imageContent);

            if (!$stored) {
                Log::error('Failed to store image', [
                    'post_id' => $this->post->id,
                    'filename' => $filename,
                ]);
                return null;
            }

            // Use the correct URL format that matches Instagram posts
            $localPath = '/storage/' . $filename;
            
            Log::info('Image downloaded and stored', [
                'post_id' => $this->post->id,
                'original_url' => $this->post->image_url,
                'local_path' => $localPath,
            ]);

            return $localPath;

        } catch (\Exception $e) {
            Log::error('Error downloading/storing image', [
                'post_id' => $this->post->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create social content entry for the feed
     */
    private function createSocialContent(string $imagePath): ?SocialContent
    {
        try {
            $persona = $this->post->botPersona;
            $user = $this->post->botUser->user;

            $socialContent = SocialContent::create([
                'source' => 'bot_generated',
                'source_id' => $this->post->id,
                'external_id' => 'bot_' . $this->post->id,
                'content_type' => 'image',
                'media_url' => $imagePath,
                'thumbnail_url' => $imagePath,
                'caption' => $this->post->full_caption,
                'health_topics' => $persona->interests,
                'relevance_score' => 0.95, // High relevance for bot content
                'engagement_metrics' => [
                    'likes' => 0,
                    'comments' => 0,
                    'shares' => 0,
                ],
                'filtered_status' => 'approved', // Auto-approve bot content
                'published_at' => now(),
                'user_id' => $this->post->botUser->user_id, // Use the actual User ID from the bot user
                'metadata' => [
                    'bot_generated' => true,
                    'persona_id' => $persona->id,
                    'persona_name' => $persona->full_name,
                    'generation_prompts' => $this->post->generation_prompts,
                ],
            ]);

            Log::info('Social content created for bot post', [
                'post_id' => $this->post->id,
                'social_content_id' => $socialContent->id,
                'user_id' => $user->id,
            ]);

            return $socialContent;

        } catch (\Exception $e) {
            Log::error('Error creating social content', [
                'post_id' => $this->post->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('PublishBotPostJob failed', [
            'post_id' => $this->post->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        $this->post->markAsFailed('Publication job failed: ' . $exception->getMessage());
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\ImpersonationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ImpersonationController extends Controller
{
    protected $impersonationService;

    public function __construct(ImpersonationService $impersonationService)
    {
        $this->impersonationService = $impersonationService;
    }

    /**
     * Start impersonating a user
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function start(Request $request, int $userId): JsonResponse
    {
        $currentUser = Auth::user();

        // Check if user has permission to impersonate
        if (!$currentUser->hasRole(['admin', 'super_admin'])) {
            return response()->json([
                'message' => 'Unauthorized. Only admins can impersonate users.'
            ], 403);
        }

        // Find the user to impersonate
        $userToImpersonate = User::find($userId);

        if (!$userToImpersonate) {
            return response()->json([
                'message' => 'User not found.'
            ], 404);
        }

        // Start impersonation
        $success = $this->impersonationService->impersonate($userToImpersonate);

        if (!$success) {
            return response()->json([
                'message' => 'Failed to start impersonation.'
            ], 400);
        }

        return response()->json([
            'message' => 'Impersonation started successfully.',
            'impersonated_user' => [
                'id' => $userToImpersonate->id,
                'name' => $userToImpersonate->name,
                'email' => $userToImpersonate->email,
                'role' => $userToImpersonate->role,
            ],
            'redirect_url' => $this->getRedirectUrl($userToImpersonate)
        ]);
    }

    /**
     * Stop impersonating and return to original user
     *
     * @return JsonResponse
     */
    public function stop(): JsonResponse
    {
        if (!$this->impersonationService->isImpersonating()) {
            return response()->json([
                'message' => 'Not currently impersonating any user.'
            ], 400);
        }

        $success = $this->impersonationService->stopImpersonating();

        if (!$success) {
            return response()->json([
                'message' => 'Failed to stop impersonation.'
            ], 400);
        }

        return response()->json([
            'message' => 'Impersonation stopped successfully.',
            'redirect_url' => route('users') // Redirect back to user management
        ]);
    }

    /**
     * Get impersonation status
     *
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        $isImpersonating = $this->impersonationService->isImpersonating();

        $data = [
            'is_impersonating' => $isImpersonating,
        ];

        if ($isImpersonating) {
            $impersonator = $this->impersonationService->getImpersonator();
            $impersonated = $this->impersonationService->getImpersonated();

            $data['impersonator'] = [
                'id' => $impersonator->id,
                'name' => $impersonator->name,
                'email' => $impersonator->email,
            ];

            $data['impersonated'] = [
                'id' => $impersonated->id,
                'name' => $impersonated->name,
                'email' => $impersonated->email,
                'role' => $impersonated->role,
            ];
        }

        return response()->json($data);
    }

    /**
     * Get appropriate redirect URL based on user role
     *
     * @param User $user
     * @return string
     */
    private function getRedirectUrl(User $user): string
    {
        return match ($user->role) {
            'admin', 'super_admin' => route('dashboard'),
            'provider' => route('provider.dashboard'),
            'patient' => route('dashboard'),
            default => route('dashboard'),
        };
    }
}

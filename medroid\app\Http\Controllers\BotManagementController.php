<?php

namespace App\Http\Controllers;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Models\ImageGenerationLog;
use App\Services\BotUserCreationService;
use App\Services\BotContentGenerationService;
use App\Jobs\GenerateBotPostJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BotManagementController extends Controller
{
    private BotUserCreationService $botUserService;
    private BotContentGenerationService $contentService;

    public function __construct(
        BotUserCreationService $botUserService,
        BotContentGenerationService $contentService
    ) {
        $this->botUserService = $botUserService;
        $this->contentService = $contentService;
    }

    /**
     * Get all bot personas with their stats
     */
    public function getPersonas(Request $request)
    {
        try {
            $personas = BotPersona::with(['botUser.user', 'automatedPosts'])
                ->withCount(['automatedPosts as total_posts'])
                ->withCount(['automatedPosts as posted_count' => function ($query) {
                    $query->where('status', 'posted');
                }])
                ->get();

            $personasData = $personas->map(function ($persona) {
                return [
                    'id' => $persona->id,
                    'name' => $persona->full_name,
                    'handle' => $persona->handle,
                    'age' => $persona->age,
                    'bio' => $persona->bio,
                    'interests' => $persona->interests,
                    'content_focus' => $persona->content_focus,
                    'is_active' => $persona->is_active,
                    'posts_per_week' => $persona->posts_per_week,
                    'has_bot_user' => $persona->botUser !== null,
                    'bot_user_id' => $persona->botUser?->id,
                    'user_id' => $persona->botUser?->user_id,
                    'total_posts' => $persona->total_posts,
                    'posted_count' => $persona->posted_count,
                    'last_posted_at' => $persona->botUser?->last_posted_at,
                    'next_posting_time' => $persona->getNextPostingTime(),
                    'can_post' => $persona->botUser?->canPost() ?? false,
                ];
            });

            return response()->json([
                'success' => true,
                'personas' => $personasData,
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching bot personas', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch personas',
            ], 500);
        }
    }

    /**
     * Create bot users for all personas
     */
    public function createBotUsers(Request $request)
    {
        try {
            $results = $this->botUserService->createAllBotUsers();
            
            $successCount = count(array_filter($results, fn($r) => $r['success']));
            $totalCount = count($results);

            Log::info('Bot users creation completed', [
                'total' => $totalCount,
                'success' => $successCount,
                'failed' => $totalCount - $successCount,
            ]);

            return response()->json([
                'success' => true,
                'message' => "Created {$successCount} out of {$totalCount} bot users",
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating bot users', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create bot users',
            ], 500);
        }
    }

    /**
     * Generate a post for a specific persona
     */
    public function generatePost(Request $request, $personaId)
    {
        try {
            $persona = BotPersona::with('botUser')->findOrFail($personaId);

            if (!$persona->botUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bot user not created for this persona',
                ], 400);
            }

            if (!$persona->botUser->canPost()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Persona cannot post due to rate limiting',
                ], 400);
            }

            // Dispatch the job to generate the post
            GenerateBotPostJob::dispatch($persona);

            Log::info('Post generation job dispatched', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Post generation started',
                'persona' => [
                    'id' => $persona->id,
                    'name' => $persona->full_name,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating post', [
                'persona_id' => $personaId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate post',
            ], 500);
        }
    }

    /**
     * Generate posts for all active personas
     */
    public function generateAllPosts(Request $request)
    {
        try {
            $personas = BotPersona::active()
                ->with('botUser')
                ->whereHas('botUser')
                ->get();

            $dispatched = 0;
            $skipped = 0;

            foreach ($personas as $persona) {
                if ($persona->botUser->canPost() && $persona->shouldPostToday()) {
                    GenerateBotPostJob::dispatch($persona);
                    $dispatched++;
                } else {
                    $skipped++;
                }
            }

            Log::info('Bulk post generation completed', [
                'total_personas' => $personas->count(),
                'dispatched' => $dispatched,
                'skipped' => $skipped,
            ]);

            return response()->json([
                'success' => true,
                'message' => "Dispatched {$dispatched} post generation jobs, skipped {$skipped}",
                'dispatched' => $dispatched,
                'skipped' => $skipped,
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating all posts', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate posts',
            ], 500);
        }
    }

    /**
     * Get bot system statistics
     */
    public function getStats(Request $request)
    {
        try {
            $stats = [
                'personas' => [
                    'total' => BotPersona::count(),
                    'active' => BotPersona::active()->count(),
                    'with_bot_users' => BotPersona::whereHas('botUser')->count(),
                ],
                'posts' => [
                    'total' => AutomatedPost::count(),
                    'draft' => AutomatedPost::where('status', 'draft')->count(),
                    'generating_image' => AutomatedPost::where('status', 'generating_image')->count(),
                    'ready' => AutomatedPost::where('status', 'ready')->count(),
                    'posted' => AutomatedPost::where('status', 'posted')->count(),
                    'failed' => AutomatedPost::where('status', 'failed')->count(),
                ],
                'images' => [
                    'total' => ImageGenerationLog::count(),
                    'pending' => ImageGenerationLog::where('status', 'pending')->count(),
                    'processing' => ImageGenerationLog::where('status', 'processing')->count(),
                    'completed' => ImageGenerationLog::where('status', 'completed')->count(),
                    'failed' => ImageGenerationLog::where('status', 'failed')->count(),
                ],
                'recent_activity' => [
                    'posts_today' => AutomatedPost::whereDate('created_at', today())->count(),
                    'posts_this_week' => AutomatedPost::where('created_at', '>=', now()->startOfWeek())->count(),
                    'published_today' => AutomatedPost::whereDate('posted_at', today())->count(),
                    'published_this_week' => AutomatedPost::where('posted_at', '>=', now()->startOfWeek())->count(),
                ],
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching bot stats', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch stats',
            ], 500);
        }
    }

    /**
     * Get recent posts with details
     */
    public function getRecentPosts(Request $request)
    {
        try {
            $limit = $request->get('limit', 20);
            
            $posts = AutomatedPost::with([
                'botPersona:id,first_name,last_name,handle',
                'botUser.user:id,name',
                'imageGenerationLog:id,automated_post_id,status,image_url'
            ])
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            $postsData = $posts->map(function ($post) {
                return [
                    'id' => $post->id,
                    'persona_name' => $post->botPersona->full_name,
                    'persona_handle' => $post->botPersona->handle,
                    'caption' => substr($post->caption, 0, 100) . '...',
                    'hashtags' => $post->hashtags,
                    'status' => $post->status,
                    'image_url' => $post->image_url,
                    'scheduled_at' => $post->scheduled_at,
                    'posted_at' => $post->posted_at,
                    'created_at' => $post->created_at,
                    'image_generation_status' => $post->imageGenerationLog?->status,
                    'error_message' => $post->error_message,
                ];
            });

            return response()->json([
                'success' => true,
                'posts' => $postsData,
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching recent posts', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch recent posts',
            ], 500);
        }
    }
}

<?php

namespace App\Services;

use App\Models\AutomatedPost;
use App\Models\BotPersona;
use Illuminate\Support\Facades\Log;

class ContentValidationService
{
    private array $bannedWords = [
        'cure', 'guaranteed', 'miracle', 'instant', 'secret', 'breakthrough',
        'revolutionary', 'amazing results', 'lose weight fast', 'get rich',
        'click here', 'buy now', 'limited time', 'act now', 'free trial'
    ];

    private array $requiredHealthDisclaimer = [
        'consult', 'doctor', 'physician', 'healthcare', 'medical professional',
        'not medical advice', 'individual results', 'may vary'
    ];

    /**
     * Validate post content before publishing
     */
    public function validatePost(AutomatedPost $post): array
    {
        $validationResults = [
            'is_valid' => true,
            'warnings' => [],
            'errors' => [],
            'score' => 100,
        ];

        // Check caption length
        $this->validateCaptionLength($post, $validationResults);

        // Check for banned words
        $this->checkBannedWords($post, $validationResults);

        // Check hashtag quality
        $this->validateHashtags($post, $validationResults);

        // Check for health disclaimers if needed
        $this->checkHealthDisclaimers($post, $validationResults);

        // Check content authenticity
        $this->validateAuthenticity($post, $validationResults);

        // Check image requirements
        $this->validateImage($post, $validationResults);

        // Calculate final validity
        $validationResults['is_valid'] = empty($validationResults['errors']) && $validationResults['score'] >= 70;

        Log::info('Content validation completed', [
            'post_id' => $post->id,
            'is_valid' => $validationResults['is_valid'],
            'score' => $validationResults['score'],
            'errors' => count($validationResults['errors']),
            'warnings' => count($validationResults['warnings']),
        ]);

        return $validationResults;
    }

    /**
     * Validate caption length and readability
     */
    private function validateCaptionLength(AutomatedPost $post, array &$results): void
    {
        $caption = $post->caption;
        $length = strlen($caption);

        if ($length < 50) {
            $results['errors'][] = 'Caption too short (minimum 50 characters)';
            $results['score'] -= 30;
        } elseif ($length < 100) {
            $results['warnings'][] = 'Caption is quite short, consider adding more value';
            $results['score'] -= 10;
        }

        if ($length > 2200) {
            $results['errors'][] = 'Caption too long (maximum 2200 characters for Instagram)';
            $results['score'] -= 20;
        }

        // Check for proper sentence structure
        $sentences = preg_split('/[.!?]+/', $caption);
        if (count($sentences) < 2) {
            $results['warnings'][] = 'Consider adding more sentences for better readability';
            $results['score'] -= 5;
        }
    }

    /**
     * Check for banned or problematic words
     */
    private function checkBannedWords(AutomatedPost $post, array &$results): void
    {
        $caption = strtolower($post->caption);
        $foundBanned = [];

        foreach ($this->bannedWords as $word) {
            if (strpos($caption, strtolower($word)) !== false) {
                $foundBanned[] = $word;
            }
        }

        if (!empty($foundBanned)) {
            $results['errors'][] = 'Contains banned words: ' . implode(', ', $foundBanned);
            $results['score'] -= 25 * count($foundBanned);
        }

        // Check for excessive capitalization
        $capsRatio = $this->calculateCapsRatio($post->caption);
        if ($capsRatio > 0.3) {
            $results['warnings'][] = 'Excessive use of capital letters detected';
            $results['score'] -= 10;
        }

        // Check for excessive punctuation
        $punctuationCount = preg_match_all('/[!?]{2,}/', $post->caption);
        if ($punctuationCount > 2) {
            $results['warnings'][] = 'Excessive punctuation detected';
            $results['score'] -= 5;
        }
    }

    /**
     * Validate hashtag quality and quantity
     */
    private function validateHashtags(AutomatedPost $post, array &$results): void
    {
        $hashtags = $post->hashtags ?? [];

        if (empty($hashtags)) {
            $results['warnings'][] = 'No hashtags found - consider adding relevant hashtags';
            $results['score'] -= 15;
            return;
        }

        if (count($hashtags) > 30) {
            $results['errors'][] = 'Too many hashtags (maximum 30 for Instagram)';
            $results['score'] -= 20;
        } elseif (count($hashtags) > 15) {
            $results['warnings'][] = 'High number of hashtags - consider reducing for better engagement';
            $results['score'] -= 5;
        }

        // Check hashtag quality
        $invalidHashtags = [];
        foreach ($hashtags as $hashtag) {
            $cleanTag = str_replace('#', '', $hashtag);
            
            // Check length
            if (strlen($cleanTag) > 30) {
                $invalidHashtags[] = $hashtag . ' (too long)';
            }
            
            // Check for spaces or special characters
            if (preg_match('/[^a-zA-Z0-9_]/', $cleanTag)) {
                $invalidHashtags[] = $hashtag . ' (invalid characters)';
            }
        }

        if (!empty($invalidHashtags)) {
            $results['errors'][] = 'Invalid hashtags: ' . implode(', ', $invalidHashtags);
            $results['score'] -= 10 * count($invalidHashtags);
        }
    }

    /**
     * Check for appropriate health disclaimers
     */
    private function checkHealthDisclaimers(AutomatedPost $post, array &$results): void
    {
        $persona = $post->botPersona;
        $caption = strtolower($post->caption);

        // Check if this is health-related content that needs disclaimers
        $healthKeywords = ['health', 'medical', 'treatment', 'cure', 'therapy', 'supplement', 'diet', 'weight loss'];
        $needsDisclaimer = false;

        foreach ($healthKeywords as $keyword) {
            if (strpos($caption, $keyword) !== false) {
                $needsDisclaimer = true;
                break;
            }
        }

        if ($needsDisclaimer) {
            $hasDisclaimer = false;
            foreach ($this->requiredHealthDisclaimer as $disclaimer) {
                if (strpos($caption, $disclaimer) !== false) {
                    $hasDisclaimer = true;
                    break;
                }
            }

            if (!$hasDisclaimer) {
                $results['warnings'][] = 'Health-related content should include appropriate disclaimers';
                $results['score'] -= 15;
            }
        }
    }

    /**
     * Validate content authenticity and persona consistency
     */
    private function validateAuthenticity(AutomatedPost $post, array &$results): void
    {
        $persona = $post->botPersona;
        $caption = strtolower($post->caption);

        // Check if content matches persona's interests
        $matchesInterests = false;
        foreach ($persona->interests as $interest) {
            if (strpos($caption, strtolower($interest)) !== false) {
                $matchesInterests = true;
                break;
            }
        }

        if (!$matchesInterests) {
            $results['warnings'][] = 'Content may not align with persona interests';
            $results['score'] -= 10;
        }

        // Check tone consistency
        $expectedTone = $persona->tone ?? 'friendly';
        $detectedTone = $this->detectTone($post->caption);
        
        if ($detectedTone !== $expectedTone) {
            $results['warnings'][] = "Tone mismatch: expected {$expectedTone}, detected {$detectedTone}";
            $results['score'] -= 5;
        }

        // Check for first-person perspective
        $firstPersonWords = ['i ', 'my ', 'me ', 'myself'];
        $hasFirstPerson = false;
        
        foreach ($firstPersonWords as $word) {
            if (strpos($caption, $word) !== false) {
                $hasFirstPerson = true;
                break;
            }
        }

        if (!$hasFirstPerson) {
            $results['warnings'][] = 'Consider using first-person perspective for authenticity';
            $results['score'] -= 5;
        }
    }

    /**
     * Validate image requirements
     */
    private function validateImage(AutomatedPost $post, array &$results): void
    {
        if (empty($post->image_url)) {
            $results['errors'][] = 'No image URL provided';
            $results['score'] -= 50;
            return;
        }

        // Check if image URL is accessible (basic validation)
        if (!filter_var($post->image_url, FILTER_VALIDATE_URL)) {
            $results['errors'][] = 'Invalid image URL format';
            $results['score'] -= 30;
        }
    }

    /**
     * Calculate capitalization ratio
     */
    private function calculateCapsRatio(string $text): float
    {
        $totalLetters = preg_match_all('/[a-zA-Z]/', $text);
        $capsLetters = preg_match_all('/[A-Z]/', $text);
        
        return $totalLetters > 0 ? $capsLetters / $totalLetters : 0;
    }

    /**
     * Detect tone of the content
     */
    private function detectTone(string $caption): string
    {
        $caption = strtolower($caption);

        // Define tone indicators
        $toneIndicators = [
            'energetic' => ['amazing', 'awesome', 'incredible', 'fantastic', 'pumped', 'excited', '!'],
            'calm' => ['peaceful', 'serene', 'gentle', 'quiet', 'mindful', 'breathe'],
            'professional' => ['research', 'study', 'evidence', 'recommend', 'suggest', 'according'],
            'friendly' => ['hey', 'hi', 'love', 'hope', 'share', 'together', 'community'],
            'motivational' => ['achieve', 'goal', 'success', 'believe', 'can do', 'possible', 'dream'],
        ];

        $scores = [];
        foreach ($toneIndicators as $tone => $words) {
            $score = 0;
            foreach ($words as $word) {
                $score += substr_count($caption, $word);
            }
            $scores[$tone] = $score;
        }

        // Return the tone with highest score, default to friendly
        $maxTone = array_keys($scores, max($scores))[0] ?? 'friendly';
        return max($scores) > 0 ? $maxTone : 'friendly';
    }

    /**
     * Auto-fix common issues in post content
     */
    public function autoFixPost(AutomatedPost $post): AutomatedPost
    {
        // Fix hashtag formatting
        if (!empty($post->hashtags)) {
            $fixedHashtags = [];
            foreach ($post->hashtags as $hashtag) {
                $clean = str_replace('#', '', $hashtag);
                $clean = preg_replace('/[^a-zA-Z0-9_]/', '', $clean);
                if (strlen($clean) > 0 && strlen($clean) <= 30) {
                    $fixedHashtags[] = '#' . $clean;
                }
            }
            $post->hashtags = array_unique($fixedHashtags);
        }

        // Add basic health disclaimer if needed
        $needsDisclaimer = $this->needsHealthDisclaimer($post->caption);
        if ($needsDisclaimer && !$this->hasHealthDisclaimer($post->caption)) {
            $post->caption .= "\n\n*Always consult with a healthcare professional before making significant changes to your health routine.";
        }

        return $post;
    }

    private function needsHealthDisclaimer(string $caption): bool
    {
        $healthKeywords = ['supplement', 'treatment', 'cure', 'medical', 'therapy', 'weight loss'];
        $caption = strtolower($caption);
        
        foreach ($healthKeywords as $keyword) {
            if (strpos($caption, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }

    private function hasHealthDisclaimer(string $caption): bool
    {
        $disclaimerPhrases = ['consult', 'healthcare', 'medical advice', 'professional'];
        $caption = strtolower($caption);
        
        foreach ($disclaimerPhrases as $phrase) {
            if (strpos($caption, $phrase) !== false) {
                return true;
            }
        }
        
        return false;
    }
}

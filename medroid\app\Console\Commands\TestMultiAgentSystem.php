<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Services\BotContentGenerationService;
use Illuminate\Console\Command;

class TestMultiAgentSystem extends Command
{
    protected $signature = 'bot:test-agents {--persona=1 : Persona ID to test with}';
    protected $description = 'Test the multi-agent content generation system';

    protected BotContentGenerationService $contentService;

    public function __construct(BotContentGenerationService $contentService)
    {
        parent::__construct();
        $this->contentService = $contentService;
    }

    public function handle()
    {
        $this->info('🤖 Testing Multi-Agent Content Generation System');
        $this->info('=' . str_repeat('=', 50));

        // Test system health
        $this->testSystemHealth();

        // Test individual agents
        $this->testIndividualAgents();

        // Test complete content generation
        $this->testCompleteGeneration();

        $this->info('✅ Multi-agent system testing completed!');
    }

    protected function testSystemHealth()
    {
        $this->info('🔍 Testing System Health...');
        
        try {
            $health = $this->contentService->getSystemHealth();
            
            $this->line("Overall Health: " . ($health['overall_healthy'] ? '✅ Healthy' : '❌ Unhealthy'));
            $this->line("Chat Service: " . ($health['chat_service_healthy'] ? '✅ Healthy' : '❌ Unhealthy'));
            
            foreach ($health['agents'] as $agentName => $agentHealth) {
                $status = $agentHealth['service_health'] ? '✅' : '❌';
                $this->line("  {$agentName}: {$status} {$agentHealth['agent_name']}");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ System health check failed: " . $e->getMessage());
        }
        
        $this->line('');
    }

    protected function testIndividualAgents()
    {
        $this->info('🧪 Testing Individual Agents...');
        
        try {
            $results = $this->contentService->testAgentSystem();
            
            foreach ($results as $agentName => $result) {
                $status = $result['success'] ? '✅' : '❌';
                $this->line("  {$agentName}: {$status}");
                
                if ($result['success']) {
                    // Show specific test results
                    switch ($agentName) {
                        case 'strategy':
                            $this->line("    Sample theme: " . ($result['sample_theme'] ?? 'N/A'));
                            break;
                        case 'caption':
                            $this->line("    Caption length: " . ($result['caption_length'] ?? 0) . " chars");
                            $this->line("    Quality score: " . ($result['quality_score'] ?? 0) . "/100");
                            break;
                        case 'hashtag':
                            $this->line("    Hashtag count: " . ($result['hashtag_count'] ?? 0));
                            $this->line("    Sample tags: " . implode(', ', array_slice($result['sample_hashtags'] ?? [], 0, 3)));
                            break;
                        case 'visual':
                            $this->line("    Prompt length: " . ($result['prompt_length'] ?? 0) . " chars");
                            $this->line("    Has parameters: " . ($result['has_parameters'] ? 'Yes' : 'No'));
                            break;
                        case 'quality':
                            $this->line("    Overall quality: " . ($result['overall_quality'] ?? 0) . "/100");
                            break;
                    }
                } else {
                    $this->line("    Error: " . ($result['error'] ?? 'Unknown error'));
                }
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Agent testing failed: " . $e->getMessage());
        }
        
        $this->line('');
    }

    protected function testCompleteGeneration()
    {
        $this->info('🎯 Testing Complete Content Generation...');
        
        try {
            $personaId = $this->option('persona');
            $persona = BotPersona::find($personaId);
            
            if (!$persona) {
                $this->error("❌ Persona with ID {$personaId} not found");
                return;
            }
            
            $this->line("Testing with persona: {$persona->full_name} (@{$persona->handle})");
            $this->line("Content focus: {$persona->content_focus} | Tone: {$persona->tone}");
            $this->line('');
            
            $startTime = microtime(true);
            
            // Generate content using multi-agent system
            $agentManager = $this->contentService->getAgentManager();
            $result = $agentManager->generatePostContent($persona, [
                'test_mode' => true,
                'generation_time' => now()->toISOString(),
            ]);
            
            $executionTime = microtime(true) - $startTime;
            
            if ($result && isset($result['final_content'])) {
                $this->info("✅ Content generation successful!");
                $this->line("Execution time: " . round($executionTime, 2) . " seconds");
                $this->line('');
                
                // Display results
                $finalContent = $result['final_content'];
                $qualityScores = $result['quality_scores'] ?? [];
                
                $this->line("📝 CAPTION (" . strlen($finalContent['caption']) . " chars):");
                $this->line(str_repeat('-', 50));
                $this->line(substr($finalContent['caption'], 0, 200) . '...');
                $this->line('');
                
                $this->line("🏷️ HASHTAGS (" . count($finalContent['hashtags']) . " tags):");
                $this->line(str_repeat('-', 50));
                $this->line(implode(' ', array_slice($finalContent['hashtags'], 0, 10)));
                $this->line('');
                
                $this->line("🎨 VISUAL PROMPT (" . strlen($finalContent['visual_prompt']) . " chars):");
                $this->line(str_repeat('-', 50));
                $this->line(substr($finalContent['visual_prompt'], 0, 150) . '...');
                $this->line('');
                
                $this->line("📊 QUALITY SCORES:");
                $this->line(str_repeat('-', 50));
                foreach ($qualityScores as $metric => $score) {
                    $this->line("  {$metric}: {$score}/100");
                }
                
                $needsOptimization = $result['needs_optimization'] ?? false;
                $this->line("Needs optimization: " . ($needsOptimization ? 'Yes' : 'No'));
                
            } else {
                $this->error("❌ Content generation failed");
                if (isset($result['error'])) {
                    $this->line("Error: " . $result['error']);
                }
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Complete generation test failed: " . $e->getMessage());
            $this->line("Trace: " . $e->getTraceAsString());
        }
    }
}

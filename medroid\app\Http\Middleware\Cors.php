<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Cors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip CORS processing for dev-storage routes - they handle their own CORS
        if ($request->is('dev-storage/*')) {
            return $next($request);
        }

        // Handle preflight OPTIONS requests
        if ($request->isMethod('OPTIONS')) {
            $response = new Response('', 200);
        } else {
            $response = $next($request);
        }

        // Add CORS headers to all responses
        $origin = $request->headers->get('Origin');
        $allowedOrigins = explode(',', env('CORS_ALLOWED_ORIGINS', '*'));

        // Always allow frontend URL for static assets
        $frontendUrl = env('FRONTEND_URL', 'https://app.medroid.ai');
        if (in_array('*', $allowedOrigins) || in_array($origin, $allowedOrigins) || $origin === $frontendUrl) {
            $response->headers->set('Access-Control-Allow-Origin', $origin ?: $frontendUrl);
        }

        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN');
        $response->headers->set('Access-Control-Allow-Credentials', env('CORS_SUPPORTS_CREDENTIALS', 'true'));
        $response->headers->set('Access-Control-Max-Age', '86400');

        return $response;
    }
}

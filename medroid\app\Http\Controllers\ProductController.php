<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'images', 'user'])
            ->active()
            ->approved()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order')
            ->orderBy('name');

        // Filter by category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Filter by type
        if ($request->filled('type')) {
            if ($request->type === 'physical') {
                $query->physical();
            } elseif ($request->type === 'digital') {
                $query->digital();
            }
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sort
        if ($request->filled('sort')) {
            switch ($request->sort) {
                case 'price_low':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price_high':
                    $query->orderBy('price', 'desc');
                    break;
                case 'name':
                    $query->orderBy('name', 'asc');
                    break;
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
            }
        }

        $products = $query->paginate(12);
        $categories = ProductCategory::active()->rootCategories()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'products' => $products,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Shop/Products', [
            'products' => $products,
            'categories' => $categories,
            'filters' => $request->only(['category', 'type', 'search', 'min_price', 'max_price', 'sort']),
        ]);
    }

    public function show(Request $request, $slugOrId)
    {
        // Try to find by slug first, then by ID if slug doesn't exist
        $product = Product::with(['category', 'images', 'user'])
            ->where(function($query) use ($slugOrId) {
                $query->where('slug', $slugOrId)
                      ->orWhere('id', $slugOrId);
            })
            ->active()
            ->approved()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->firstOrFail();

        // Get related products
        $relatedProducts = Product::with(['category', 'images', 'user'])
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->active()
            ->approved()
            ->inStock()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->limit(4)
            ->get();

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
                'related_products' => $relatedProducts,
            ]);
        }

        return Inertia::render('Shop/ProductDetail', [
            'product' => $product,
            'relatedProducts' => $relatedProducts,
        ]);
    }

    public function featured(Request $request)
    {
        $products = Product::with(['category', 'images', 'user'])
            ->active()
            ->approved()
            ->featured()
            ->inStock()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->orderBy('sort_order')
            ->limit(8)
            ->get();

        if ($request->expectsJson()) {
            return response()->json([
                'products' => $products,
            ]);
        }

        return response()->json($products);
    }

    public function categories(Request $request)
    {
        $categories = ProductCategory::active()
            ->rootCategories()
            ->ordered()
            ->withCount(['products' => function ($query) {
                $query->active()->approved()->whereHas('user', function ($q) {
                    $q->where('is_active', true);
                });
            }])
            ->get();

        if ($request->expectsJson()) {
            return response()->json([
                'categories' => $categories,
            ]);
        }

        return response()->json($categories);
    }

    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2',
            'limit' => 'integer|min:1|max:50',
        ]);

        $query = $request->q;
        $limit = $request->get('limit', 10);

        $products = Product::with(['category', 'images', 'user'])
            ->active()
            ->approved()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('short_description', 'like', "%{$query}%")
                  ->orWhere('sku', 'like', "%{$query}%");
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('name')
            ->limit($limit)
            ->get();

        return response()->json([
            'products' => $products,
            'query' => $query,
        ]);
    }
}

<?php

namespace App\Services\Agents;

use App\Models\BotPersona;

class ContentStrategyAgent extends BaseAgent
{
    protected function getAgentName(): string
    {
        return 'ContentStrategyAgent';
    }

    protected function getDefaultOptions(): array
    {
        return [
            'temperature' => 0.7,
            'max_completion_tokens' => 400,
            'top_p' => 0.9,
        ];
    }

    protected function createSystemPrompt(array $context = []): string
    {
        return "You are a Content Strategy Expert specializing in health and wellness social media.

Your role is to analyze a persona and create a strategic content plan for their next post.

ANALYSIS FRAMEWORK:
1. Persona Analysis - Understand their unique voice, expertise, and audience
2. Content Themes - Identify relatable, everyday topics in their niche
3. Engagement Strategy - Plan for authentic social media connection
4. Social Media Optimization - Ensure content works for mobile feed consumption

OUTPUT FORMAT:
CONTENT_THEME: [Main topic/theme for the post]
TARGET_EMOTION: [Primary emotion to evoke: inspiration, motivation, education, comfort, etc.]
KEY_MESSAGE: [Core message in 1-2 sentences]
CONTENT_TYPE: [quick_tip, personal_moment, motivational, relatable_story, behind_scenes, etc.]
AUDIENCE_FOCUS: [Primary audience segment]
ENGAGEMENT_HOOK: [Strategy to encourage interaction]
TONE_DIRECTION: [Specific tone guidance for content creation]
VISUAL_STYLE: [Recommended visual approach]

Be strategic, authentic, and focused on creating content that genuinely helps people in their health journey.";
    }

    public function process(array $input): array
    {
        $this->validateInput($input, ['persona']);
        
        $persona = $input['persona'];
        $context = $input['context'] ?? [];
        
        $userPrompt = $this->createUserPrompt($persona, $context);
        $systemPrompt = $this->createSystemPrompt($context);
        
        $response = $this->executeAgent($systemPrompt, $userPrompt);
        
        return $this->parseStrategyResponse($response);
    }

    protected function createUserPrompt(BotPersona $persona, array $context): string
    {
        $timeOfDay = now()->format('H:i');
        $dayOfWeek = now()->format('l');
        $season = $this->getCurrentSeason();
        
        $interests = is_array($persona->interests) ? implode(', ', $persona->interests) : ($persona->interests ?? 'General wellness');
        $personalityTraits = is_array($persona->personality_traits) ? implode(', ', $persona->personality_traits) : ($persona->personality_traits ?? 'Authentic, helpful, knowledgeable');

        $prompt = "PERSONA ANALYSIS:
Name: {$persona->full_name} (@{$persona->handle})
Content Focus: {$persona->content_focus}
Tone: {$persona->tone}
Bio: {$persona->bio}
Interests: {$interests}
Personality Traits: {$personalityTraits}

CONTEXT:
Current Time: {$timeOfDay} on {$dayOfWeek}
Season: {$season}
Recent Posting Pattern: " . ($context['recent_posts'] ?? 'No recent posts') . "

CONTENT FOCUS EXPERTISE:
";

        // Add focus-specific context
        switch ($persona->content_focus) {
            case 'yoga':
                $prompt .= "- Yoga poses, mindfulness, flexibility, spiritual wellness
- Morning/evening routines, breathwork, meditation
- Mind-body connection, stress relief, inner peace";
                break;
            case 'fitness':
                $prompt .= "- Workout routines, strength training, cardio, recovery
- Fitness motivation, goal setting, progress tracking
- Exercise form, injury prevention, athletic performance";
                break;
            case 'nutrition':
                $prompt .= "- Healthy recipes, meal planning, nutritional science
- Dietary tips, food education, cooking techniques
- Wellness through nutrition, energy optimization";
                break;
            case 'mental health':
                $prompt .= "- Mental wellness strategies, stress management, mindfulness
- Emotional intelligence, self-care practices, resilience
- Mental health awareness, coping techniques, therapy insights";
                break;
            default:
                $prompt .= "- General health and wellness topics
- Lifestyle optimization, preventive care, holistic health
- Evidence-based wellness practices, healthy habits";
        }

        $prompt .= "\n\nCreate a strategic content plan for their next engaging post that authentically represents their expertise and connects with their audience.";

        return $prompt;
    }

    protected function parseStrategyResponse(string $response): array
    {
        $patterns = [
            'content_theme' => '/CONTENT_THEME:\s*(.*?)(?=TARGET_EMOTION:|$)/s',
            'target_emotion' => '/TARGET_EMOTION:\s*(.*?)(?=KEY_MESSAGE:|$)/s',
            'key_message' => '/KEY_MESSAGE:\s*(.*?)(?=CONTENT_TYPE:|$)/s',
            'content_type' => '/CONTENT_TYPE:\s*(.*?)(?=AUDIENCE_FOCUS:|$)/s',
            'audience_focus' => '/AUDIENCE_FOCUS:\s*(.*?)(?=ENGAGEMENT_HOOK:|$)/s',
            'engagement_hook' => '/ENGAGEMENT_HOOK:\s*(.*?)(?=TONE_DIRECTION:|$)/s',
            'tone_direction' => '/TONE_DIRECTION:\s*(.*?)(?=VISUAL_STYLE:|$)/s',
            'visual_style' => '/VISUAL_STYLE:\s*(.*?)$/s',
        ];

        $strategy = $this->parseStructuredResponse($response, $patterns);

        // Add fallbacks for missing fields
        $strategy['content_theme'] = $strategy['content_theme'] ?? 'General wellness tips';
        $strategy['target_emotion'] = $strategy['target_emotion'] ?? 'inspiration';
        $strategy['key_message'] = $strategy['key_message'] ?? 'Focus on your health journey';
        $strategy['content_type'] = $strategy['content_type'] ?? 'educational';
        $strategy['audience_focus'] = $strategy['audience_focus'] ?? 'health-conscious individuals';
        $strategy['engagement_hook'] = $strategy['engagement_hook'] ?? 'Ask a question';
        $strategy['tone_direction'] = $strategy['tone_direction'] ?? 'friendly and encouraging';
        $strategy['visual_style'] = $strategy['visual_style'] ?? 'clean and inspiring';

        return [
            'strategy' => $strategy,
            'raw_response' => $response,
            'agent' => $this->agentName,
            'timestamp' => now()->toISOString(),
        ];
    }

    protected function getCurrentSeason(): string
    {
        $month = now()->month;
        
        if ($month >= 3 && $month <= 5) return 'Spring';
        if ($month >= 6 && $month <= 8) return 'Summer';
        if ($month >= 9 && $month <= 11) return 'Fall';
        return 'Winter';
    }

    public function test(): array
    {
        // Create a test persona
        $testPersona = new BotPersona();
        $testPersona->first_name = 'Test';
        $testPersona->last_name = 'User';
        $testPersona->handle = 'test_user';
        $testPersona->content_focus = 'fitness';
        $testPersona->tone = 'energetic';
        $testPersona->bio = 'Fitness enthusiast helping others achieve their goals';
        $testPersona->interests = ['strength training', 'cardio', 'nutrition'];
        $testPersona->personality_traits = ['motivational', 'disciplined', 'supportive'];

        $result = $this->process([
            'persona' => $testPersona,
            'context' => ['test_mode' => true],
        ]);

        return [
            'agent' => $this->agentName,
            'test_successful' => isset($result['strategy']['content_theme']),
            'strategy_fields' => array_keys($result['strategy'] ?? []),
            'sample_theme' => $result['strategy']['content_theme'] ?? 'Not generated',
        ];
    }
}

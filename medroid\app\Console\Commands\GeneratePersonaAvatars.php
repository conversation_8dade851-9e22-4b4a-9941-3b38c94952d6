<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Services\AvatarGenerationService;
use Illuminate\Console\Command;

class GeneratePersonaAvatars extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bot:generate-avatars
                            {--persona= : Generate avatar for specific persona ID}
                            {--force : Regenerate even if avatar already exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate avatars and face references for bot personas';

    private AvatarGenerationService $avatarService;

    public function __construct(AvatarGenerationService $avatarService)
    {
        parent::__construct();
        $this->avatarService = $avatarService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎭 Starting Avatar Generation for Bot Personas...');

        $personaId = $this->option('persona');
        $force = $this->option('force');

        if ($personaId) {
            return $this->generateForSpecificPersona($personaId, $force);
        }

        return $this->generateForAllPersonas($force);
    }

    private function generateForSpecificPersona(int $personaId, bool $force): int
    {
        $persona = BotPersona::find($personaId);

        if (!$persona) {
            $this->error("❌ Persona with ID {$personaId} not found");
            return 1;
        }

        return $this->processPersona($persona, $force) ? 0 : 1;
    }

    private function generateForAllPersonas(bool $force): int
    {
        $personas = BotPersona::active()->get();

        if ($personas->isEmpty()) {
            $this->warn('⚠️ No active personas found');
            return 0;
        }

        $this->info("Found {$personas->count()} active personas");

        $successCount = 0;
        $totalCount = $personas->count();

        foreach ($personas as $persona) {
            if ($this->processPersona($persona, $force)) {
                $successCount++;
            }
        }

        $this->info("✅ Avatar generation completed: {$successCount}/{$totalCount} successful");

        return $successCount === $totalCount ? 0 : 1;
    }

    private function processPersona(BotPersona $persona, bool $force): bool
    {
        $this->line("Processing: {$persona->full_name}");

        // Skip if avatar already exists and not forcing
        if (!$force && $persona->avatar_url && $persona->face_reference_url) {
            $this->line("  ⏭️ Skipping - Avatar already exists (use --force to regenerate)");
            return true;
        }

        // Generate physical description first
        if (empty($persona->physical_description)) {
            $this->line("  📝 Generating physical description...");
            $this->generatePhysicalDescription($persona);
        }

        $this->line("  🎨 Generating avatar and face reference...");

        $success = $this->avatarService->generateAvatarForPersona($persona);

        if ($success) {
            $this->line("  ✅ Avatar generated successfully");
            $this->line("     Avatar: {$persona->fresh()->avatar_url}");
            $this->line("     Face Ref: {$persona->fresh()->face_reference_url}");
            return true;
        } else {
            $this->line("  ❌ Failed to generate avatar");
            return false;
        }
    }

    private function generatePhysicalDescription(BotPersona $persona): void
    {
        // Predefined descriptions for existing personas
        $descriptions = [
            'Hannah Kim' => 'Asian woman, late 20s, shoulder-length black hair, warm brown eyes, gentle smile, athletic build from yoga practice',
            'Luis Rodriguez' => 'Hispanic man, early 30s, short dark hair, brown eyes, athletic runner build, confident smile, tan complexion',
            'Javier Martinez' => 'Latino man, mid 40s, salt-and-pepper beard, kind brown eyes, creative artistic style, warm expression',
            'Leila Haddad' => 'Middle Eastern woman, early 30s, long dark curly hair, expressive brown eyes, professional yet approachable',
            'Ingrid Olsen' => 'Scandinavian woman, late 30s, blonde hair in a bun, blue eyes, minimalist style, serene expression',
        ];

        $description = $descriptions[$persona->full_name] ??
                      "Professional person, {$this->getAgeGroup($persona->age)}, warm and approachable expression";

        $persona->update(['physical_description' => $description]);
        $this->line("     Description: {$description}");
    }

    private function getAgeGroup(int $age): string
    {
        if ($age < 25) return 'early 20s';
        if ($age < 30) return 'late 20s';
        if ($age < 35) return 'early 30s';
        if ($age < 40) return 'late 30s';
        if ($age < 45) return 'early 40s';
        if ($age < 50) return 'late 40s';
        return 'middle-aged';
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Provider;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class ServiceController extends Controller
{
    /**
     * Display a listing of the provider's services.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {
        $user = $request->user();

        // If ID is provided in route (for /providers/{id}/services)
        if ($id) {
            $provider = Provider::with('user')->find($id);

            if (!$provider || !$provider->user->is_active) {
                return response()->json([
                    'message' => 'Provider not found or inactive'
                ], 404);
            }

            // Get the provider's services (only approved ones for public viewing)
            $services = Service::where('provider_id', $id)
                ->where('active', true)
                ->approved()
                ->get();

            // For debugging: also get all services for this provider
            $allServices = Service::where('provider_id', $id)->get();
            $pendingServices = Service::where('provider_id', $id)->where('approval_status', 'pending')->get();

            // Add availability information to each service
            foreach ($services as $service) {
                $weeklyAvailability = $provider->weekly_availability;
                $service->availability = $weeklyAvailability;
            }

            return response()->json([
                'services' => $services,
                'provider' => $provider->load('user'),
                'debug' => [
                    'total_services' => $allServices->count(),
                    'pending_services' => $pendingServices->count(),
                    'approved_services' => $services->count(),
                    'all_services_status' => $allServices->pluck('approval_status', 'name'),
                ]
            ]);
        }

        // Original logic for authenticated users
        if ($user && $user->role === 'provider') {
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            $services = $provider->services()->get();

        } else {
            // For patients or other users, filter by provider_id if provided
            if ($request->has('provider_id')) {
                // Get the provider and its weekly availability
                $provider = Provider::with('user')->find($request->provider_id);

                if (!$provider || !$provider->user->is_active) {
                    return response()->json([
                        'message' => 'Provider not found or inactive'
                    ], 404);
                }

                // Get the provider's services
                $services = Service::where('provider_id', $request->provider_id)
                    ->where('active', true)
                    ->approved()
                    ->get();

                // Add availability information to each service
                foreach ($services as $service) {
                    // Get the provider's weekly availability
                    $weeklyAvailability = $provider->weekly_availability;

                    // Set the availability on the service
                    $service->availability = $weeklyAvailability;
                }
            } else {
                // Get all active services from active providers only
                $services = Service::where('active', true)
                    ->approved()
                    ->with('provider.user')
                    ->whereHas('provider.user', function ($query) {
                        $query->where('is_active', true);
                    })
                    ->get();

                // Add availability information to each service
                foreach ($services as $service) {
                    // Get the provider
                    $provider = $service->provider;

                    if ($provider) {
                        // Get the provider's weekly availability
                        $weeklyAvailability = $provider->weekly_availability;

                        // Set the availability on the service
                        $service->availability = $weeklyAvailability;
                    }
                }
            }
        }

        return response()->json($services);
    }

    /**
     * Store a newly created service in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'duration' => 'required|integer|min:5',
            'price' => 'required|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'availability' => 'nullable|array',
            'active' => 'boolean',
            'is_telemedicine' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Only providers can create services'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        // Determine approval status based on user role
        $approvalStatus = $user->hasRole('admin') ? 'approved' : 'pending';
        $approvedBy = $user->hasRole('admin') ? $user->id : null;
        $approvedAt = $user->hasRole('admin') ? now() : null;

        $service = Service::create([
            'provider_id' => $provider->id,
            'name' => $request->name,
            'description' => $request->description,
            'duration' => $request->duration,
            'price' => $request->price,
            'category' => $request->category,
            'availability' => $request->availability,
            'active' => $request->has('active') ? $request->active : true,
            'is_telemedicine' => $request->has('is_telemedicine') ? $request->is_telemedicine : false,
            'approval_status' => $approvalStatus,
            'approved_by' => $approvedBy,
            'approved_at' => $approvedAt,
        ]);

        return response()->json([
            'message' => 'Service created successfully',
            'service' => $service
        ], 201);
    }

    /**
     * Display the specified service.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $service = Service::with('provider.user')->findOrFail($id);
        
        // Add availability information to the service
        if ($service->provider) {
            // Get the provider's weekly availability
            $weeklyAvailability = $service->provider->weekly_availability;
            
            // Set the availability on the service
            $service->availability = $weeklyAvailability;
        }

        return response()->json($service);
    }

    /**
     * Update the specified service in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        \Log::info('Service update request received', [
            'service_id' => $id,
            'user_id' => $request->user()?->id,
            'request_data' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'duration' => 'sometimes|required|integer|min:5',
            'price' => 'sometimes|required|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'availability' => 'nullable|array',
            'active' => 'boolean',
            'is_telemedicine' => 'boolean',
            'supports_video' => 'boolean',
            'supports_audio' => 'boolean',
            'supports_chat' => 'boolean',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_valid_until' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            \Log::warning('Service update validation failed', [
                'service_id' => $id,
                'errors' => $validator->errors()
            ]);
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if ($user->role !== 'provider') {
            \Log::warning('Non-provider user attempted to update service', [
                'user_id' => $user->id,
                'user_role' => $user->role
            ]);
            return response()->json([
                'message' => 'Only providers can update services'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            \Log::warning('Provider profile not found for user', [
                'user_id' => $user->id
            ]);
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        $service = Service::findOrFail($id);

        if ($service->provider_id !== $provider->id) {
            \Log::warning('Unauthorized service update attempt', [
                'service_id' => $id,
                'service_provider_id' => $service->provider_id,
                'user_provider_id' => $provider->id
            ]);
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $oldData = $service->toArray();
        $service->update($request->all());
        $newData = $service->fresh()->toArray();

        \Log::info('Service updated successfully', [
            'service_id' => $id,
            'old_data' => $oldData,
            'new_data' => $newData
        ]);

        return response()->json([
            'message' => 'Service updated successfully',
            'service' => $service->fresh()
        ]);
    }

    /**
     * Remove the specified service from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();

        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Only providers can delete services'
            ], 403);
        }

        $provider = Provider::where('user_id', $user->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found'
            ], 404);
        }

        $service = Service::findOrFail($id);

        if ($service->provider_id !== $provider->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if there are any appointments for this service
        $hasAppointments = $service->appointments()->exists();

        if ($hasAppointments) {
            // Instead of deleting, mark as inactive
            $service->update(['active' => false]);

            return response()->json([
                'message' => 'Service has existing appointments and cannot be deleted. It has been marked as inactive.'
            ]);
        }

        $service->delete();

        return response()->json([
            'message' => 'Service deleted successfully'
        ]);
    }

    /**
     * Get services by category.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getByCategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $services = Service::where('category', $request->category)
            ->where('active', true)
            ->approved()
            ->with('provider.user')
            ->whereHas('provider.user', function ($query) {
                $query->where('is_active', true);
            })
            ->get();
            
        // Add availability information to each service
        foreach ($services as $service) {
            // Get the provider
            $provider = $service->provider;
            
            if ($provider) {
                // Get the provider's weekly availability
                $weeklyAvailability = $provider->weekly_availability;
                
                // Set the availability on the service
                $service->availability = $weeklyAvailability;
            }
        }

        return response()->json($services);
    }

    /**
     * Get all service categories.
     *
     * @return \Illuminate\Http\Response
     */
    public function getCategories()
    {
        $categories = Service::where('active', true)
            ->approved()
            ->whereHas('provider.user', function ($query) {
                $query->where('is_active', true);
            })
            ->distinct()
            ->pluck('category')
            ->filter()
            ->values();

        return response()->json(['categories' => $categories]);
    }

    /**
     * Get pending services for approval
     */
    public function getPendingServices(Request $request)
    {
        $user = Auth::user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = Service::with(['provider.user', 'approvedBy'])
            ->where('approval_status', 'pending')
            ->orderBy('created_at', 'desc');

        $services = $query->paginate($request->get('per_page', 15));

        return response()->json($services);
    }

    /**
     * Approve a service
     */
    public function approveService(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $service = Service::findOrFail($id);

            $service->update([
                'approval_status' => 'approved',
                'approved_by' => $user->id,
                'approved_at' => now(),
                'rejection_reason' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Service approved successfully',
                'service' => $service->load(['provider.user', 'approvedBy']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve service: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reject a service
     */
    public function rejectService(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        try {
            $service = Service::findOrFail($id);

            $service->update([
                'approval_status' => 'rejected',
                'approved_by' => $user->id,
                'approved_at' => now(),
                'rejection_reason' => $request->rejection_reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Service rejected successfully',
                'service' => $service->load(['provider.user', 'approvedBy']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject service: ' . $e->getMessage(),
            ], 500);
        }
    }
}

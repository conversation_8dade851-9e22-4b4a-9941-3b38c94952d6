<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Clinic;
use App\Models\Provider;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class TestClinicSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test clinic
        $clinic = Clinic::firstOrCreate(
            ['name' => 'Test Medical Clinic'],
            [
                'description' => 'A test clinic for development purposes',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'website' => 'https://testclinic.com',
                'address' => '123 Medical Center Drive',
                'city' => 'Test City',
                'state' => 'Test State',
                'postal_code' => '12345',
                'country' => 'United States',
                'license_number' => 'TEST-LIC-001',
                'is_active' => true,
                'accepts_new_patients' => true,
                'telemedicine_enabled' => true,
                'primary_color' => '#2563eb',
                'secondary_color' => '#1e40af'
            ]
        );

        // Create a clinic admin user (provider role with clinic management permissions)
        $clinicAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Dr. Test Clinic Admin',
                'password' => Hash::make('password123'),
                'role' => 'provider',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create provider record for the clinic admin
        $provider = Provider::firstOrCreate(
            ['user_id' => $clinicAdmin->id],
            [
                'clinic_id' => $clinic->id,
                'specialization' => 'General Practice',
                'license_number' => 'TEST-PROV-001',
                'bio' => 'Test clinic administrator and general practitioner',
                'verification_status' => 'verified',
                'verified_at' => now(),
                'rating' => 4.8,
                'pricing' => [
                    'consultation_fee' => 100.00,
                    'currency' => 'USD'
                ],
            ]
        );

        // Assign provider role and clinic management permissions
        $providerRole = Role::firstOrCreate(['name' => 'provider']);
        $clinicAdmin->assignRole($providerRole);

        // Give clinic management permissions
        $clinicAdmin->givePermissionTo([
            'view clinics',
            'edit clinics',
            'manage clinics',
            'view providers',
            'view patients',
            'view appointments',
            'edit appointments'
        ]);

        // Create a regular clinic provider
        $clinicProvider = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Dr. Test Provider',
                'password' => Hash::make('password123'),
                'role' => 'provider',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create provider record for the regular provider
        Provider::firstOrCreate(
            ['user_id' => $clinicProvider->id],
            [
                'clinic_id' => $clinic->id,
                'specialization' => 'Cardiology',
                'license_number' => 'TEST-PROV-002',
                'bio' => 'Test cardiologist',
                'verification_status' => 'verified',
                'verified_at' => now(),
                'rating' => 4.6,
                'pricing' => [
                    'consultation_fee' => 150.00,
                    'currency' => 'USD'
                ],
            ]
        );

        $clinicProvider->assignRole($providerRole);

        // Create a test patient for the clinic
        $patient = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Patient',
                'password' => Hash::make('password123'),
                'role' => 'patient',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        $patientRole = Role::firstOrCreate(['name' => 'patient']);
        $patient->assignRole($patientRole);

        $this->command->info('Test clinic and users created successfully!');
        $this->command->info('Clinic: ' . $clinic->name . ' (ID: ' . $clinic->id . ')');
        $this->command->info('Clinic Admin: ' . $clinicAdmin->email . ' (password: password123)');
        $this->command->info('Provider: ' . $clinicProvider->email . ' (password: password123)');
        $this->command->info('Patient: ' . $patient->email . ' (password: password123)');
    }
}

<?php

namespace App\Observers;

use App\Models\Order;
use App\Models\User;
use App\Mail\NewOrderNotificationMail;
use Illuminate\Support\Facades\Mail;

class OrderObserver
{
    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        // Load the order with relationships
        $order->load(['user', 'items.product.user']);

        // Send confirmation email to customer
        try {
            \Log::info("Sending order confirmation email to customer: {$order->user->email} for order #{$order->id}");
            Mail::to($order->user->email)->send(new NewOrderNotificationMail($order, $order->user, false));
            \Log::info("Order confirmation email queued successfully for customer: {$order->user->email}");
        } catch (\Exception $e) {
            \Log::error('Failed to send customer order confirmation email: ' . $e->getMessage());
        }

        // Send notification emails to providers
        $providers = collect();

        foreach ($order->items as $item) {
            if ($item->product && $item->product->user) {
                $providers->put($item->product->user->id, $item->product->user);
            }
        }

        foreach ($providers as $provider) {
            try {
                \Log::info("Sending order notification email to provider: {$provider->email} for order #{$order->id}");
                Mail::to($provider->email)->send(new NewOrderNotificationMail($order, $provider, true));
                \Log::info("Order notification email queued successfully for provider: {$provider->email}");
            } catch (\Exception $e) {
                \Log::error("Failed to send provider order notification email to {$provider->email}: " . $e->getMessage());
            }
        }
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $order): void
    {
        //
    }
}

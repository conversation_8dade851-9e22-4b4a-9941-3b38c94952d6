<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\Patient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MistralNemoService
{
    protected $apiKey;
    protected $apiUrl;
    protected $modelName;
    protected $maxContextTokens = 8000; // Default max token limit for large context models

    public function __construct()
    {
        $this->apiKey = config('services.mistral_nemo.api_key');
        $this->apiUrl = config('services.mistral_nemo.api_url');
        $this->modelName = config('services.mistral_nemo.model_name');
    }

    /**
     * Generate a response from the AI based on the conversation history
     */
    public function generateResponse(ChatConversation $conversation, $includePatientContext = true)
    {
        try {
            // Format the conversation history for the Mistral API
            $messages = $this->formatMessages($conversation);

            // Add patient context if available and requested
            $patientContext = '';
            if ($includePatientContext) {
                $patientContext = $this->getPatientContext($conversation->patient_id);
            }

            // Create the system prompt with healthcare context
            $systemPrompt = $this->createSystemPrompt($patientContext);

            // Check if we need to truncate the conversation history to fit within token limits
            $allMessages = array_merge([
                ['role' => 'system', 'content' => $systemPrompt],
            ], $messages);

            // Apply message truncation if needed
            $truncatedMessages = $this->truncateMessagesIfNeeded($allMessages);

            // Prepare the request
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->modelName,
                'messages' => $truncatedMessages,
                'temperature' => 0.7,
                'top_p' => 0.9,
                'max_tokens' => 1200,
                'response_format' => ['type' => 'json_object'],
            ]);

            if ($response->successful()) {
                $result = $response->json();

                // Check if choices array exists and has at least one item
                if (isset($result['choices']) && is_array($result['choices']) && !empty($result['choices'])) {
                    // Check if message array exists and content key exists
                    if (isset($result['choices'][0]['message']) && isset($result['choices'][0]['message']['content'])) {
                        $content = $result['choices'][0]['message']['content'];
                    } else {
                        $content = '';
                    }
                } else {
                    $content = '';
                }

                // Process the AI response to extract structured information
                $processedResponse = $this->processAIResponse($content);

                // Apply additional healthcare validations and enhancements
                $enhancedResponse = $this->enhanceResponse($processedResponse, $conversation);

                return $enhancedResponse;
            } else {
                Log::error('Mistral API error', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                ]);

                return [
                    'message' => 'I apologize, but I am having trouble processing your request right now. Please try again later.',
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                ];
            }
        } catch (\Exception $e) {
            Log::error('Mistral API exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'message' => 'I apologize, but I am having trouble processing your request right now. Please try again later.',
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Format the conversation messages for the Mistral API
     */
    private function formatMessages(ChatConversation $conversation)
    {
        return collect($conversation->messages)->map(function ($message) {
            return [
                'role' => $message['role'],
                'content' => $message['content'],
            ];
        })->toArray();
    }

    /**
     * Get patient context information if available
     */
    private function getPatientContext($patientId)
    {
        try {
            $patient = Patient::find($patientId);
            if (!$patient) {
                return '';
            }

            $context = "Patient context:\n";

            // Add health history if available
            if (!empty($patient->health_history)) {
                $context .= "Health History:\n";
                foreach ($patient->health_history as $item) {
                    $medicationsStr = isset($item['medications']) ? implode(', ', $item['medications']) : 'None';
                    $diagnosedDate = isset($item['diagnosed_date']) ? date('Y-m-d', strtotime($item['diagnosed_date'])) : 'Unknown date';
                    $context .= "- {$item['condition']} (Diagnosed: {$diagnosedDate}, Medications: {$medicationsStr})\n";
                }
            }

            // Add allergies if available
            if (!empty($patient->allergies)) {
                $context .= "\nAllergies: " . implode(', ', $patient->allergies) . "\n";
            }

            // Add preferences (health topics of interest) if available
            if (!empty($patient->preferences) && isset($patient->preferences['feed_topics']) && !empty($patient->preferences['feed_topics'])) {
                $context .= "\nHealth Topics of Interest: " . implode(', ', $patient->preferences['feed_topics']) . "\n";
            }

            return $context;
        } catch (\Exception $e) {
            Log::error('Error getting patient context', [
                'message' => $e->getMessage(),
                'patient_id' => $patientId,
            ]);
            return '';
        }
    }

    /**
     * Truncate messages if they exceed token limits
     */
    private function truncateMessagesIfNeeded($messages, $maxTokens = null)
    {
        if ($maxTokens === null) {
            $maxTokens = $this->maxContextTokens - 1200; // Reserve 1200 tokens for the response
        }

        // Simple approximation of token count (4 chars ~= 1 token)
        $totalTokens = 0;
        foreach ($messages as $message) {
            $totalTokens += (int)(strlen($message['content']) / 4) + 4; // 4 extra tokens for message metadata
        }

        // If under limit, return as is
        if ($totalTokens <= $maxTokens) {
            return $messages;
        }

        // Otherwise, keep the system message and truncate the conversation history
        $systemMessage = $messages[0]; // Assuming system message is first
        $conversationMessages = array_slice($messages, 1);

        // We'll try to keep the most recent N messages
        $truncatedMessages = [$systemMessage];
        $currentTokens = (int)(strlen($systemMessage['content']) / 4) + 4;

        // Start from the most recent messages and work backwards
        for ($i = count($conversationMessages) - 1; $i >= 0; $i--) {
            $message = $conversationMessages[$i];
            $messageTokens = (int)(strlen($message['content']) / 4) + 4;

            if ($currentTokens + $messageTokens <= $maxTokens) {
                $truncatedMessages[] = $message;
                $currentTokens += $messageTokens;
            } else {
                // We've hit the limit, stop adding messages
                break;
            }
        }

        // Reverse the messages to maintain chronological order
        return array_merge([$systemMessage], array_reverse(array_slice($truncatedMessages, 1)));
    }

    /**
     * Create a system prompt for healthcare context
     */
    private function createSystemPrompt($patientContext = '')
    {
        $basePrompt = "
            You are MedAI, an advanced, accurate, and compassionate healthcare assistant. Your primary goal is to provide reliable, evidence-based information about health topics while being sensitive to the user's concerns.

            Guidelines:
            1. Provide accurate health information based on established medical consensus.
            2. Be careful not to diagnose definitively or prescribe specific treatments - always suggest consulting with qualified healthcare professionals.
            3. Escalate potentially serious health concerns to real healthcare providers.
            4. Always maintain patient privacy and confidentiality.
            5. Be particularly thoughtful and sensitive when addressing mental health concerns.
            6. Use clear, non-technical language when possible, and explain medical terms when they must be used.
            7. Format your responses as detailed, accurate and helpful JSON with the following structure:
               {
                 \"message\": \"Your compassionate and informative response to the user\",
                 \"health_concerns\": [\"list\", \"of\", \"identified\", \"health\", \"topics\"],
                 \"recommendations\": [
                   {\"type\": \"specialist\", \"content\": \"Consider consulting with a [specialist type] for [specific reason]\"},
                   {\"type\": \"lifestyle\", \"content\": \"Consider [detailed lifestyle recommendation]\"},
                   {\"type\": \"resource\", \"content\": \"Check [specific trusted health resource] for more information\"},
                   {\"type\": \"medication_info\", \"content\": \"About the medication you mentioned: [accurate information]\"}
                 ],
                 \"escalate\": true/false,
                 \"escalation_reason\": \"Only include this field if escalate is true, explaining why this should be escalated\"
               }

            Escalation Criteria:
            - Set \"escalate\": true if you detect any potentially serious conditions requiring prompt medical attention, including:
              - Severe, persistent, or unusual pain
              - Difficulty breathing or shortness of breath
              - Potential signs of heart attack or stroke (chest pain, numbness, confusion, severe headache)
              - Suicidal thoughts or significant mental health crisis
              - Severe allergic reactions
              - Significant injuries or bleeding
              - High fever combined with other concerning symptoms
              - Sudden, unexplained changes in mental status or severe cognitive issues
              - Pregnancy complications
              - Worsening of chronic conditions despite treatment
              - Symptoms of severe infection or sepsis
            - For non-emergency situations, set \"escalate\": false

            In your recommendations, be specific and helpful. Base them on well-established medical knowledge.
        ";

        // Add patient context if available
        if (!empty($patientContext)) {
            $basePrompt .= "\n\n" . $patientContext;
        }

        return $basePrompt;
    }

    /**
     * Process the AI response to extract structured information
     */
    private function processAIResponse($content)
    {
        try {
            // Try to parse JSON response
            $data = json_decode($content, true);

            if (json_last_error() === JSON_ERROR_NONE && isset($data['message'])) {
                return $data;
            }

            // If direct parsing failed, try to extract JSON part
            if (strpos($content, '{') !== false && strpos($content, '}') !== false) {
                // Extract JSON part of the response
                preg_match('/{.*}/s', $content, $matches);
                if (!empty($matches)) {
                    $jsonStr = $matches[0];
                    $data = json_decode($jsonStr, true);

                    if (json_last_error() === JSON_ERROR_NONE && isset($data['message'])) {
                        return $data;
                    }
                }
            }

            // If JSON parsing fails, return the raw response
            return [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        } catch (\Exception $e) {
            Log::error('Error processing AI response', [
                'message' => $e->getMessage(),
                'content' => $content,
            ]);

            return [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Apply additional healthcare-specific validation and enhancement of the response
     */
    private function enhanceResponse($response, $conversation)
    {
        try {
            // Ensure all required fields are present
            if (!isset($response['message']) || empty($response['message'])) {
                $response['message'] = "I apologize, but I couldn't properly process your request. Please try rephrasing your question.";
            }

            // Ensure health concerns is an array
            if (!isset($response['health_concerns']) || !is_array($response['health_concerns'])) {
                $response['health_concerns'] = [];
            }

            // Ensure recommendations is an array
            if (!isset($response['recommendations']) || !is_array($response['recommendations'])) {
                $response['recommendations'] = [];
            } else {
                // Validate each recommendation has required fields
                foreach ($response['recommendations'] as $key => $recommendation) {
                    if (!isset($recommendation['type']) || !isset($recommendation['content']) || empty($recommendation['content'])) {
                        unset($response['recommendations'][$key]);
                        continue;
                    }

                    // Validate recommendation type
                    $validTypes = ['specialist', 'lifestyle', 'resource', 'medication_info', 'followup', 'prevention'];
                    if (!in_array($recommendation['type'], $validTypes)) {
                        $response['recommendations'][$key]['type'] = 'resource';
                    }
                }

                // Re-index the array
                $response['recommendations'] = array_values($response['recommendations']);
            }

            // Check if any emergency keywords are present even if not flagged by LLM
            if (!($response['escalate'] ?? false)) {
                $lastMessage = end($conversation->messages);
                $userMessage = $lastMessage['content'] ?? '';
                $emergencyKeywords = [
                    'suicide', 'kill myself', 'emergency', 'severe pain', 'heart attack', 'stroke',
                    'unconscious', 'breathing difficulty', 'chest pain', 'overdose', 'bleeding heavily',
                    'can\'t breathe', 'can\'t breathe', 'anaphylaxis', 'severe allergic'
                ];

                foreach ($emergencyKeywords as $keyword) {
                    if (stripos($userMessage, $keyword) !== false) {
                        $response['escalate'] = true;
                        $response['escalation_reason'] = 'Potentially serious health concern detected.';
                        break;
                    }
                }
            }

            // Add medical disclaimer if not escalated
            if (!($response['escalate'] ?? false)) {
                $response['message'] .= "\n\n*Remember: This information is for educational purposes only and should not replace professional medical advice. Always consult with healthcare providers for diagnosis and treatment.*";
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('Error enhancing AI response', [
                'message' => $e->getMessage(),
            ]);

            return $response;
        }
    }

    /**
     * Analyze health concerns in text
     */
    public function analyzeHealthConcerns($text)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->modelName,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a healthcare analysis assistant specialized in identifying health topics and concerns from patient text. Extract all health-related topics, symptoms, conditions, medications, and procedures mentioned. Return a JSON array of health topics.'
                    ],
                    [
                        'role' => 'user',
                        'content' => "Carefully analyze the following text and extract all health topics, symptoms, treatments, and concerns as a JSON array: \"{$text}\""
                    ]
                ],
                'temperature' => 0.3,
                'response_format' => ['type' => 'json_object'],
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Try to extract JSON array from response
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($data['health_topics'])) {
                    return $data['health_topics'];
                }

                // Try to extract just an array
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    return $data;
                }

                // Try to match an array pattern in the string
                preg_match('/\[.*\]/s', $content, $matches);
                if (!empty($matches)) {
                    $jsonStr = $matches[0];
                    $topics = json_decode($jsonStr, true);

                    if (json_last_error() === JSON_ERROR_NONE) {
                        return $topics;
                    }
                }

                return [];
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Health concern analysis failed', [
                'message' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Detect appointment booking intent from message context
     *
     * @param string $message
     * @return bool
     */
    public function detectAppointmentBookingIntent($message)
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('Mistral API key is missing. Cannot detect appointment intent.');
                return false;
            }

            // Create a system prompt specifically for intent detection
            $systemPrompt = "You are an AI assistant that analyzes user messages to detect appointment booking intent. " .
                           "Your task is to determine if the user is trying to book a medical appointment, schedule a consultation, " .
                           "or request to see a healthcare provider. " .
                           "Respond with ONLY 'true' if you detect appointment booking intent, or 'false' if not.";

            // Prepare the API request
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->modelName,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $message]
                ],
                'temperature' => 0.1, // Low temperature for more deterministic responses
                'max_tokens' => 10,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                return strtolower(trim($content)) === 'true';
            }

            // Log the specific error from Mistral
            $errorBody = $response->body();
            Log::error('Mistral API error in intent detection', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            // Fallback to a simple check for critical keywords if API call fails
            $criticalKeywords = [
                'book an appointment', 'schedule an appointment',
                'make an appointment', 'need an appointment'
            ];

            foreach ($criticalKeywords as $phrase) {
                if (stripos($message, $phrase) !== false) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Error in appointment intent detection', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Fallback to a simple check for critical keywords if exception occurs
            $criticalKeywords = [
                'book an appointment', 'schedule an appointment',
                'make an appointment', 'need an appointment'
            ];

            foreach ($criticalKeywords as $phrase) {
                if (stripos($message, $phrase) !== false) {
                    return true;
                }
            }

            return false;
        }
    }

    /**
     * Generate a title for a conversation
     */
    public function generateTitle($conversationText)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->modelName,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant that generates concise, descriptive titles for medical conversations. Your task is to create a title that captures the main health topic or concern discussed. Keep titles under 50 characters, clear, and informative. Return only the title text without quotes or additional formatting.'
                    ],
                    [
                        'role' => 'user',
                        'content' => "Generate a concise, descriptive title (max 50 characters) that summarizes the main topic of this conversation:\n\n{$conversationText}"
                    ]
                ],
                'temperature' => 0.3,
                'max_tokens' => 100,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $title = $result['choices'][0]['message']['content'] ?? 'Health Conversation';

                // Clean up the title - remove quotes if present
                $title = trim($title, " \t\n\r\0\x0B\"'");

                return $title;
            }

            return 'Health Conversation';
        } catch (\Exception $e) {
            Log::error('Error generating title', [
                'message' => $e->getMessage(),
            ]);

            return 'Health Conversation';
        }
    }

    /**
     * Retrieve reliable medication information
     */
    public function getMedicationInfo($medicationName)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->modelName,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a pharmacology specialist providing accurate, professional information about medications. Provide detailed, factual information about the medication based on reliable medical knowledge. Include the medication class, common uses, typical dosing, potential side effects, major warnings, and common interactions. Format as JSON with fields: name, class, uses, dosage, side_effects, warnings, interactions. If the medication isn\'t recognized as legitimate, instead return a JSON with a single field warning: "This doesn\'t appear to be a recognized medication name."'
                    ],
                    [
                        'role' => 'user',
                        'content' => "Provide detailed, accurate information about the medication: {$medicationName}"
                    ]
                ],
                'temperature' => 0.2,
                'response_format' => ['type' => 'json_object'],
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Try to parse JSON response
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $data;
                }

                return [
                    'warning' => 'Could not retrieve medication information at this time.'
                ];
            }

            return [
                'warning' => 'Could not retrieve medication information at this time.'
            ];
        } catch (\Exception $e) {
            Log::error('Medication info retrieval failed', [
                'message' => $e->getMessage(),
                'medication' => $medicationName,
            ]);

            return [
                'warning' => 'Could not retrieve medication information at this time.'
            ];
        }
    }

    /**
     * Generate symptom analysis based on user-reported symptoms
     */
    public function analyzeSymptoms($symptoms, $patientContext = '')
    {
        try {
            $systemPrompt = "
                You are a medical symptom analysis assistant. Your task is to:
                1. Identify the symptoms described
                2. List potential common causes for these symptoms (never provide a definitive diagnosis)
                3. Suggest appropriate types of healthcare providers to consult
                4. Indicate urgency level (Routine, Soon, Urgent, Emergency)
                5. Format response as JSON with: symptoms_identified, potential_causes, provider_types, urgency, reasoning

                IMPORTANT: Always err on the side of caution. When in doubt about severity, recommend seeking prompt medical attention.
                Never diagnose definitively - only suggest possibilities.

                For emergency symptoms such as chest pain, difficulty breathing, severe bleeding, signs of stroke, etc., always mark as Emergency urgency.
            ";

            if (!empty($patientContext)) {
                $systemPrompt .= "\n\nAlso consider this patient context in your analysis:\n" . $patientContext;
            }

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->modelName,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $systemPrompt
                    ],
                    [
                        'role' => 'user',
                        'content' => "Analyze these symptoms carefully: {$symptoms}"
                    ]
                ],
                'temperature' => 0.3,
                'response_format' => ['type' => 'json_object'],
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Try to parse JSON response
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Add medical disclaimer
                    $data['disclaimer'] = 'This analysis is for informational purposes only and should not be considered medical advice. Always consult with a healthcare professional for proper diagnosis and treatment.';
                    return $data;
                }

                return [
                    'error' => 'Could not analyze symptoms properly. Please consult with a healthcare provider.',
                    'disclaimer' => 'This analysis is for informational purposes only and should not be considered medical advice. Always consult with a healthcare professional for proper diagnosis and treatment.'
                ];
            }

            return [
                'error' => 'Could not analyze symptoms at this time.',
                'disclaimer' => 'This analysis is for informational purposes only and should not be considered medical advice. Always consult with a healthcare professional for proper diagnosis and treatment.'
            ];
        } catch (\Exception $e) {
            Log::error('Symptom analysis failed', [
                'message' => $e->getMessage(),
                'symptoms' => $symptoms,
            ]);

            return [
                'error' => 'Could not analyze symptoms at this time.',
                'disclaimer' => 'This analysis is for informational purposes only and should not be considered medical advice. Always consult with a healthcare professional for proper diagnosis and treatment.'
            ];
        }
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing posts that don't have published_at set
        // Use created_at as the published_at value for consistency
        DB::statement("
            UPDATE social_contents 
            SET published_at = created_at 
            WHERE published_at IS NULL
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't reverse this - we want to keep the published_at values
        // This migration is for data consistency only
    }
};

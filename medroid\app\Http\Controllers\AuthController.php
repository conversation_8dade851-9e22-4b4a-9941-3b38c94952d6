<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use Laravel\Sanctum\PersonalAccessToken;
use App\Notifications\ProviderRegistrationNotification;
use App\Services\ReferralService;
use App\Services\UserActivityService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    protected $referralService;
    protected $userActivityService;
    protected $waitlistService;

    public function __construct(
        ReferralService $referralService,
        UserActivityService $userActivityService,
        \App\Services\WaitlistService $waitlistService
    ) {
        $this->referralService = $referralService;
        $this->userActivityService = $userActivityService;
        $this->waitlistService = $waitlistService;
    }

    public function register(Request $request)
    {
        // Check waitlist restrictions only if waitlist is enabled
        if ($this->waitlistService->isWaitlistEnabled()) {
            if (!$this->waitlistService->canBypassWaitlist($request->email)) {
                // Validate referral code for waitlist
                $referralValidation = $this->waitlistService->validateReferralCode($request->referral_code);

                if (!$referralValidation['valid']) {
                    return response()->json([
                        'errors' => ['referral_code' => [$referralValidation['message']]]
                    ], 422);
                }
            }
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:patient,provider,admin',
            'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
            'date_of_birth' => 'nullable|date',
            'device_token' => 'nullable|string',
            'device_type' => 'nullable|string|in:android,ios,web',
            'referral_code' => ($this->waitlistService->isWaitlistEnabled() && !$this->waitlistService->canBypassWaitlist($request->email)) ? 'required|string' : 'nullable|string',
        ]);

        // Check if email already exists
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            return response()->json(['errors' => ['email' => ['The email has already been taken.']]], 422);
        }

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Log waitlist activity
        $this->waitlistService->logWaitlistActivity('api_registration_attempt', [
            'email' => $request->email,
            'referral_code' => $request->referral_code,
            'has_referral_code' => !empty($request->referral_code),
            'device_type' => $request->device_type,
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'signup_source' => 'api_registration',
        ]);

        // Assign role
        $user->assignRole($request->role);

        if ($request->role === 'patient') {
            // Get default clinic for assignment
            $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
            if (!$defaultClinic) {
                $defaultClinic = Clinic::first(); // Fallback to any clinic
            }

            $patient = Patient::create([
                'user_id' => $user->id,
                'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                'gender' => $request->gender,
                'date_of_birth' => $request->date_of_birth,
                'health_history' => [],
                'allergies' => [],
                'preferences' => [
                    'feed_topics' => [],
                    'notification_settings' => [
                        'push' => true,
                        'email' => true
                    ]
                ],
                'appointment_preferences' => [
                    'preferred_location' => null,
                    'preferred_gender' => null,
                    'preferred_language' => null
                ],
            ]);

            // Fire Registered event to trigger email notification
            event(new Registered($user));
        } else if ($request->role === 'provider') {
            // Provider profile creation will be handled separately
            // Note: Provider registration notification will be sent when the provider profile is created
        }

        $tokenResult = $user->createToken('auth_token');
        $token = $tokenResult->plainTextToken;

        // Store device token if provided
        if ($request->has('device_token') && !empty($request->device_token)) {
            app(\App\Services\NotificationService::class)->storeDeviceToken(
                $user,
                $request->device_token,
                $request->device_type ?? 'android'
            );
        }

        // Process referral code if provided
        if ($request->has('referral_code') && !empty($request->referral_code)) {
            $referralCode = $request->referral_code;
            Log::info('Processing referral code during registration', [
                'user_id' => $user->id,
                'referral_code' => $referralCode
            ]);

            // First check if it's a founder referral code
            $clubService = app(\App\Services\ClubService::class);
            $isFounderCode = $clubService->checkFounderMembership($user, $referralCode);

            if (!$isFounderCode) {
                // If not a founder code, process as regular referral
                $referralResult = $this->referralService->processReferral($referralCode, $user);

                if ($referralResult) {
                    Log::info('Successfully processed referral during registration', [
                        'user_id' => $user->id,
                        'referral_code' => $referralCode
                    ]);
                } else {
                    Log::warning('Failed to process referral during registration', [
                        'user_id' => $user->id,
                        'referral_code' => $referralCode
                    ]);
                }
            } else {
                Log::info('Successfully processed founder code during registration', [
                    'user_id' => $user->id,
                    'founder_code' => $referralCode
                ]);
            }
        }

        return response()->json([
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_at' => now()->addDay()->toDateTimeString(),
        ], 201);
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
            'device_token' => 'nullable|string',
            'device_type' => 'nullable|string|in:android,ios,web',
            'user_agent' => 'nullable|string',
            'device_model' => 'nullable|string',
            'platform' => 'nullable|string',
            'app_version' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Find the user by email
        $user = User::where('email', $request->email)->first();

        // Check if user exists and password is correct
        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'message' => 'Invalid login details',
            ], 401);
        }

        // Check if user is active
        if (!$user->is_active) {
            $message = $user->hasRole('provider')
                ? 'Your account has been deactivated. Please contact support for assistance.'
                : 'Your account has been deactivated. Please contact support for assistance.';

            return response()->json([
                'message' => $message,
            ], 403);
        }

        // Update last login timestamp
        $user->last_login_at = now();
        $user->save();

        // Log user activity
        $this->userActivityService->logActivity($user, 'login');

        // Generate token that never expires for better persistence
        $tokenResult = $user->createToken('auth_token');
        $token = $tokenResult->plainTextToken;

        // Store device token if provided
        if ($request->has('device_token') && !empty($request->device_token)) {
            $userAgentService = app(\App\Services\UserAgentService::class);

            // Use Flutter-provided device info if available, otherwise parse from request
            $deviceInfo = [
                'user_agent' => $request->user_agent ?? $request->userAgent(),
                'browser' => $request->browser ?? $userAgentService->getBrowser($request->userAgent() ?? ''),
                'platform' => $request->platform ?? $userAgentService->getPlatform($request->userAgent() ?? ''),
                'ip_address' => $request->ip(),
                'device_model' => $request->device_model,
                'app_version' => $request->app_version,
            ];

            app(\App\Services\NotificationService::class)->storeDeviceToken(
                $user,
                $request->device_token,
                $request->device_type ?? $userAgentService->getNotificationDeviceType($request->userAgent() ?? ''),
                $deviceInfo
            );
        }

        // Ensure role is never null for API responses
        if (!$user->role) {
            $user->role = 'patient';
            $user->save();
        }

        return response()->json([
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ]);
    }

    public function logout(Request $request)
    {
        $user = $request->user();

        if ($user) {
            // Revoke current access token
            $request->user()->currentAccessToken()->delete();

            // Log user activity
            $this->userActivityService->logActivity($user, 'logout');
        }

        return response()->json([
            'message' => 'Successfully logged out',
        ]);
    }

    public function user(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        }

        // Ensure role is never null for API responses
        if (!$user->role) {
            $user->role = 'patient';
            $user->save();
        }

        if ($user->hasRole('patient')) {
            $user->load('patient');
        } else if ($user->hasRole('provider')) {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Handle SSO login/registration
     */
    public function ssoLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|string|in:google,apple',
            'provider_id' => 'required|string',
            'email' => 'required|string|email',
            'name' => 'required|string',
            'access_token' => 'nullable|string',
            'id_token' => 'nullable|string',
            'authorization_code' => 'nullable|string',
            'identity_token' => 'nullable|string',
            'nonce' => 'nullable|string',
            'profile_picture' => 'nullable|string',
            'device_token' => 'nullable|string',
            'device_type' => 'nullable|string|in:android,ios,web',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Check if user exists with this SSO provider
            $user = User::where('sso_provider', $request->provider)
                       ->where('sso_provider_id', $request->provider_id)
                       ->first();

            // If not found, check by email
            if (!$user) {
                $user = User::where('email', $request->email)->first();

                if ($user) {
                    // Link existing account with SSO
                    $user->update([
                        'sso_provider' => $request->provider,
                        'sso_provider_id' => $request->provider_id,
                        'sso_access_token' => $request->access_token,
                        'sso_profile_data' => [
                            'name' => $request->name,
                            'email' => $request->email,
                            'profile_picture' => $request->profile_picture,
                            'provider_data' => $request->only([
                                'id_token', 'authorization_code', 'identity_token', 'nonce'
                            ])
                        ]
                    ]);
                }
            }

            $needsAdditionalInfo = false;

            // Create new user if doesn't exist
            if (!$user) {
                // Check waitlist restrictions for new users only if waitlist is enabled
                if ($this->waitlistService->isWaitlistEnabled()) {
                    $googleSignupCheck = $this->waitlistService->canGoogleSignup($request->email);

                    if (!$googleSignupCheck['allowed']) {
                        return response()->json([
                            'success' => false,
                            'message' => $googleSignupCheck['message'],
                            'waitlist_restricted' => true,
                        ], 403);
                    }
                }

                // Log waitlist activity
                $this->waitlistService->logWaitlistActivity('sso_registration_attempt', [
                    'email' => $request->email,
                    'provider' => $request->provider,
                    'device_type' => $request->device_type,
                ]);

                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'role' => 'patient', // Always create as patient
                    'signup_source' => 'api_registration',
                    'sso_provider' => $request->provider,
                    'sso_provider_id' => $request->provider_id,
                    'sso_access_token' => $request->access_token,
                    'sso_profile_data' => [
                        'name' => $request->name,
                        'email' => $request->email,
                        'profile_picture' => $request->profile_picture,
                        'provider_data' => $request->only([
                            'id_token', 'authorization_code', 'identity_token', 'nonce'
                        ])
                    ]
                ]);

                // Assign patient role using Spatie permissions
                $user->assignRole('patient');

                // Get default clinic for assignment
                $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
                if (!$defaultClinic) {
                    $defaultClinic = Clinic::first(); // Fallback to any clinic
                }

                // Create patient profile
                $user->patient()->create([
                    'user_id' => $user->id,
                    'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                    'phone' => null,
                    'address' => null,
                    'emergency_contact' => null,
                    'medical_history' => null,
                    'allergies' => null,
                    'current_medications' => null,
                ]);

                // Initialize user credits
                $user->credit()->create([
                    'balance' => 0.00,
                    'total_earned' => 0.00,
                    'total_spent' => 0.00,
                ]);

                // Fire Registered event to trigger email notification
                event(new Registered($user));

                $needsAdditionalInfo = true;
            }

            // Update last login
            $user->last_login_at = now();
            $user->save();

            // Log user activity
            $this->userActivityService->logActivity($user, 'sso_login', [
                'provider' => $request->provider
            ]);

            // Generate token
            $tokenResult = $user->createToken('auth_token');
            $token = $tokenResult->plainTextToken;

            // Store device token if provided
            if ($request->has('device_token') && !empty($request->device_token)) {
                $userAgentService = app(\App\Services\UserAgentService::class);
                $deviceInfo = $userAgentService->parseUserAgent($request);

                app(\App\Services\NotificationService::class)->storeDeviceToken(
                    $user,
                    $request->device_token,
                    $request->device_type ?? $userAgentService->getNotificationDeviceType($request->userAgent() ?? ''),
                    $deviceInfo
                );
            }

            // Ensure role is never null for API responses
            if (!$user->role) {
                $user->role = 'patient';
                $user->save();
            }

            return response()->json([
                'success' => true,
                'access_token' => $token,
                'user' => $user->load(['patient', 'credit']),
                'needs_additional_info' => $needsAdditionalInfo,
            ]);

        } catch (\Exception $e) {
            \Log::error('SSO login error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'SSO authentication failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete SSO registration with additional details
     */
    public function ssoCompleteRegistration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'temp_token' => 'required|string',
            'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
            'date_of_birth' => 'nullable|date',
            'referral_code' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Verify the temporary token and get user
            $accessToken = PersonalAccessToken::findToken($request->temp_token);

            if (!$accessToken || ($accessToken->expires_at && $accessToken->expires_at->isPast())) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired token'
                ], 401);
            }

            $user = $accessToken->user;

            // Update patient profile with additional details
            $patientData = [];
            if ($request->gender) {
                $patientData['gender'] = $request->gender;
            }
            if ($request->date_of_birth) {
                $patientData['date_of_birth'] = $request->date_of_birth;
            }

            if (!empty($patientData)) {
                $user->patient()->update($patientData);
            }

            // Handle referral code
            if ($request->referral_code) {
                // First check if it's a founder referral code
                $clubService = app(\App\Services\ClubService::class);
                $isFounderCode = $clubService->checkFounderMembership($user, $request->referral_code);

                if (!$isFounderCode) {
                    // If not a founder code, check for regular referral
                    $referrer = User::where('referral_code', $request->referral_code)->first();
                    if ($referrer) {
                        $user->update(['referred_by' => $referrer->id]);

                        // Create referral record and award credits
                        app(\App\Services\ReferralService::class)->processReferral($request->referral_code, $user);
                    }
                }
            }

            // Generate new token
            $tokenResult = $user->createToken('auth_token');
            $token = $tokenResult->plainTextToken;

            // Revoke the temporary token
            $accessToken->delete();

            // Ensure role is never null for API responses
            if (!$user->role) {
                $user->role = 'patient';
                $user->save();
            }

            return response()->json([
                'success' => true,
                'access_token' => $token,
                'user' => $user->load(['patient', 'credit']),
            ]);

        } catch (\Exception $e) {
            \Log::error('SSO complete registration error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete registration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle SSO provider registration
     */
    public function ssoProviderRegistration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|string|in:google,apple',
            'provider_id' => 'required|string',
            'email' => 'required|string|email',
            'name' => 'required|string',
            'access_token' => 'nullable|string',
            'id_token' => 'nullable|string',
            'authorization_code' => 'nullable|string',
            'identity_token' => 'nullable|string',
            'nonce' => 'nullable|string',
            'profile_picture' => 'nullable|string',
            'device_token' => 'nullable|string',
            'device_type' => 'nullable|string|in:android,ios,web',
            'specialization' => 'required|string',
            'license_number' => 'required|string',
            'gender' => 'nullable|string|in:male,female,other',
            'bio' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Check if user already exists
            $existingUser = User::where('email', $request->email)->first();
            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User with this email already exists',
                ], 409);
            }

            // Check if SSO provider ID already exists
            $existingSSOUser = User::where('sso_provider', $request->provider)
                                 ->where('sso_provider_id', $request->provider_id)
                                 ->first();
            if ($existingSSOUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'This social account is already linked to another user',
                ], 409);
            }

            // Create provider user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'role' => 'provider',
                'signup_source' => 'api_registration',
                'sso_provider' => $request->provider,
                'sso_provider_id' => $request->provider_id,
                'sso_access_token' => $request->access_token,
                'email_verified_at' => now(),
                'sso_profile_data' => [
                    'name' => $request->name,
                    'email' => $request->email,
                    'profile_picture' => $request->profile_picture,
                    'provider_data' => $request->only([
                        'id_token', 'authorization_code', 'identity_token', 'nonce'
                    ])
                ]
            ]);

            // Assign provider role using Spatie permissions
            $user->assignRole('provider');

            // Get default clinic for assignment
            $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
            if (!$defaultClinic) {
                $defaultClinic = Clinic::first(); // Fallback to any clinic
            }

            // Create provider profile
            $provider = Provider::create([
                'user_id' => $user->id,
                'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                'specialization' => $request->specialization,
                'license_number' => $request->license_number,
                'verification_status' => 'verified', // Auto-verify SSO providers
                'verified_at' => now(),
                'gender' => $request->gender,
                'bio' => $request->bio,
                'rating' => 0,
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'absences' => [],
                'practice_locations' => [],
            ]);

            // Generate token
            $tokenResult = $user->createToken('auth_token');
            $token = $tokenResult->plainTextToken;

            // Store device token if provided
            if ($request->has('device_token') && !empty($request->device_token)) {
                app(\App\Services\NotificationService::class)->storeDeviceToken(
                    $user,
                    $request->device_token,
                    $request->device_type ?? 'android'
                );
            }

            return response()->json([
                'success' => true,
                'access_token' => $token,
                'user' => $user->load(['provider']),
                'message' => 'Provider registration successful',
            ]);

        } catch (\Exception $e) {
            \Log::error('SSO provider registration error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to register provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
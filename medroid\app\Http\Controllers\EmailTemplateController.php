<?php

namespace App\Http\Controllers;

use App\Models\EmailTemplate;
use App\Services\EmailTemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailTemplateController extends Controller
{
    protected $emailTemplateService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\EmailTemplateService  $emailTemplateService
     * @return void
     */
    public function __construct(EmailTemplateService $emailTemplateService)
    {
        $this->emailTemplateService = $emailTemplateService;

        // For testing purposes, temporarily disable authentication
        // $this->middleware('auth:api')->except(['getBySlug', 'index']);

        // // Use a closure middleware to check for the 'view settings' permission
        // $this->middleware(function ($request, $next) {
        //     $user = $request->user();
        //     if (!$user || !$user->can('view settings')) {
        //         return response()->json(['message' => 'Unauthorized. You need the view settings permission.'], 403);
        //     }
        //     return $next($request);
        // })->except(['getBySlug', 'index']);
    }
    /**
     * Display a listing of the email templates.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $templates = $this->emailTemplateService->getAllTemplates();
        return response()->json($templates);
    }

    /**
     * Display the specified email template.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $template = $this->emailTemplateService->getTemplateById($id);

        if (!$template) {
            return response()->json(['message' => 'Email template not found'], 404);
        }

        return response()->json($template);
    }

    /**
     * Store a newly created email template in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user has permission to edit settings
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized. You need the edit settings permission.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:email_templates',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $template = EmailTemplate::create($request->only([
                'name', 'slug', 'subject', 'content', 'description', 'is_active'
            ]));

            return response()->json([
                'message' => 'Email template created successfully',
                'template' => $template,
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create email template', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to create email template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the specified email template in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Check if user has permission to edit settings
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized. You need the edit settings permission.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $template = $this->emailTemplateService->updateTemplate($id, $request->only([
                'subject', 'content', 'is_active'
            ]));

            if (!$template) {
                return response()->json(['message' => 'Email template not found'], 404);
            }

            return response()->json([
                'message' => 'Email template updated successfully',
                'template' => $template,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update email template', [
                'id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to update email template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified email template from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        // Check if user has permission to edit settings
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized. You need the edit settings permission.'], 403);
        }

        try {
            $template = EmailTemplate::find($id);

            if (!$template) {
                return response()->json(['message' => 'Email template not found'], 404);
            }

            $template->delete();

            return response()->json([
                'message' => 'Email template deleted successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete email template', [
                'id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to delete email template',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get an email template by its slug.
     *
     * @param  string  $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBySlug($slug)
    {
        $template = EmailTemplate::where('slug', $slug)->first();

        if (!$template) {
            return response()->json(['message' => 'Email template not found'], 404);
        }

        return response()->json($template);
    }

    /**
     * Preview an email template with test data.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function preview(Request $request, $id)
    {
        try {
            $template = $this->emailTemplateService->getTemplateById($id);

            if (!$template) {
                // Return a friendly error message as HTML content
                return response()->json([
                    'subject' => 'Template Not Found',
                    'content' => '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                        <h3>Template Not Found</h3>
                        <p>The requested email template (ID: ' . htmlspecialchars($id) . ') could not be found.</p>
                    </div>',
                ]);
            }

            // Get test data from request or use default test data
            $testData = $request->input('test_data', $this->getDefaultTestData($template->slug));

            // Log the test data for debugging
            Log::debug('Email template preview test data', [
                'template_id' => $id,
                'template_slug' => $template->slug,
                'test_data' => $testData
            ]);

            // Render the template with test data
            $rendered = $this->emailTemplateService->renderTemplate($template->slug, $testData);

            // The renderTemplate method now always returns an array with subject and content
            return response()->json([
                'subject' => $rendered['subject'],
                'content' => $rendered['content'],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to preview email template', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Return the error as HTML content
            return response()->json([
                'subject' => 'Error Previewing Template',
                'content' => '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px 0;">
                    <h3>Error Previewing Template</h3>
                    <p>' . htmlspecialchars($e->getMessage()) . '</p>
                    <p>Please check the template syntax and make sure all required variables are provided.</p>
                </div>',
            ]);
        }
    }

    /**
     * Send a test email to the specified email address.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTestEmail(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $template = $this->emailTemplateService->getTemplateById($id);

            if (!$template) {
                return response()->json(['message' => 'Email template not found'], 404);
            }

            // Get test data
            $testData = $this->getDefaultTestData($template->slug);

            // Render the template
            $rendered = $this->emailTemplateService->renderTemplate($template->slug, $testData);

            // Send the email
            $recipientEmail = $request->input('email');

            try {
                // Log the attempt
                Log::info('Attempting to send test email', [
                    'recipient' => $recipientEmail,
                    'template_id' => $id,
                    'template_name' => $template->name,
                    'mail_config' => [
                        'host' => config('mail.mailers.smtp.host'),
                        'port' => config('mail.mailers.smtp.port'),
                        'encryption' => config('mail.mailers.smtp.encryption'),
                        'username' => config('mail.mailers.smtp.username'),
                        'from_address' => config('mail.from.address')
                    ]
                ]);

                // Use the simplest approach possible
                $subject = 'Test Email from Medroid';
                $content = 'This is a test email from Medroid App to verify SMTP connection.';

                // Send a simple test email
                Mail::raw($content, function ($message) use ($recipientEmail, $subject) {
                    $message->to($recipientEmail)
                        ->from(config('mail.from.address'), config('mail.from.name'))
                        ->subject($subject);
                });

                // Log successful email sending
                Log::info('Test email sent successfully', [
                    'recipient' => $recipientEmail,
                    'template_id' => $id,
                    'template_name' => $template->name
                ]);

                return response()->json([
                    'message' => 'Test email sent successfully to ' . $recipientEmail,
                    'success' => true
                ]);
            } catch (\Exception $e) {
                // Log detailed error information
                Log::error('SMTP Error Details', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'mail_config' => [
                        'host' => config('mail.mailers.smtp.host'),
                        'port' => config('mail.mailers.smtp.port'),
                        'encryption' => config('mail.mailers.smtp.encryption'),
                        'username' => config('mail.mailers.smtp.username'),
                        'from_address' => config('mail.from.address')
                    ]
                ]);

                // Return a more detailed error message
                return response()->json([
                    'message' => 'Failed to send test email: ' . $e->getMessage(),
                    'success' => false
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send test email', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => 'Failed to send test email: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Test email configuration and send a test email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testEmailConfiguration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $recipientEmail = $request->input('email');
            $currentMailer = config('mail.default');

            // Log the email configuration
            Log::info('Testing email configuration', [
                'recipient' => $recipientEmail,
                'mail_config' => [
                    'mailer' => $currentMailer,
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'username' => config('mail.mailers.smtp.username'),
                    'from_address' => config('mail.from.address'),
                    'from_name' => config('mail.from.name')
                ]
            ]);

            // Test basic email sending
            $subject = 'Medroid Email Configuration Test';
            $content = 'This is a test email to verify that the Medroid email configuration is working correctly. If you receive this email, the email settings are properly configured.';

            Mail::raw($content, function ($message) use ($recipientEmail, $subject) {
                $message->to($recipientEmail)
                    ->from(config('mail.from.address'), config('mail.from.name'))
                    ->subject($subject);
            });

            Log::info('Email configuration test successful', [
                'recipient' => $recipientEmail,
                'mailer' => $currentMailer
            ]);

            $message = $currentMailer === 'log'
                ? 'Email test completed successfully. Email was logged (check storage/logs/laravel.log for details).'
                : 'Test email sent successfully to ' . $recipientEmail;

            return response()->json([
                'success' => true,
                'message' => $message,
                'config' => [
                    'mailer' => $currentMailer,
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'from' => config('mail.from.address'),
                    'note' => $currentMailer === 'log' ? 'Using log driver - emails are logged instead of sent' : 'Using SMTP driver'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Email configuration test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'mail_config' => [
                    'mailer' => config('mail.default'),
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'username' => config('mail.mailers.smtp.username'),
                    'from_address' => config('mail.from.address')
                ]
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage(),
                'error_details' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get default test data for a template based on its slug.
     *
     * @param  string  $slug
     * @return array
     */
    private function getDefaultTestData($slug)
    {
        $commonData = [
            'user' => (object)[
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
        ];

        // Common appointment data for all appointment-related templates
        $appointmentData = [
            'appointment' => (object)[
                'reason' => 'Annual checkup',
                'is_telemedicine' => true,
                'id' => 12345,
            ],
            'patient' => (object)[
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
            'provider' => (object)[
                'name' => 'Dr. Jane Smith',
                'email' => '<EMAIL>', // Keep this as test email
            ],
            'date' => 'Monday, June 1, 2023',
            'startTime' => '10:00 AM',
            'endTime' => '10:30 AM',
            'appointmentStatus' => 'scheduled',
            'paymentStatus' => 'pending',
            'reason' => 'Consultation needed',
            'payment' => (object)[
                'amount' => 150.00,
                'paid_at' => now(),
                'payment_method_type' => 'credit_card',
                'payment_id' => 'pi_' . substr(md5(uniqid()), 0, 24),
            ],
            'resetUrl' => env('FRONTEND_URL', 'https://app.medroid.ai') . '/reset-password?token=sample-token',
        ];

        switch ($slug) {
            case 'appointment-booked-patient':
            case 'appointment-confirmed-patient':
            case 'appointment-cancelled-patient':
            case 'appointment-reminder-patient':
            case 'appointment-booked-provider':
            case 'appointment-confirmed-provider':
            case 'appointment-cancelled-provider':
            case 'appointment-reminder-provider':
            case 'appointment-rescheduled-patient':
            case 'appointment-rescheduled-provider':
                return array_merge($commonData, $appointmentData);

            case 'password-reset':
                return array_merge($commonData, [
                    'resetUrl' => env('FRONTEND_URL', 'https://app.medroid.ai') . '/reset-password?token=sample-token',
                ]);

            case 'referral-invitation':
                return array_merge($commonData, [
                    'referralCode' => 'FRIEND123', // Add referral code for the template preview
                ]);

            default:
                return $commonData;
        }
    }
}

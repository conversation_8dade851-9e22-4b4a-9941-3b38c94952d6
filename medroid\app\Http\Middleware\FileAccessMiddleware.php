<?php

namespace App\Http\Middleware;

use App\Models\File;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class FileAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Allow access if user is authenticated
        if (!$user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        // For file-specific routes, check file ownership
        if ($request->route('id')) {
            $fileId = $request->route('id');
            $file = File::find($fileId);

            if (!$file) {
                return response()->json(['message' => 'File not found'], 404);
            }

            // Check if user owns the file or is admin
            if ($file->user_id !== $user->id && !$user->hasRole('admin')) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            // Store file in request for controller use
            $request->attributes->set('file', $file);
        }

        return $next($request);
    }
}

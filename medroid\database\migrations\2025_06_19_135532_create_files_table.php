<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Display name for the file
            $table->string('original_name'); // Original filename when uploaded
            $table->string('path'); // Storage path
            $table->string('disk')->default('public'); // Storage disk (public, s3, etc.)
            $table->string('mime_type');
            $table->bigInteger('size'); // File size in bytes
            $table->string('category')->default('general'); // File category
            $table->string('extension', 10);
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional file metadata
            $table->boolean('is_public')->default(false); // Whether file can be publicly accessed
            $table->timestamp('last_accessed_at')->nullable();
            $table->integer('download_count')->default(0);
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'category']);
            $table->index(['user_id', 'created_at']);
            $table->index('mime_type');
            $table->index('category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};

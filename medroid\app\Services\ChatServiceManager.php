<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Services\Contracts\ChatServiceInterface;
use Illuminate\Support\Facades\Log;

class ChatServiceManager implements ChatServiceInterface
{
    protected $primaryService;
    protected $fallbackService;
    protected $retryAttempts;

    public function __construct()
    {
        $this->initializeServices();
        $this->retryAttempts = config('services.chat.retry_attempts', 3);
    }

    /**
     * Initialize primary and fallback services
     */
    protected function initializeServices()
    {
        $primaryProvider = config('services.chat.primary_provider', 'nscale');
        $fallbackProvider = config('services.chat.fallback_provider', 'groq');

        $this->primaryService = $this->createService($primaryProvider);
        $this->fallbackService = $this->createService($fallbackProvider);

        Log::info('Chat Service Manager initialized', [
            'primary_service' => $this->primaryService->getServiceName(),
            'fallback_service' => $this->fallbackService->getServiceName(),
        ]);
    }

    /**
     * Create service instance based on provider name
     */
    protected function createService($provider)
    {
        switch ($provider) {
            case 'nscale':
                return new NScaleService();
            case 'groq':
                return new GroqService();
            default:
                throw new \InvalidArgumentException("Unknown chat service provider: {$provider}");
        }
    }

    /**
     * Execute method with primary service and fallback on failure
     */
    protected function executeWithFallback($method, $args = [])
    {
        // Try primary service first
        try {
            if ($this->primaryService->isHealthy()) {
                Log::debug("Executing {$method} with primary service", [
                    'service' => $this->primaryService->getServiceName()
                ]);
                
                $result = call_user_func_array([$this->primaryService, $method], $args);
                
                // Log successful primary service usage
                Log::info("Primary service executed successfully", [
                    'method' => $method,
                    'service' => $this->primaryService->getServiceName()
                ]);

                // Add service indicator to result if it's an array
                if (is_array($result)) {
                    $result['used_service'] = $this->primaryService->getServiceName();
                    $result['used_fallback'] = false;
                }

                return $result;
            } else {
                Log::warning("Primary service is not healthy, switching to fallback", [
                    'primary_service' => $this->primaryService->getServiceName(),
                    'fallback_service' => $this->fallbackService->getServiceName()
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Primary service failed, switching to fallback", [
                'method' => $method,
                'primary_service' => $this->primaryService->getServiceName(),
                'fallback_service' => $this->fallbackService->getServiceName(),
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to secondary service
        try {
            Log::info("Executing {$method} with fallback service", [
                'service' => $this->fallbackService->getServiceName()
            ]);
            
            $result = call_user_func_array([$this->fallbackService, $method], $args);
            
            // Add fallback indicator to result if it's an array
            if (is_array($result)) {
                $result['used_service'] = $this->fallbackService->getServiceName();
                $result['used_fallback'] = true;
                $result['fallback_service'] = $this->fallbackService->getServiceName();
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Both primary and fallback services failed", [
                'method' => $method,
                'primary_service' => $this->primaryService->getServiceName(),
                'fallback_service' => $this->fallbackService->getServiceName(),
                'error' => $e->getMessage()
            ]);

            // Return error response
            return [
                'message' => 'I apologize, but our AI Doctor service is currently unavailable. Please try again later or contact support.',
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'service_error' => true,
                'error_details' => 'Both primary and fallback services failed',
            ];
        }
    }

    /**
     * Generate a medical consultation response
     */
    public function generateMedicalConsultation(ChatConversation $conversation, $includePatientContext = true, $additionalContext = '', $hasRecentAppointment = false)
    {
        return $this->executeWithFallback('generateMedicalConsultation', [
            $conversation, 
            $includePatientContext, 
            $additionalContext, 
            $hasRecentAppointment
        ]);
    }

    /**
     * Detect appointment booking intent from message context
     */
    public function detectAppointmentBookingIntent($message, $conversationContext = [])
    {
        return $this->executeWithFallback('detectAppointmentBookingIntent', [
            $message, 
            $conversationContext
        ]);
    }

    /**
     * Generate a title for a conversation
     */
    public function generateTitle($prompt)
    {
        return $this->executeWithFallback('generateTitle', [$prompt]);
    }

    /**
     * Generate an appointment request to connect with a real doctor
     */
    public function generateAppointmentRequest($patientInfo, $symptoms, $preferredTiming = null)
    {
        return $this->executeWithFallback('generateAppointmentRequest', [
            $patientInfo, 
            $symptoms, 
            $preferredTiming
        ]);
    }

    /**
     * Get emergency services information based on user's location
     */
    public function getEmergencyServices($location = null)
    {
        return $this->executeWithFallback('getEmergencyServices', [$location]);
    }

    /**
     * Generate specialized exam question analysis
     */
    public function analyzeExamQuestion($question, $examType, $options = [])
    {
        return $this->executeWithFallback('analyzeExamQuestion', [
            $question, 
            $examType, 
            $options
        ]);
    }

    /**
     * Generate clinical reasoning transparency
     */
    public function generateClinicalReasoning(ChatConversation $conversation, $mode = 'consultation')
    {
        return $this->executeWithFallback('generateClinicalReasoning', [
            $conversation, 
            $mode
        ]);
    }

    /**
     * Get the current active service name
     */
    public function getServiceName()
    {
        return "ChatServiceManager (Primary: {$this->primaryService->getServiceName()}, Fallback: {$this->fallbackService->getServiceName()})";
    }

    /**
     * Check if any service is available/healthy
     */
    public function isHealthy()
    {
        return $this->primaryService->isHealthy() || $this->fallbackService->isHealthy();
    }

    /**
     * Get configuration for both services
     */
    public function getConfig()
    {
        return [
            'manager' => 'ChatServiceManager',
            'primary_service' => $this->primaryService->getConfig(),
            'fallback_service' => $this->fallbackService->getConfig(),
            'retry_attempts' => $this->retryAttempts,
        ];
    }

    /**
     * Get primary service instance (for direct access if needed)
     */
    public function getPrimaryService()
    {
        return $this->primaryService;
    }

    /**
     * Get fallback service instance (for direct access if needed)
     */
    public function getFallbackService()
    {
        return $this->fallbackService;
    }

    /**
     * Force switch to fallback service (for testing or manual override)
     */
    public function switchToFallback()
    {
        Log::info('Manually switching to fallback service', [
            'primary_service' => $this->primaryService->getServiceName(),
            'fallback_service' => $this->fallbackService->getServiceName()
        ]);

        // Swap the services
        $temp = $this->primaryService;
        $this->primaryService = $this->fallbackService;
        $this->fallbackService = $temp;
    }

    /**
     * Get service health status
     */
    public function getHealthStatus()
    {
        return [
            'primary_service' => [
                'name' => $this->primaryService->getServiceName(),
                'healthy' => $this->primaryService->isHealthy(),
                'config' => $this->primaryService->getConfig(),
            ],
            'fallback_service' => [
                'name' => $this->fallbackService->getServiceName(),
                'healthy' => $this->fallbackService->isHealthy(),
                'config' => $this->fallbackService->getConfig(),
            ],
            'overall_healthy' => $this->isHealthy(),
        ];
    }

    /**
     * Generate simple content using the chat service
     * This is a wrapper for simple content generation tasks
     */
    public function generateContent($systemPrompt, $userPrompt, $options = [])
    {
        return $this->executeWithFallback('generateSimpleContent', [
            $systemPrompt,
            $userPrompt,
            $options
        ]);
    }
}

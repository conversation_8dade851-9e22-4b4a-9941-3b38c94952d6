<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie', 'oauth/*', 'storage/*', 'storage/*/*', 'build/*', 'build/*/*'],

    'allowed_methods' => ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],

    'allowed_origins' => [
        'https://app.medroid.ai',
        'http://localhost:8000', // Keep for local development
        'https://api.medroid.ai', // Keep for local development
        'http://localhost:8000', // Keep for local development        'http://127.0.0.1:8000', // Keep for local development
    ],

    'allowed_origins_patterns' => [
        // Allow any port on localhost for development
        '/^http:\/\/localhost:\d+$/',
        '/^http:\/\/127\.0\.0\.1:\d+$/',
        '/^https:\/\/localhost:\d+$/',
        '/^https:\/\/127\.0\.0\.1:\d+$/',
        // Also allow common development patterns
        '/^http:\/\/0\.0\.0\.0:\d+$/',
        '/^https:\/\/0\.0\.0\.0:\d+$/',
        // Allow Flutter web development server patterns
        '/^http:\/\/localhost:\d{4,5}$/',
        '/^http:\/\/127\.0\.0\.1:\d{4,5}$/',
        // Allow Cloudflare Tunnel patterns
        '/^https:\/\/.*\.trycloudflare\.com$/',
        '/^https:\/\/.*\.cloudflareaccess\.com$/',
        // Allow Medroid domain patterns
        '/^https:\/\/.*\.medroid\.ai$/',
    ],

    'allowed_headers' => ['X-Requested-With', 'Content-Type', 'X-Token-Auth', 'Authorization', 'X-CSRF-TOKEN', 'X-XSRF-TOKEN', 'Accept'],

    'exposed_headers' => [],

    'max_age' => 86400,

    'supports_credentials' => true,

];

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StaticAssetCors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the response first
        $response = $next($request);

        // Check if this is a request from frontend
        $origin = $request->headers->get('Origin');
        $frontendUrl = env('FRONTEND_URL', 'https://app.medroid.ai');
        $userAgent = $request->headers->get('User-Agent', '');

        // Check if this is a social media crawler
        $isSocialCrawler = $this->isSocialMediaCrawler($userAgent);

        // Check if this is an image request
        $isImageRequest = $this->isImageRequest($request);

        if ($origin === $frontendUrl || $isSocialCrawler || $isImageRequest) {
            // Add CORS headers to the response
            $response->headers->set('Access-Control-Allow-Origin', $origin ?: '*');
            $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE');
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN');
            $response->headers->set('Access-Control-Allow-Credentials', 'true');
            $response->headers->set('Access-Control-Max-Age', '86400');

            // Add specific headers for social media crawlers
            if ($isSocialCrawler || $isImageRequest) {
                $response->headers->set('Cache-Control', 'public, max-age=3600');
                $response->headers->set('X-Robots-Tag', 'index, follow');

                // For image requests, add specific image headers
                if ($isImageRequest) {
                    $response->headers->set('Content-Disposition', 'inline');
                    $response->headers->set('X-Content-Type-Options', 'nosniff');
                }
            }
        }

        return $response;
    }
    
    /**
     * Check if the request is from a social media crawler
     */
    private function isSocialMediaCrawler(string $userAgent): bool
    {
        $crawlers = [
            'facebookexternalhit',
            'Facebot',
            'Twitterbot',
            'LinkedInBot',
            'WhatsApp',
            'TelegramBot',
            'SkypeUriPreview',
            'SlackBot',
            'DiscordBot',
            'GoogleBot',
            'bingbot',
            'YandexBot',
            'PinterestBot',
            'redditbot',
            'SnapchatAdsBot',
            'InstagramBot',
            'TikTokBot'
        ];

        foreach ($crawlers as $crawler) {
            if (stripos($userAgent, $crawler) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the request is for an image
     */
    private function isImageRequest(Request $request): bool
    {
        $path = $request->getPathInfo();
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        $imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp', 'ico'];

        return in_array(strtolower($extension), $imageExtensions);
    }

    /**
     * Check if the request is for a static asset
     */
    private function isStaticAssetRequest(Request $request): bool
    {
        $path = $request->getPathInfo();

        // Check if it's a build asset
        if (str_starts_with($path, '/build/assets/')) {
            return true;
        }

        // Check if it's a static file by extension
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        $staticExtensions = ['js', 'css', 'map', 'woff', 'woff2', 'ttf', 'eot', 'svg', 'png', 'jpg', 'jpeg', 'gif', 'ico'];

        return in_array(strtolower($extension), $staticExtensions);
    }
}

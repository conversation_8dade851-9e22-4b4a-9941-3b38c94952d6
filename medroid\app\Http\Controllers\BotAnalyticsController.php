<?php

namespace App\Http\Controllers;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Models\ImageGenerationLog;
use App\Services\PostingScheduleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BotAnalyticsController extends Controller
{
    private PostingScheduleService $scheduleService;

    public function __construct(PostingScheduleService $scheduleService)
    {
        $this->scheduleService = $scheduleService;
    }

    /**
     * Get comprehensive bot analytics dashboard data
     */
    public function getDashboardData(Request $request)
    {
        try {
            $timeframe = $request->get('timeframe', '30'); // days
            $startDate = now()->subDays($timeframe);

            $data = [
                'overview' => $this->getOverviewStats($startDate),
                'performance' => $this->getPerformanceMetrics($startDate),
                'posting_patterns' => $this->getPostingPatterns($startDate),
                'content_quality' => $this->getContentQualityMetrics($startDate),
                'persona_rankings' => $this->getPersonaRankings($startDate),
                'system_health' => $this->getSystemHealth(),
                'recommendations' => $this->getRecommendations(),
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timeframe' => $timeframe,
                'generated_at' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating bot analytics dashboard', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate analytics data',
            ], 500);
        }
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(\Carbon\Carbon $startDate): array
    {
        return [
            'total_personas' => BotPersona::count(),
            'active_personas' => BotPersona::active()->count(),
            'total_posts' => AutomatedPost::where('created_at', '>=', $startDate)->count(),
            'published_posts' => AutomatedPost::where('posted_at', '>=', $startDate)->count(),
            'failed_posts' => AutomatedPost::where('status', 'failed')
                ->where('updated_at', '>=', $startDate)->count(),
            'success_rate' => $this->calculateSuccessRate($startDate),
            'avg_posts_per_day' => $this->getAveragePostsPerDay($startDate),
            'total_engagement' => $this->getTotalEngagement($startDate),
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(\Carbon\Carbon $startDate): array
    {
        $posts = AutomatedPost::where('posted_at', '>=', $startDate)
            ->where('status', 'posted')
            ->get();

        $totalEngagement = 0;
        $totalLikes = 0;
        $totalComments = 0;
        $totalShares = 0;

        foreach ($posts as $post) {
            $engagement = $post->engagement_data ?? [];
            $totalLikes += $engagement['likes'] ?? 0;
            $totalComments += $engagement['comments'] ?? 0;
            $totalShares += $engagement['shares'] ?? 0;
        }

        $totalEngagement = $totalLikes + $totalComments + $totalShares;
        $postCount = $posts->count();

        return [
            'total_engagement' => $totalEngagement,
            'total_likes' => $totalLikes,
            'total_comments' => $totalComments,
            'total_shares' => $totalShares,
            'avg_engagement_per_post' => $postCount > 0 ? round($totalEngagement / $postCount, 2) : 0,
            'avg_likes_per_post' => $postCount > 0 ? round($totalLikes / $postCount, 2) : 0,
            'avg_comments_per_post' => $postCount > 0 ? round($totalComments / $postCount, 2) : 0,
            'engagement_rate' => $this->calculateEngagementRate($posts),
        ];
    }

    /**
     * Get posting patterns analysis
     */
    private function getPostingPatterns(\Carbon\Carbon $startDate): array
    {
        $posts = AutomatedPost::where('posted_at', '>=', $startDate)
            ->where('status', 'posted')
            ->get();

        $hourlyDistribution = [];
        $dailyDistribution = [];
        $weeklyTrend = [];

        foreach ($posts as $post) {
            $hour = $post->posted_at->hour;
            $day = $post->posted_at->format('l');
            $week = $post->posted_at->format('Y-W');

            $hourlyDistribution[$hour] = ($hourlyDistribution[$hour] ?? 0) + 1;
            $dailyDistribution[$day] = ($dailyDistribution[$day] ?? 0) + 1;
            $weeklyTrend[$week] = ($weeklyTrend[$week] ?? 0) + 1;
        }

        return [
            'hourly_distribution' => $hourlyDistribution,
            'daily_distribution' => $dailyDistribution,
            'weekly_trend' => $weeklyTrend,
            'peak_hours' => $this->findPeakHours($hourlyDistribution),
            'optimal_days' => $this->findOptimalDays($dailyDistribution),
        ];
    }

    /**
     * Get content quality metrics
     */
    private function getContentQualityMetrics(\Carbon\Carbon $startDate): array
    {
        $posts = AutomatedPost::where('created_at', '>=', $startDate)->get();
        
        $qualityScores = [];
        $avgCaptionLength = 0;
        $avgHashtagCount = 0;
        $contentTypes = [];

        foreach ($posts as $post) {
            $captionLength = strlen($post->caption);
            $hashtagCount = count($post->hashtags ?? []);
            
            $avgCaptionLength += $captionLength;
            $avgHashtagCount += $hashtagCount;
            
            // Analyze content type based on persona
            $contentFocus = $post->botPersona->content_focus ?? 'general';
            $contentTypes[$contentFocus] = ($contentTypes[$contentFocus] ?? 0) + 1;
        }

        $postCount = $posts->count();

        return [
            'avg_caption_length' => $postCount > 0 ? round($avgCaptionLength / $postCount) : 0,
            'avg_hashtag_count' => $postCount > 0 ? round($avgHashtagCount / $postCount, 1) : 0,
            'content_type_distribution' => $contentTypes,
            'image_generation_success_rate' => $this->getImageGenerationSuccessRate($startDate),
            'validation_pass_rate' => $this->getValidationPassRate($startDate),
        ];
    }

    /**
     * Get persona performance rankings
     */
    private function getPersonaRankings(\Carbon\Carbon $startDate): array
    {
        $personas = BotPersona::with(['automatedPosts' => function ($query) use ($startDate) {
            $query->where('posted_at', '>=', $startDate)->where('status', 'posted');
        }])->get();

        $rankings = [];

        foreach ($personas as $persona) {
            $posts = $persona->automatedPosts;
            $totalEngagement = 0;
            $postCount = $posts->count();

            foreach ($posts as $post) {
                $engagement = $post->engagement_data ?? [];
                $totalEngagement += ($engagement['likes'] ?? 0) + 
                                  ($engagement['comments'] ?? 0) + 
                                  ($engagement['shares'] ?? 0);
            }

            $rankings[] = [
                'persona_id' => $persona->id,
                'name' => $persona->full_name,
                'handle' => $persona->handle,
                'post_count' => $postCount,
                'total_engagement' => $totalEngagement,
                'avg_engagement' => $postCount > 0 ? round($totalEngagement / $postCount, 2) : 0,
                'content_focus' => $persona->content_focus,
                'success_rate' => $this->getPersonaSuccessRate($persona, $startDate),
            ];
        }

        // Sort by average engagement
        usort($rankings, function ($a, $b) {
            return $b['avg_engagement'] <=> $a['avg_engagement'];
        });

        return array_slice($rankings, 0, 10); // Top 10
    }

    /**
     * Get system health metrics
     */
    private function getSystemHealth(): array
    {
        return [
            'queue_health' => $this->getQueueHealth(),
            'image_generation_health' => $this->getImageGenerationHealth(),
            'error_rate' => $this->getErrorRate(),
            'recent_failures' => $this->getRecentFailures(),
            'system_load' => $this->getSystemLoad(),
        ];
    }

    /**
     * Get recommendations for optimization
     */
    private function getRecommendations(): array
    {
        $analysis = $this->scheduleService->analyzePostingPatterns();
        return $analysis['recommendations'] ?? [];
    }

    // Helper methods

    private function calculateSuccessRate(\Carbon\Carbon $startDate): float
    {
        $totalPosts = AutomatedPost::where('created_at', '>=', $startDate)->count();
        $successfulPosts = AutomatedPost::where('posted_at', '>=', $startDate)->count();
        
        return $totalPosts > 0 ? round(($successfulPosts / $totalPosts) * 100, 2) : 0;
    }

    private function getAveragePostsPerDay(\Carbon\Carbon $startDate): float
    {
        $days = now()->diffInDays($startDate);
        $totalPosts = AutomatedPost::where('posted_at', '>=', $startDate)->count();
        
        return $days > 0 ? round($totalPosts / $days, 2) : 0;
    }

    private function getTotalEngagement(\Carbon\Carbon $startDate): int
    {
        $posts = AutomatedPost::where('posted_at', '>=', $startDate)
            ->where('status', 'posted')
            ->get();

        $total = 0;
        foreach ($posts as $post) {
            $engagement = $post->engagement_data ?? [];
            $total += ($engagement['likes'] ?? 0) + 
                     ($engagement['comments'] ?? 0) + 
                     ($engagement['shares'] ?? 0);
        }

        return $total;
    }

    private function calculateEngagementRate($posts): float
    {
        if ($posts->isEmpty()) return 0;

        $totalEngagement = 0;
        $totalFollowers = 0;

        foreach ($posts as $post) {
            $engagement = $post->engagement_data ?? [];
            $postEngagement = ($engagement['likes'] ?? 0) + 
                            ($engagement['comments'] ?? 0) + 
                            ($engagement['shares'] ?? 0);
            
            $totalEngagement += $postEngagement;
            $totalFollowers += $post->botUser->follower_count ?? 1000; // Default follower count
        }

        return $totalFollowers > 0 ? round(($totalEngagement / $totalFollowers) * 100, 2) : 0;
    }

    private function findPeakHours(array $hourlyDistribution): array
    {
        arsort($hourlyDistribution);
        return array_slice(array_keys($hourlyDistribution), 0, 3, true);
    }

    private function findOptimalDays(array $dailyDistribution): array
    {
        arsort($dailyDistribution);
        return array_slice(array_keys($dailyDistribution), 0, 3, true);
    }

    private function getImageGenerationSuccessRate(\Carbon\Carbon $startDate): float
    {
        $totalGenerations = ImageGenerationLog::where('created_at', '>=', $startDate)->count();
        $successfulGenerations = ImageGenerationLog::where('created_at', '>=', $startDate)
            ->where('status', 'completed')->count();
        
        return $totalGenerations > 0 ? round(($successfulGenerations / $totalGenerations) * 100, 2) : 0;
    }

    private function getValidationPassRate(\Carbon\Carbon $startDate): float
    {
        $totalPosts = AutomatedPost::where('created_at', '>=', $startDate)->count();
        $failedValidation = AutomatedPost::where('created_at', '>=', $startDate)
            ->where('status', 'failed')
            ->where('error_message', 'like', '%validation%')->count();
        
        return $totalPosts > 0 ? round((($totalPosts - $failedValidation) / $totalPosts) * 100, 2) : 0;
    }

    private function getPersonaSuccessRate(BotPersona $persona, \Carbon\Carbon $startDate): float
    {
        $totalPosts = $persona->automatedPosts()->where('created_at', '>=', $startDate)->count();
        $successfulPosts = $persona->automatedPosts()
            ->where('posted_at', '>=', $startDate)->count();
        
        return $totalPosts > 0 ? round(($successfulPosts / $totalPosts) * 100, 2) : 0;
    }

    private function getQueueHealth(): array
    {
        // This would integrate with your queue monitoring
        return [
            'status' => 'healthy',
            'pending_jobs' => 0,
            'failed_jobs' => 0,
        ];
    }

    private function getImageGenerationHealth(): array
    {
        $pending = ImageGenerationLog::where('status', 'pending')->count();
        $processing = ImageGenerationLog::where('status', 'processing')->count();
        
        return [
            'status' => ($pending + $processing) < 10 ? 'healthy' : 'busy',
            'pending' => $pending,
            'processing' => $processing,
        ];
    }

    private function getErrorRate(): float
    {
        $totalPosts = AutomatedPost::where('created_at', '>=', now()->subDay())->count();
        $failedPosts = AutomatedPost::where('status', 'failed')
            ->where('updated_at', '>=', now()->subDay())->count();
        
        return $totalPosts > 0 ? round(($failedPosts / $totalPosts) * 100, 2) : 0;
    }

    private function getRecentFailures(): array
    {
        return AutomatedPost::where('status', 'failed')
            ->where('updated_at', '>=', now()->subHours(24))
            ->with('botPersona:id,first_name,last_name')
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($post) {
                return [
                    'persona' => $post->botPersona->full_name,
                    'error' => $post->error_message,
                    'time' => $post->updated_at->diffForHumans(),
                ];
            })
            ->toArray();
    }

    private function getSystemLoad(): array
    {
        return [
            'cpu_usage' => 'N/A', // Would integrate with system monitoring
            'memory_usage' => 'N/A',
            'disk_usage' => 'N/A',
        ];
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Provider;
use App\Models\Clinic;
use App\Notifications\ProviderRegistrationNotification;
use Inertia\Inertia;

class ProviderRegistrationController extends Controller
{
    /**
     * Show provider registration page (Web route).
     *
     * @return \Inertia\Response
     */
    public function showRegistrationPage()
    {
        return Inertia::render('ProviderRegister', [
            'specializations' => [
                'General Practice',
                'Cardiology',
                'Dermatology',
                'Endocrinology',
                'Gastroenterology',
                'Neurology',
                'Oncology',
                'Orthopedics',
                'Pediatrics',
                'Psychiatry',
                'Radiology',
                'Surgery',
                'Other'
            ],
            'languages' => [
                'English',
                'Spanish',
                'French',
                'German',
                'Italian',
                'Portuguese',
                'Arabic',
                'Chinese',
                'Japanese',
                'Other'
            ]
        ]);
    }

    /**
     * Get provider registration form data (API endpoint).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function showRegistrationForm()
    {
        return response()->json([
            'specializations' => [
                'General Practice',
                'Cardiology',
                'Dermatology',
                'Endocrinology',
                'Gastroenterology',
                'Neurology',
                'Oncology',
                'Orthopedics',
                'Pediatrics',
                'Psychiatry',
                'Radiology',
                'Surgery',
                'Other'
            ],
            'languages' => [
                'English',
                'Spanish',
                'French',
                'German',
                'Italian',
                'Portuguese',
                'Arabic',
                'Chinese',
                'Japanese',
                'Other'
            ]
        ]);
    }

    /**
     * Get registration success data (API endpoint).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function showSuccessPage()
    {
        return response()->json([
            'message' => 'Provider registration submitted successfully',
            'next_steps' => [
                'Your application is under review',
                'You will receive an email confirmation within 24 hours',
                'Our team will contact you if additional information is needed',
                'Once approved, you will receive login credentials'
            ]
        ]);
    }

    /**
     * Handle a provider registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function register(Request $request)
    {
        // Log the incoming request data for debugging
        Log::info('Provider registration request received', [
            'data' => $request->all(),
            'headers' => $request->headers->all()
        ]);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'required|string|max:20',
            'gender' => 'required|in:male,female,other',
            'specialization' => 'required|string',
            'license_number' => 'required|string|unique:providers',
            'bio' => 'required|string|min:50|max:1000',
            'languages' => 'nullable|array',
            'languages.*' => 'string',
        ]);

        if ($validator->fails()) {
            Log::warning('Provider registration validation failed', [
                'errors' => $validator->errors()->toArray(),
                'input' => $request->except(['password', 'password_confirmation']),
                'validation_rules' => [
                    'name' => 'required|string|max:255',
                    'email' => 'required|string|email|max:255|unique:users',
                    'password' => 'required|string|min:8|confirmed',
                    'phone' => 'required|string|max:20',
                    'gender' => 'required|in:male,female,other',
                    'specialization' => 'required|string|max:255',
                    'license_number' => 'required|string|max:255',
                    'bio' => 'required|string|min:50|max:1000',
                    'languages' => 'array',
                    'languages.*' => 'string'
                ]
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'debug' => [
                    'received_data_types' => array_map('gettype', $request->all()),
                    'validation_rules' => 'Check logs for details'
                ]
            ], 422);
        }

        // Create the user with provider role
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone_number' => $request->phone,
            'role' => 'provider',
        ]);

        // Assign the provider role using Spatie permissions
        $user->assignRole('provider');

        // Process languages if provided
        $languages = $request->languages ?? [];

        // Get default clinic for assignment
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first(); // Fallback to any clinic
        }

        // Create the provider profile
        $provider = Provider::create([
            'user_id' => $user->id,
            'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
            'specialization' => $request->specialization,
            'license_number' => $request->license_number,
            'verification_status' => 'verified', // Auto-verify new providers
            'verified_at' => now(),
            'gender' => $request->gender,
            'bio' => $request->bio,
            'languages' => $languages,
            'weekly_availability' => [
                [
                    'day' => 'Monday',
                    'slots' => []
                ],
                [
                    'day' => 'Tuesday',
                    'slots' => []
                ],
                [
                    'day' => 'Wednesday',
                    'slots' => []
                ],
                [
                    'day' => 'Thursday',
                    'slots' => []
                ],
                [
                    'day' => 'Friday',
                    'slots' => []
                ],
                [
                    'day' => 'Saturday',
                    'slots' => []
                ],
                [
                    'day' => 'Sunday',
                    'slots' => []
                ]
            ],
            'absences' => [],
            'practice_locations' => [],
        ]);

        // Send provider registration notification
        try {
            $user->notify(new ProviderRegistrationNotification($user, $provider));
            Log::info('Provider registration notification sent', ['user_id' => $user->id, 'email' => $user->email]);
        } catch (\Exception $e) {
            Log::error('Failed to send provider registration notification', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
        }

        // Return success response
        return response()->json([
            'success' => true,
            'message' => 'Provider registration submitted successfully',
            'application_id' => $provider->id,
            'user_id' => $user->id
        ], 201);
    }
}

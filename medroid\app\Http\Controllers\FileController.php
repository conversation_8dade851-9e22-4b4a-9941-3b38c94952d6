<?php

namespace App\Http\Controllers;

use App\Models\File;
use App\Models\FileUsage;
use App\Services\ImageOptimizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class FileController extends Controller
{
    /**
     * Get files for the authenticated user
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = File::forUser($user->id);

        // Filter by category
        if ($request->has('category') && $request->category !== 'all') {
            $query->byCategory($request->category);
        }

        // Filter by type
        if ($request->has('type')) {
            switch ($request->type) {
                case 'images':
                    $query->images();
                    break;
                case 'documents':
                    $query->documents();
                    break;
                case 'videos':
                    $query->videos();
                    break;
            }
        }

        // Search by name
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $files = $query->paginate($request->get('per_page', 20));

        return response()->json($files);
    }

    /**
     * Upload a new file
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => [
                'required',
                'file',
                'max:51200', // 50MB max
                function ($attribute, $value, $fail) {
                    if (!$this->isAllowedFileType($value)) {
                        $fail('The file type is not allowed.');
                    }
                },
            ],
            'category' => 'nullable|string|in:' . implode(',', array_keys(File::CATEGORIES)),
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = Auth::user();
            $uploadedFile = $request->file('file');

            // Check file size limits based on user role
            $maxSize = $this->getMaxFileSize($user);
            if ($uploadedFile->getSize() > $maxSize) {
                return response()->json([
                    'message' => 'File size exceeds the limit for your account type.'
                ], 422);
            }

            // Sanitize original filename
            $originalName = $this->sanitizeFilename($uploadedFile->getClientOriginalName());

            // Generate unique filename
            $extension = $uploadedFile->getClientOriginalExtension();
            $filename = Str::uuid() . '.' . $extension;

            // Determine category based on file type if not provided
            $category = $request->get('category', $this->determineCategoryFromMimeType($uploadedFile->getMimeType()));

            // Store file in category-specific directory
            $path = $uploadedFile->storeAs(
                'files/' . $user->id . '/' . $category,
                $filename,
                'public'
            );

            // Scan file for malware (basic check)
            $fullPath = storage_path('app/public/' . $path);
            if (!$this->scanFileForMalware($fullPath)) {
                // Delete the uploaded file
                Storage::disk('public')->delete($path);
                return response()->json([
                    'message' => 'File failed security scan.'
                ], 422);
            }

            // Create file record
            $file = File::create([
                'user_id' => $user->id,
                'name' => $request->get('name', $originalName),
                'original_name' => $originalName,
                'path' => $path,
                'disk' => 'public',
                'mime_type' => $uploadedFile->getMimeType(),
                'size' => $uploadedFile->getSize(),
                'category' => $category,
                'extension' => $extension,
                'description' => $request->get('description'),
                'is_public' => $request->get('is_public', false),
            ]);

            return response()->json([
                'message' => 'File uploaded successfully',
                'file' => $file
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific file
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            // Check access permissions
            if (!$this->canAccessFile($user, $file)) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            // Load usage information
            $file->load('usages.usable');

            return response()->json($file);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update file information
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|in:' . implode(',', array_keys(File::CATEGORIES)),
            'is_public' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            // Check if user owns the file (only owners can edit, not even admins for security)
            if ($file->user_id !== $user->id) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            // Sanitize the name
            $sanitizedName = $this->sanitizeFilename($request->name);

            $file->update([
                'name' => $sanitizedName,
                'description' => $request->description,
                'category' => $request->category,
                'is_public' => $request->boolean('is_public', false),
            ]);

            return response()->json([
                'message' => 'File updated successfully',
                'file' => $file
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a file
     */
    public function destroy($id)
    {
        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            // Check if user owns the file or is admin
            if ($file->user_id !== $user->id && !$user->hasRole('admin')) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            // Check if file is in use
            if ($file->isInUse()) {
                $usageDetails = $file->getUsageDetails();
                return response()->json([
                    'message' => 'Cannot delete file as it is currently in use',
                    'usage_details' => $usageDetails
                ], 422);
            }

            // Log file deletion for audit trail
            \Log::info('File deleted', [
                'file_id' => $file->id,
                'file_name' => $file->name,
                'deleted_by' => $user->id,
                'user_email' => $user->email
            ]);

            $file->delete();

            return response()->json([
                'message' => 'File deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download a file
     */
    public function download($id)
    {
        try {
            $user = Auth::user();

            // Get file with ownership check
            $file = File::where('id', $id)->first();

            if (!$file) {
                return response()->json(['message' => 'File not found'], 404);
            }

            // Check access permissions
            if (!$this->canAccessFile($user, $file)) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            // Check if file exists on disk
            if (!Storage::disk($file->disk)->exists($file->path)) {
                return response()->json(['message' => 'File not found on storage'], 404);
            }

            // Increment download count
            $file->incrementDownloadCount();

            // Secure file serving with proper headers
            $headers = [
                'Content-Type' => $file->mime_type,
                'Content-Disposition' => 'attachment; filename="' . $file->original_name . '"',
                'X-Content-Type-Options' => 'nosniff',
                'X-Frame-Options' => 'DENY',
                'X-XSS-Protection' => '1; mode=block',
            ];

            return Storage::disk($file->disk)->download($file->path, $file->original_name, $headers);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to download file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file categories
     */
    public function getCategories()
    {
        return response()->json(File::getCategoryOptions());
    }

    /**
     * Get file statistics
     */
    public function getStats()
    {
        $user = Auth::user();

        $stats = [
            'total_files' => File::forUser($user->id)->count(),
            'total_size' => File::forUser($user->id)->sum('size'),
            'by_category' => File::forUser($user->id)
                ->selectRaw('category, COUNT(*) as count, SUM(size) as total_size')
                ->groupBy('category')
                ->get(),
            'by_type' => [
                'images' => File::forUser($user->id)->images()->count(),
                'documents' => File::forUser($user->id)->documents()->count(),
                'videos' => File::forUser($user->id)->videos()->count(),
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Bulk delete files
     */
    public function bulkDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_ids' => 'required|array',
            'file_ids.*' => 'integer|exists:files,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = Auth::user();
            $files = File::forUser($user->id)->whereIn('id', $request->file_ids)->get();

            $deleted = 0;
            $errors = [];

            foreach ($files as $file) {
                if ($file->isInUse()) {
                    $errors[] = "File '{$file->name}' is in use and cannot be deleted";
                    continue;
                }

                $file->delete();
                $deleted++;
            }

            return response()->json([
                'message' => "Successfully deleted {$deleted} files",
                'deleted_count' => $deleted,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Determine file category based on MIME type
     */
    private function determineCategoryFromMimeType($mimeType)
    {
        if (Str::startsWith($mimeType, 'image/')) {
            return 'general'; // Let user choose specific category
        } elseif (Str::startsWith($mimeType, 'video/')) {
            return 'videos';
        } elseif (Str::startsWith($mimeType, 'audio/')) {
            return 'audio';
        } elseif (in_array($mimeType, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ])) {
            return 'documents';
        }

        return 'general';
    }

    /**
     * Check if file type is allowed
     */
    private function isAllowedFileType($file)
    {
        $allowedMimeTypes = [
            // Images
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',

            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',

            // Videos
            'video/mp4',
            'video/mpeg',
            'video/quicktime',
            'video/webm',

            // Audio
            'audio/mpeg',
            'audio/wav',
            'audio/ogg',
            'audio/mp4',

            // Archives
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
        ];

        $mimeType = $file->getMimeType();
        $extension = strtolower($file->getClientOriginalExtension());

        // Check MIME type
        if (!in_array($mimeType, $allowedMimeTypes)) {
            return false;
        }

        // Additional extension check for security
        $allowedExtensions = [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'txt', 'csv',
            'mp4', 'mpeg', 'mov', 'webm',
            'mp3', 'wav', 'ogg', 'm4a',
            'zip', 'rar', '7z'
        ];

        return in_array($extension, $allowedExtensions);
    }

    /**
     * Sanitize filename for security
     */
    private function sanitizeFilename($filename)
    {
        // Remove any path traversal attempts
        $filename = basename($filename);

        // Remove or replace dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);

        // Ensure filename is not empty and has reasonable length
        if (empty($filename) || strlen($filename) > 255) {
            $filename = 'file_' . time();
        }

        return $filename;
    }

    /**
     * Check file size limits based on user role
     */
    private function getMaxFileSize($user)
    {
        if ($user->hasRole('admin')) {
            return 100 * 1024 * 1024; // 100MB for admins
        } elseif ($user->hasRole('provider')) {
            return 50 * 1024 * 1024; // 50MB for providers
        } else {
            return 10 * 1024 * 1024; // 10MB for patients
        }
    }

    /**
     * Scan file for malware (placeholder for future implementation)
     */
    private function scanFileForMalware($filePath)
    {
        // This is a placeholder for malware scanning
        // In production, you would integrate with a service like ClamAV
        // or a cloud-based scanning service

        // For now, just check file size and basic patterns
        $fileSize = filesize($filePath);
        if ($fileSize > 100 * 1024 * 1024) { // 100MB
            return false;
        }

        return true;
    }

    /**
     * Check if user can access a file
     */
    private function canAccessFile($user, $file)
    {
        // Admin can access any file
        if ($user->hasRole('admin')) {
            return true;
        }

        // User can access their own files
        if ($file->user_id === $user->id) {
            return true;
        }

        // Public files can be accessed by anyone
        if ($file->is_public) {
            return true;
        }

        return false;
    }

    /**
     * Get secure file URL for public files
     */
    public function getSecureUrl($id)
    {
        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            if (!$this->canAccessFile($user, $file)) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            // Generate temporary signed URL for secure access
            $url = Storage::disk($file->disk)->temporaryUrl(
                $file->path,
                now()->addMinutes(60) // URL expires in 1 hour
            );

            return response()->json(['url' => $url]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate secure URL: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Optimize an image file
     */
    public function optimizeImage($id, ImageOptimizationService $optimizationService)
    {
        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            // Check access permissions
            if (!$this->canAccessFile($user, $file)) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            if (!$file->is_image) {
                return response()->json(['message' => 'File is not an image'], 422);
            }

            $result = $optimizationService->optimizeImage($file);

            if (!$result) {
                return response()->json(['message' => 'Failed to optimize image'], 500);
            }

            return response()->json([
                'message' => 'Image optimized successfully',
                'optimization_result' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to optimize image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate thumbnails for an image
     */
    public function generateThumbnails($id, ImageOptimizationService $optimizationService)
    {
        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            // Check access permissions
            if (!$this->canAccessFile($user, $file)) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            if (!$file->is_image) {
                return response()->json(['message' => 'File is not an image'], 422);
            }

            $thumbnails = $optimizationService->generateThumbnails($file);

            if (!$thumbnails) {
                return response()->json(['message' => 'Failed to generate thumbnails'], 500);
            }

            return response()->json([
                'message' => 'Thumbnails generated successfully',
                'thumbnails' => $thumbnails
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate thumbnails: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get thumbnail URL for an image
     */
    public function getThumbnail($id, Request $request, ImageOptimizationService $optimizationService)
    {
        try {
            $user = Auth::user();
            $file = File::findOrFail($id);

            // Check access permissions
            if (!$this->canAccessFile($user, $file)) {
                return response()->json(['message' => 'Access denied'], 403);
            }

            if (!$file->is_image) {
                return response()->json(['message' => 'File is not an image'], 422);
            }

            $size = $request->get('size', 'medium');
            $url = $optimizationService->getThumbnailUrl($file, $size);

            if (!$url) {
                return response()->json(['message' => 'Thumbnail not available'], 404);
            }

            return response()->json(['thumbnail_url' => $url]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get thumbnail: ' . $e->getMessage()
            ], 500);
        }
    }
}

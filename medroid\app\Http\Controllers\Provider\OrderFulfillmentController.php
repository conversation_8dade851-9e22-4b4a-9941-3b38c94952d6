<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Mail\OrderDispatchedMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;

class OrderFulfillmentController extends Controller
{
    /**
     * Get orders for provider fulfillment
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get orders that contain products from this provider
        $query = Order::with(['user', 'items.product', 'dispatchedBy'])
            ->whereHas('items.product', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Search by order number or customer name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->paginate(20);

        // Filter order items to only show this provider's products
        foreach ($orders as $order) {
            $order->items = $order->items->filter(function ($item) use ($user) {
                return $item->product && $item->product->user_id === $user->id;
            });
        }

        // Get summary statistics for this provider
        $stats = [
            'total_orders' => Order::whereHas('items.product', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->count(),
            'pending_orders' => Order::where('status', 'pending')
                ->whereHas('items.product', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->count(),
            'processing_orders' => Order::where('status', 'processing')
                ->whereHas('items.product', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->count(),
            'shipped_orders' => Order::where('status', 'shipped')
                ->whereHas('items.product', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->count(),
        ];

        if ($request->expectsJson()) {
            return response()->json([
                'orders' => $orders,
                'stats' => $stats,
            ]);
        }

        return Inertia::render('Provider/Orders', [
            'orders' => $orders,
            'stats' => $stats,
            'filters' => $request->only(['status', 'payment_status', 'search']),
        ]);
    }

    /**
     * Show order details for fulfillment
     */
    public function show(Request $request, $id)
    {
        $user = Auth::user();

        $order = Order::with(['user', 'items.product.images', 'dispatchedBy'])
            ->whereHas('items.product', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->findOrFail($id);

        // Filter order items to only show this provider's products
        $order->items = $order->items->filter(function ($item) use ($user) {
            return $item->product && $item->product->user_id === $user->id;
        });

        if ($request->expectsJson()) {
            return response()->json([
                'order' => $order,
            ]);
        }

        return Inertia::render('Provider/OrderDetail', [
            'order' => $order,
        ]);
    }

    /**
     * Dispatch an order (mark as shipped with tracking info)
     */
    public function dispatch(Request $request, $id)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'tracking_number' => 'required|string|max:255',
            'shipping_company' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $order = Order::whereHas('items.product', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->findOrFail($id);

        // Check if order can be shipped
        if (!in_array($order->status, ['pending', 'processing'])) {
            return response()->json(['message' => 'Order cannot be dispatched in current status'], 422);
        }

        // Mark order as dispatched
        $order->markAsDispatched(
            $request->tracking_number,
            $request->shipping_company,
            $user->id
        );

        // Send dispatch notification email to customer
        try {
            Mail::to($order->user->email)->send(new OrderDispatchedMail($order));
        } catch (\Exception $e) {
            \Log::error('Failed to send dispatch email: ' . $e->getMessage());
        }

        return response()->json([
            'message' => 'Order dispatched successfully',
            'order' => $order->fresh(['user', 'items.product', 'dispatchedBy']),
        ]);
    }

    /**
     * Get shipping companies list
     */
    public function getShippingCompanies()
    {
        $companies = [
            'DHL',
            'FedEx',
            'UPS',
            'Royal Mail',
            'DPD',
            'Hermes',
            'Yodel',
            'Amazon Logistics',
            'TNT',
            'Other'
        ];

        return response()->json(['companies' => $companies]);
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, $id)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $order = Order::whereHas('items.product', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->findOrFail($id);

        $order->update(['status' => $request->status]);

        return response()->json([
            'message' => 'Order status updated successfully',
            'order' => $order->fresh(['user', 'items.product', 'dispatchedBy']),
        ]);
    }
}

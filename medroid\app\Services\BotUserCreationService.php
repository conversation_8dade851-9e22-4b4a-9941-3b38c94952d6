<?php

namespace App\Services;

use App\Models\User;
use App\Models\BotPersona;
use App\Models\BotUser;
use App\Models\PostingSchedule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BotUserCreationService
{
    /**
     * Create a bot user account for a persona
     */
    public function createBotUser(BotPersona $persona): ?BotUser
    {
        try {
            Log::info('Creating bot user for persona', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
            ]);

            // Create the User account
            $user = $this->createUserAccount($persona);
            
            if (!$user) {
                Log::error('Failed to create user account for persona', [
                    'persona_id' => $persona->id,
                ]);
                return null;
            }

            // Create the BotUser record
            $botUser = BotUser::create([
                'user_id' => $user->id,
                'bot_persona_id' => $persona->id,
                'profile_image_url' => $this->generateProfileImageUrl($persona),
                'is_verified' => $this->shouldBeVerified($persona),
                'follower_count' => $this->generateFollowerCount($persona),
                'following_count' => $this->generateFollowingCount($persona),
                'post_count' => 0,
                'engagement_metrics' => [
                    'total_likes' => 0,
                    'total_comments' => 0,
                    'total_shares' => 0,
                    'total_posts' => 0,
                ],
            ]);

            // Create posting schedules
            $this->createPostingSchedules($persona);

            Log::info('Bot user created successfully', [
                'persona_id' => $persona->id,
                'user_id' => $user->id,
                'bot_user_id' => $botUser->id,
            ]);

            return $botUser;

        } catch (\Exception $e) {
            Log::error('Error creating bot user', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create the User account for the bot
     */
    private function createUserAccount(BotPersona $persona): ?User
    {
        try {
            // Generate email
            $email = $this->generateEmail($persona);
            
            // Check if email already exists
            if (User::where('email', $email)->exists()) {
                $email = $this->generateEmail($persona, true); // Add random suffix
            }

            $user = User::create([
                'name' => $persona->full_name,
                'email' => $email,
                'password' => Hash::make(Str::random(32)), // Random password
                'role' => 'patient',
                'signup_source' => 'system_created', // Use existing enum value
                'is_active' => true,
                'email_verified_at' => now(),
                'bio' => $persona->bio,
                'date_of_birth' => $this->generateDateOfBirth($persona->age),
            ]);

            // Assign patient role
            $user->assignRole('patient');

            return $user;

        } catch (\Exception $e) {
            Log::error('Error creating user account for bot', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Generate email for the bot user
     */
    private function generateEmail(BotPersona $persona, bool $addSuffix = false): string
    {
        $firstName = strtolower($persona->first_name);
        $lastName = strtolower($persona->last_name);
        
        $baseEmail = "{$firstName}.{$lastName}";
        
        if ($addSuffix) {
            $baseEmail .= '.' . rand(100, 999);
        }
        
        return "{$baseEmail}@medroid-bot.local";
    }

    /**
     * Generate date of birth based on age
     */
    private function generateDateOfBirth(int $age): string
    {
        $birthYear = now()->year - $age;
        $birthMonth = rand(1, 12);
        $birthDay = rand(1, 28); // Safe day for all months
        
        return sprintf("%d-%02d-%02d", $birthYear, $birthMonth, $birthDay);
    }

    /**
     * Generate profile image URL (placeholder for now)
     */
    private function generateProfileImageUrl(BotPersona $persona): ?string
    {
        // For now, return null. In the future, this could generate
        // AI-generated profile images or use stock photos
        return null;
    }

    /**
     * Determine if bot should be verified
     */
    private function shouldBeVerified(BotPersona $persona): bool
    {
        // Some personas might be "verified" based on their expertise
        $verifiedTypes = ['fitness', 'nutrition', 'mental health', 'medical'];
        
        return in_array($persona->content_focus, $verifiedTypes) && rand(1, 100) <= 30; // 30% chance
    }

    /**
     * Generate realistic follower count
     */
    private function generateFollowerCount(BotPersona $persona): int
    {
        $baseFollowers = 500;
        
        // Adjust based on persona characteristics
        if ($persona->age < 30) {
            $baseFollowers *= 1.5; // Younger personas tend to have more followers
        }
        
        if (in_array($persona->content_focus, ['fitness', 'nutrition'])) {
            $baseFollowers *= 2; // Popular topics
        }
        
        // Add randomness
        $variation = rand(50, 200) / 100; // 0.5 to 2.0 multiplier
        
        return round($baseFollowers * $variation);
    }

    /**
     * Generate realistic following count
     */
    private function generateFollowingCount(BotPersona $persona): int
    {
        $followerCount = $this->generateFollowerCount($persona);
        
        // Following count is usually 20-80% of follower count for health influencers
        $ratio = rand(20, 80) / 100;
        
        return round($followerCount * $ratio);
    }

    /**
     * Create posting schedules for the persona
     */
    private function createPostingSchedules(BotPersona $persona): void
    {
        $postsPerWeek = $persona->posts_per_week;
        
        // Define optimal posting times for health content
        $optimalTimes = [
            'morning' => ['07:00', '08:30', '09:15'],
            'afternoon' => ['12:30', '13:45', '15:30'],
            'evening' => ['17:30', '18:45', '19:30'],
        ];
        
        // Distribute posts across the week
        $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $selectedDays = array_slice($daysOfWeek, 0, min($postsPerWeek, 7));
        
        foreach ($selectedDays as $index => $day) {
            // Choose time period based on persona characteristics
            $timePeriod = $this->getPreferredTimePeriod($persona, $day);
            $times = $optimalTimes[$timePeriod];
            $selectedTime = $times[array_rand($times)];
            
            PostingSchedule::create([
                'bot_persona_id' => $persona->id,
                'day_of_week' => $day,
                'preferred_time' => $selectedTime,
                'priority' => $index + 1,
                'is_active' => true,
            ]);
        }
        
        Log::info('Created posting schedules for persona', [
            'persona_id' => $persona->id,
            'schedules_created' => count($selectedDays),
        ]);
    }

    /**
     * Get preferred time period for posting based on persona
     */
    private function getPreferredTimePeriod(BotPersona $persona, string $day): string
    {
        // Fitness personas prefer morning posts
        if (in_array('fitness', $persona->interests) || in_array('running', $persona->interests)) {
            return 'morning';
        }
        
        // Nutrition personas prefer meal times
        if (in_array('nutrition', $persona->interests) || in_array('cooking', $persona->interests)) {
            return rand(0, 1) ? 'afternoon' : 'evening';
        }
        
        // Mental health personas prefer evening
        if (in_array('meditation', $persona->interests) || in_array('mindfulness', $persona->interests)) {
            return 'evening';
        }
        
        // Weekend posts tend to be more flexible
        if (in_array($day, ['saturday', 'sunday'])) {
            $periods = ['morning', 'afternoon', 'evening'];
            return $periods[array_rand($periods)];
        }
        
        // Default distribution
        $periods = ['morning', 'afternoon', 'evening'];
        return $periods[array_rand($periods)];
    }

    /**
     * Create all bot users for existing personas
     */
    public function createAllBotUsers(): array
    {
        $results = [];
        
        $personas = BotPersona::active()
            ->whereDoesntHave('botUser')
            ->get();
        
        foreach ($personas as $persona) {
            $botUser = $this->createBotUser($persona);
            $results[] = [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
                'success' => $botUser !== null,
                'bot_user_id' => $botUser?->id,
            ];
        }
        
        return $results;
    }
}

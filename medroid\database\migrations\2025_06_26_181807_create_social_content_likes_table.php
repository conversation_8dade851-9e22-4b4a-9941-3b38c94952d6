<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('social_content_likes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('social_content_id')->constrained('social_contents')->onDelete('cascade');
            $table->timestamps();

            // Ensure a user can only like a content once
            $table->unique(['user_id', 'social_content_id']);
            
            // Index for faster queries
            $table->index(['user_id']);
            $table->index(['social_content_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_content_likes');
    }
};
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PerformanceMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $profile
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $profile = 'default')
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        // Set performance limits based on profile
        $this->setPerformanceLimits($profile);

        // Log request start
        Log::info('Performance middleware applied', [
            'profile' => $profile,
            'route' => $request->route()?->getName(),
            'method' => $request->method(),
            'url' => $request->url(),
            'memory_start' => $this->formatBytes($startMemory),
            'time_limit' => ini_get('max_execution_time'),
            'memory_limit' => ini_get('memory_limit')
        ]);

        $response = $next($request);

        // Log performance metrics
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        $executionTime = $endTime - $startTime;

        Log::info('Request completed', [
            'profile' => $profile,
            'execution_time' => round($executionTime, 3) . 's',
            'memory_used' => $this->formatBytes($endMemory - $startMemory),
            'peak_memory' => $this->formatBytes($peakMemory),
            'response_status' => $response->getStatusCode()
        ]);

        // Add performance headers for debugging
        if (config('app.debug')) {
            $response->headers->set('X-Execution-Time', round($executionTime, 3));
            $response->headers->set('X-Memory-Peak', $this->formatBytes($peakMemory));
            $response->headers->set('X-Performance-Profile', $profile);
        }

        return $response;
    }

    /**
     * Set performance limits based on profile
     */
    private function setPerformanceLimits($profile)
    {
        $executionTime = config("performance.execution_time.{$profile}", 
                               config('performance.execution_time.default', 30));
        
        $memoryLimit = config("performance.memory_limits.{$profile}", 
                             config('performance.memory_limits.default', '256M'));

        set_time_limit($executionTime);
        ini_set('memory_limit', $memoryLimit);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

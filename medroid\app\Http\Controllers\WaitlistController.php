<?php

namespace App\Http\Controllers;

use App\Models\WaitlistRequest;
use App\Models\WaitlistInvitation;
use App\Services\WaitlistService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WaitlistController extends Controller
{
    protected $waitlistService;

    public function __construct(WaitlistService $waitlistService)
    {
        $this->waitlistService = $waitlistService;
    }

    /**
     * Get waitlist status (public endpoint)
     */
    public function getWaitlistStatus()
    {
        return response()->json([
            'enabled' => $this->waitlistService->isWaitlistEnabled(),
            'messages' => config('waitlist.messages'),
        ]);
    }

    /**
     * Add email to waitlist
     */
    public function joinWaitlist(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->waitlistService->addToWaitlist(
            $request->email,
            $request->name
        );

        return response()->json($result);
    }

    /**
     * Get waitlist requests (admin only)
     */
    public function getWaitlistRequests(Request $request)
    {
        try {
            if (!$request->user()->can('view users')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $query = WaitlistRequest::query();

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('email', 'like', "%{$search}%")
                      ->orWhere('name', 'like', "%{$search}%");
                });
            }

            $requests = $query->with('invitation')
                             ->orderBy('created_at', 'desc')
                             ->paginate($request->input('per_page', 15));

            return response()->json($requests);
        } catch (\Exception $e) {
            \Log::error('Error getting waitlist requests: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $request->user()->id ?? null,
            ]);
            
            return response()->json([
                'message' => 'Unable to fetch waitlist requests',
                'error' => 'Server error occurred'
            ], 500);
        }
    }

    /**
     * Send invitation to waitlist request
     */
    public function sendInvitation(Request $request, $requestId)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'club_type' => 'required|in:founder,premium,regular',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $waitlistRequest = WaitlistRequest::findOrFail($requestId);

        if (!$waitlistRequest->canBeInvited()) {
            return response()->json([
                'success' => false,
                'message' => 'This request cannot be invited'
            ], 400);
        }

        // Create invitation
        $invitation = $this->waitlistService->createInvitation(
            $waitlistRequest->email,
            $request->club_type,
            $request->user()
        );

        // Send invitation email
        $sent = $this->waitlistService->sendInvitation($invitation);

        if ($sent) {
            return response()->json([
                'success' => true,
                'message' => 'Invitation sent successfully',
                'invitation' => $invitation
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send invitation'
            ], 500);
        }
    }

    /**
     * Send bulk invitations
     */
    public function sendBulkInvitations(Request $request)
    {
        try {
            if (!$request->user()->can('edit settings')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $validator = Validator::make($request->all(), [
                'request_ids' => 'required|array|min:1',
                'request_ids.*' => 'integer|exists:waitlist_requests,id',
                'club_type' => 'required|in:founder,premium,regular',
                'membership_level' => 'required|string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $requests = WaitlistRequest::whereIn('id', $request->request_ids)
                                      ->where('status', 'pending')
                                      ->get();

            $sent = 0;
            $failed = 0;
            $errors = [];

            foreach ($requests as $waitlistRequest) {
                try {
                    // Create invitation
                    $invitation = $this->waitlistService->createInvitation(
                        $waitlistRequest->email,
                        $request->club_type,
                        $request->user()
                    );

                    // Send invitation email
                    if ($this->waitlistService->sendInvitation($invitation)) {
                        $sent++;
                    } else {
                        $failed++;
                        $errors[] = "Failed to send invitation to {$waitlistRequest->email}";
                    }
                } catch (\Exception $e) {
                    $failed++;
                    $errors[] = "Error processing {$waitlistRequest->email}: " . $e->getMessage();
                    \Log::error('Bulk invitation error', [
                        'email' => $waitlistRequest->email,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Sent {$sent} invitations successfully" . ($failed > 0 ? ", {$failed} failed" : ""),
                'sent' => $sent,
                'failed' => $failed,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            \Log::error('Error sending bulk invitations: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $request->user()->id ?? null,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Unable to send bulk invitations',
                'error' => 'Server error occurred'
            ], 500);
        }
    }

    /**
     * Get invitation statistics
     */
    public function getInvitationStats(Request $request)
    {
        try {
            if (!$request->user()->can('view users')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $stats = [
                'total_requests' => WaitlistRequest::count(),
                'pending_requests' => WaitlistRequest::where('status', 'pending')->count(),
                'invited_requests' => WaitlistRequest::where('status', 'invited')->count(),
                'registered_requests' => WaitlistRequest::where('status', 'registered')->count(),
                'total_invitations' => WaitlistInvitation::count(),
                'sent_invitations' => WaitlistInvitation::where('status', 'sent')->count(),
                'used_invitations' => WaitlistInvitation::where('status', 'used')->count(),
                'expired_invitations' => WaitlistInvitation::expired()->count(),
            ];

            // Recent activity with error handling
            $recentRequests = WaitlistRequest::orderBy('created_at', 'desc')
                                           ->limit(10)
                                           ->get();

            $recentInvitations = WaitlistInvitation::with('creator')
                                                 ->orderBy('created_at', 'desc')
                                                 ->limit(10)
                                                 ->get();

            return response()->json([
                'stats' => $stats,
                'recent_requests' => $recentRequests,
                'recent_invitations' => $recentInvitations,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting invitation stats: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $request->user()->id ?? null,
            ]);
            
            return response()->json([
                'message' => 'Unable to fetch invitation statistics',
                'error' => 'Server error occurred'
            ], 500);
        }
    }
}

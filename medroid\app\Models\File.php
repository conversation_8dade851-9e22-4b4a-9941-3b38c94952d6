<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class File extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'original_name',
        'path',
        'disk',
        'mime_type',
        'size',
        'category',
        'extension',
        'description',
        'metadata',
        'is_public',
        'last_accessed_at',
        'download_count',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_public' => 'boolean',
        'last_accessed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // File categories
    const CATEGORIES = [
        'product_images' => 'Product Images',
        'profile_images' => 'Profile Images',
        'documents' => 'Documents',
        'videos' => 'Videos',
        'audio' => 'Audio',
        'stories' => 'Stories',
        'chat_attachments' => 'Chat Attachments',
        'general' => 'General',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function usages()
    {
        return $this->hasMany(FileUsage::class);
    }

    // Scopes
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeImages($query)
    {
        return $query->where('mime_type', 'like', 'image/%');
    }

    public function scopeDocuments($query)
    {
        return $query->whereIn('mime_type', [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
        ]);
    }

    public function scopeVideos($query)
    {
        return $query->where('mime_type', 'like', 'video/%');
    }

    // Accessors
    public function getUrlAttribute()
    {
        $url = Storage::disk($this->disk)->url($this->path);
        
        // Prevent double URL prefix issues
        if (Str::startsWith($url, ['http://', 'https://'])) {
            return $url;
        }
        
        // If it's a relative path, ensure proper URL construction
        if ($this->disk === 'public') {
            return url('storage/' . $this->path);
        }
        
        return $url;
    }

    public function getFullPathAttribute()
    {
        return Storage::disk($this->disk)->path($this->path);
    }

    public function getSizeFormattedAttribute()
    {
        return $this->formatBytes($this->size);
    }

    public function getIsImageAttribute()
    {
        return Str::startsWith($this->mime_type, 'image/');
    }

    public function getIsVideoAttribute()
    {
        return Str::startsWith($this->mime_type, 'video/');
    }

    public function getIsDocumentAttribute()
    {
        return in_array($this->mime_type, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
        ]);
    }

    public function getCategoryDisplayNameAttribute()
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    // Methods
    public function incrementDownloadCount()
    {
        $this->increment('download_count');
        $this->update(['last_accessed_at' => now()]);
    }

    public function isInUse()
    {
        return $this->usages()->exists();
    }

    public function getUsageDetails()
    {
        return $this->usages()->with('usable')->get();
    }

    public function delete()
    {
        // Delete the physical file
        if (Storage::disk($this->disk)->exists($this->path)) {
            Storage::disk($this->disk)->delete($this->path);
        }

        return parent::delete();
    }

    private function formatBytes($size, $precision = 2)
    {
        $base = log($size, 1024);
        $suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }

    // Static methods
    public static function getCategoryOptions()
    {
        return collect(self::CATEGORIES)->map(function ($label, $value) {
            return ['value' => $value, 'label' => $label];
        })->values();
    }
}

<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Services\OpenAiGpt4Service;
use App\Services\GroqService;
use App\Services\MessageFormattingService;
use Illuminate\Support\Facades\Log;

class AIService
{
    public $openAiService;
    public $groqService;

    /**
     * Create a new service instance.
     *
     * @param OpenAiGpt4Service $openAiService
     * @param GroqService $groqService
     * @return void
     */
    public function __construct(OpenAiGpt4Service $openAiService, GroqService $groqService)
    {
        $this->openAiService = $openAiService;
        $this->groqService = $groqService;
    }

    /**
     * Check if OpenAI should be used based on environment configuration
     */
    private function useOpenAi()
    {
        return env('AI_SERVICE_PROVIDER', 'groq') === 'openai';
    }

    /**
     * Generate a chat response for a conversation.
     *
     * @param ChatConversation $conversation
     * @param string $message
     * @param bool $includePatientContext
     * @param bool $requestFullResponse
     * @param string $additionalContext
     * @return array
     */
    public function generateChatResponse(ChatConversation $conversation, string $message, bool $includePatientContext = false, bool $requestFullResponse = false, string $additionalContext = '')
    {
        try {
            // Generate AI response using the selected service
            if ($this->useOpenAi()) {
                $response = $this->openAiService->generateMedicalConsultation($conversation, $includePatientContext, $additionalContext);
            } else {
                $response = $this->groqService->generateMedicalConsultation($conversation, $includePatientContext, $additionalContext);
            }
            return $response;
        } catch (\Exception $e) {
            Log::error('Error generating AI response: ' . $e->getMessage());

            // Return a formatted fallback response
            $fallbackMessage = "I apologize, but I am having **trouble processing** your request right now. Please **try again later** or contact our support team for assistance.";

            return [
                'message' => MessageFormattingService::formatMedicalResponse($fallbackMessage),
                'health_concerns' => [],
                'recommendations' => [],
                'escalated' => false,
            ];
        }
    }

    /**
     * Generate a title for a conversation.
     *
     * @param ChatConversation $conversation
     * @return string
     */
    public function generateConversationTitle(ChatConversation $conversation)
    {
        try {
            // Extract conversation text
            $conversationText = '';
            foreach ($conversation->messages as $message) {
                $role = $message['role'] ?? 'unknown';
                $content = $message['content'] ?? '';
                $conversationText .= "{$role}: {$content}\n";
            }

            // Create a prompt for generating a title
            $prompt = "Generate ONLY a concise, descriptive title (max 50 characters) that summarizes the main topic of this conversation. Return ONLY the title text without any prefixes like 'Here is', 'The title is', or explanatory text:\n\n{$conversationText}";

            if ($this->useOpenAi()) {
                $response = $this->openAiService->generateTitle($prompt);
            } else {
                $response = $this->groqService->generateTitle($prompt);
            }

            // Clean up the title - remove common AI response prefixes
            $response = trim($response, " \t\n\r\0\x0B\"'");

            $prefixes = [
                'Here is the precise title:',
                'Here is the title:',
                'Here\'s the title:',
                'The title is:',
                'Title:',
                'Here is a title:',
                'Here\'s a title:',
                'A suitable title would be:',
                'A good title would be:',
                'The precise title is:'
            ];

            foreach ($prefixes as $prefix) {
                if (stripos($response, $prefix) === 0) {
                    $response = trim(substr($response, strlen($prefix)));
                    break;
                }
            }

            // Remove any remaining quotes or colons at the start/end
            $response = trim($response, " \t\n\r\0\x0B\"':");

            // Ensure the title is not too long
            if (strlen($response) > 50) {
                $response = substr($response, 0, 47) . '...';
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('Error generating conversation title: ' . $e->getMessage());

            // If there's an error, fall back to a generic title with the date
            return 'Health Conversation - ' . now()->format('M d, Y');
        }
    }

    /**
     * Check if a message requires authentication based on content.
     * Uses AI to detect appointment booking intent from context.
     *
     * @param string $message
     * @param array $conversationContext Optional conversation history for context
     * @return bool
     */
    public function checkIfAuthRequired($message, $conversationContext = [])
    {
        try {
            // Use the selected AI service to detect appointment booking intent
            if ($this->useOpenAi()) {
                return $this->openAiService->detectAppointmentBookingIntent($message, $conversationContext);
            } else {
                return $this->groqService->detectAppointmentBookingIntent($message, $conversationContext);
            }
        } catch (\Exception $e) {
            Log::error('Error detecting appointment booking intent: ' . $e->getMessage());

            // If there's an error with the AI service, return false to avoid
            // unnecessarily prompting for authentication
            return false;
        }
    }
}

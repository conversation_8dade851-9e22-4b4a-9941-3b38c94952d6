<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PostingSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'bot_persona_id',
        'day_of_week',
        'preferred_time',
        'priority',
        'is_active',
    ];

    protected $casts = [
        'preferred_time' => 'datetime:H:i:s',
        'priority' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the persona this schedule belongs to
     */
    public function botPersona(): BelongsTo
    {
        return $this->belongsTo(BotPersona::class);
    }

    /**
     * Get the formatted time for display
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->preferred_time->format('g:i A');
    }

    /**
     * Get the day name capitalized
     */
    public function getDayNameAttribute(): string
    {
        return ucfirst($this->day_of_week);
    }

    /**
     * Check if this schedule is for today
     */
    public function isToday(): bool
    {
        return $this->day_of_week === strtolower(now()->format('l'));
    }

    /**
     * Check if the scheduled time has passed today
     */
    public function hasPassedToday(): bool
    {
        if (!$this->isToday()) {
            return false;
        }

        $scheduledTime = now()->setTimeFromTimeString($this->preferred_time->format('H:i:s'));
        return now()->gt($scheduledTime);
    }

    /**
     * Get next occurrence of this schedule
     */
    public function getNextOccurrence(): \Carbon\Carbon
    {
        $now = now();
        $targetDay = $this->day_of_week;
        $targetTime = $this->preferred_time->format('H:i:s');

        // Calculate days until target day
        $days = ['monday' => 1, 'tuesday' => 2, 'wednesday' => 3, 'thursday' => 4, 'friday' => 5, 'saturday' => 6, 'sunday' => 0];
        $currentDay = $days[strtolower($now->format('l'))];
        $targetDayNum = $days[$targetDay];

        if ($targetDayNum > $currentDay) {
            $daysToAdd = $targetDayNum - $currentDay;
        } elseif ($targetDayNum < $currentDay) {
            $daysToAdd = 7 - ($currentDay - $targetDayNum);
        } else {
            // Same day - check if time has passed
            $scheduledTime = $now->copy()->setTimeFromTimeString($targetTime);
            if ($scheduledTime->gt($now)) {
                $daysToAdd = 0;
            } else {
                $daysToAdd = 7;
            }
        }

        return $now->copy()->addDays($daysToAdd)->setTimeFromTimeString($targetTime);
    }

    /**
     * Scope to get active schedules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get schedules for today
     */
    public function scopeToday($query)
    {
        return $query->where('day_of_week', strtolower(now()->format('l')));
    }

    /**
     * Scope to get schedules ordered by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority')->orderBy('preferred_time');
    }
}

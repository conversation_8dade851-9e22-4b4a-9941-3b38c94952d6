<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Waitlist Mode
    |--------------------------------------------------------------------------
    |
    | This option controls whether the application is in waitlist mode.
    | When enabled, only users with valid referral codes can sign up.
    | This is useful for controlling access during beta or launch phases.
    |
    */

    'enabled' => env('WAITLIST_MODE', false),

    /*
    |--------------------------------------------------------------------------
    | Waitlist Messages
    |--------------------------------------------------------------------------
    |
    | These messages are displayed to users when the application is in
    | waitlist mode. You can customize these messages as needed.
    |
    */

    'messages' => [
        'signup_disabled' => 'Medroid is currently in invitation-only mode. Please enter a valid invitation code to sign up.',
        'google_signin_disabled' => 'Google sign-in is currently limited to existing users only. Please use your email and password to sign in, or get an invitation code to sign up.',
        'invitation_required' => 'An invitation code is required to create a new account.',
        'invalid_invitation' => 'The invitation code you entered is invalid or has expired.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed Referral Sources
    |--------------------------------------------------------------------------
    |
    | When waitlist mode is enabled, these are the types of referral codes
    | that are allowed for new user registration.
    |
    */

    'allowed_referral_sources' => [
        'founder_codes' => true,  // Allow founder referral codes
        'user_referrals' => true, // Allow regular user referral codes
    ],

    /*
    |--------------------------------------------------------------------------
    | Bypass Users
    |--------------------------------------------------------------------------
    |
    | Email addresses that can bypass waitlist restrictions.
    | Useful for admin accounts or testing purposes.
    |
    */

    'bypass_emails' => [
        // Add admin emails here if needed
        // '<EMAIL>',
    ],
];

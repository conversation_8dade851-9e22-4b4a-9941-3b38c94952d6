<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'chat_id',
        'sender_id',
        'sender_type',
        'content',
        'is_read',
        'read_at',
        'is_system_message',
        'hidden',
        'hidden_by',
        'hidden_at',
        'edited_by',
        'edited_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'is_system_message' => 'boolean',
        'hidden' => 'boolean',
        'hidden_at' => 'datetime',
        'edited_at' => 'datetime',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the chat that owns the message.
     */
    public function chat()
    {
        return $this->belongsTo(Chat::class);
    }

    /**
     * Get the sender of the message.
     */
    public function sender()
    {
        return $this->morphTo();
    }

    /**
     * Get the user who hid the message.
     */
    public function hiddenBy()
    {
        return $this->belongsTo(User::class, 'hidden_by');
    }

    /**
     * Get the user who edited the message.
     */
    public function editedBy()
    {
        return $this->belongsTo(User::class, 'edited_by');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AutomatedPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'bot_user_id',
        'bot_persona_id',
        'caption',
        'hashtags',
        'image_url',
        'midjourney_task_id',
        'status',
        'scheduled_at',
        'posted_at',
        'generation_prompts',
        'engagement_data',
        'error_message',
        'retry_count',
    ];

    protected $casts = [
        'hashtags' => 'array',
        'scheduled_at' => 'datetime',
        'posted_at' => 'datetime',
        'generation_prompts' => 'array',
        'engagement_data' => 'array',
        'retry_count' => 'integer',
    ];

    /**
     * Get the bot user that owns this post
     */
    public function botUser(): BelongsTo
    {
        return $this->belongsTo(BotUser::class);
    }

    /**
     * Get the persona this post belongs to
     */
    public function botPersona(): BelongsTo
    {
        return $this->belongsTo(BotPersona::class);
    }

    /**
     * Get the image generation log for this post
     */
    public function imageGenerationLog(): HasOne
    {
        return $this->hasOne(ImageGenerationLog::class);
    }

    /**
     * Get the full caption with hashtags
     */
    public function getFullCaptionAttribute(): string
    {
        $caption = $this->caption;
        
        if (!empty($this->hashtags)) {
            $hashtags = collect($this->hashtags)->map(function ($tag) {
                return str_starts_with($tag, '#') ? $tag : "#{$tag}";
            })->implode(' ');
            
            $caption .= "\n\n" . $hashtags;
        }

        return $caption;
    }

    /**
     * Check if post is ready to be published
     */
    public function isReadyToPost(): bool
    {
        return $this->status === 'ready' && 
               $this->scheduled_at && 
               $this->scheduled_at->lte(now()) &&
               !empty($this->caption) &&
               !empty($this->image_url);
    }

    /**
     * Mark post as failed with error message
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
        ]);
    }

    /**
     * Mark post as posted
     */
    public function markAsPosted(): void
    {
        $this->update([
            'status' => 'posted',
            'posted_at' => now(),
        ]);
    }

    /**
     * Update engagement data
     */
    public function updateEngagementData(array $data): void
    {
        $currentData = $this->engagement_data ?? [];
        $this->engagement_data = array_merge($currentData, $data);
        $this->save();
    }

    /**
     * Check if post can be retried
     */
    public function canRetry(): bool
    {
        return $this->status === 'failed' && $this->retry_count < 3;
    }

    /**
     * Scope to get posts ready for posting
     */
    public function scopeReadyToPost($query)
    {
        return $query->where('status', 'ready')
                    ->where('scheduled_at', '<=', now())
                    ->whereNotNull('image_url')
                    ->whereNotNull('caption');
    }

    /**
     * Scope to get failed posts that can be retried
     */
    public function scopeRetryable($query)
    {
        return $query->where('status', 'failed')
                    ->where('retry_count', '<', 3);
    }

    /**
     * Scope to get posts by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get recent posts
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}

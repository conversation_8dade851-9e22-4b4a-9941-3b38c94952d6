<?php

namespace App\Listeners;

use App\Notifications\UserRegistrationNotification;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendUserRegistrationNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        try {
            // Send user registration notification
            $event->user->notify(new UserRegistrationNotification($event->user));
            
            Log::info('User registration notification sent', [
                'user_id' => $event->user->id,
                'email' => $event->user->email
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send user registration notification', [
                'user_id' => $event->user->id,
                'email' => $event->user->email,
                'error' => $e->getMessage()
            ]);
        }
    }
}

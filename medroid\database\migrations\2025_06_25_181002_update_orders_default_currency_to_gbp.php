<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('currency', 3)->default('GBP')->change();
        });

        // Update existing orders to use GBP currency
        \DB::table('orders')->where('currency', 'USD')->update(['currency' => 'GBP']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('currency', 3)->default('USD')->change();
        });

        // Revert existing orders back to USD currency
        \DB::table('orders')->where('currency', 'GBP')->update(['currency' => 'USD']);
    }
};

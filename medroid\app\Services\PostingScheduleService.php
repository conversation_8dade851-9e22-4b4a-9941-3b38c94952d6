<?php

namespace App\Services;

use App\Models\BotPersona;
use App\Models\PostingSchedule;
use App\Models\AutomatedPost;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class PostingScheduleService
{
    /**
     * Calculate optimal posting times for all personas
     */
    public function calculateOptimalPostingTimes(): array
    {
        $personas = BotPersona::active()->with(['botUser', 'postingSchedules'])->get();
        $schedule = [];

        foreach ($personas as $persona) {
            $optimalTimes = $this->getOptimalTimesForPersona($persona);
            $schedule[$persona->id] = [
                'persona' => $persona->full_name,
                'times' => $optimalTimes,
                'next_post' => $this->getNextPostingTime($persona),
            ];
        }

        return $schedule;
    }

    /**
     * Get optimal posting times for a specific persona
     */
    public function getOptimalTimesForPersona(BotPersona $persona): array
    {
        $schedules = $persona->postingSchedules()->active()->orderBy('priority')->get();
        $optimalTimes = [];

        foreach ($schedules as $schedule) {
            $optimalTimes[] = [
                'day' => $schedule->day_of_week,
                'time' => $schedule->preferred_time->format('H:i'),
                'priority' => $schedule->priority,
                'next_occurrence' => $schedule->getNextOccurrence(),
            ];
        }

        return $optimalTimes;
    }

    /**
     * Get the next posting time for a persona
     */
    public function getNextPostingTime(BotPersona $persona): ?Carbon
    {
        // Check if persona can post
        if (!$persona->botUser || !$persona->botUser->canPost()) {
            return null;
        }

        $schedules = $persona->postingSchedules()
            ->active()
            ->orderBy('priority')
            ->get();

        if ($schedules->isEmpty()) {
            return null;
        }

        $now = Carbon::now();
        $nextTimes = [];

        foreach ($schedules as $schedule) {
            $nextOccurrence = $schedule->getNextOccurrence();
            
            // Add some randomness to avoid all bots posting at the same time
            $randomMinutes = rand(-15, 15);
            $nextOccurrence->addMinutes($randomMinutes);
            
            $nextTimes[] = $nextOccurrence;
        }

        // Return the earliest time
        return collect($nextTimes)->min();
    }

    /**
     * Check if it's a good time to post for any persona
     */
    public function getPersonasReadyToPost(): array
    {
        $personas = BotPersona::active()
            ->with(['botUser', 'postingSchedules'])
            ->whereHas('botUser')
            ->get();

        $readyPersonas = [];
        $now = Carbon::now();

        foreach ($personas as $persona) {
            if (!$persona->botUser->canPost()) {
                continue;
            }

            $nextPostTime = $this->getNextPostingTime($persona);
            
            if ($nextPostTime && $nextPostTime->lte($now->copy()->addMinutes(30))) {
                $readyPersonas[] = [
                    'persona' => $persona,
                    'scheduled_time' => $nextPostTime,
                    'minutes_until_post' => $now->diffInMinutes($nextPostTime, false),
                ];
            }
        }

        return $readyPersonas;
    }

    /**
     * Distribute posting times to avoid conflicts
     */
    public function distributePostingTimes(array $personas, Carbon $startTime, Carbon $endTime): array
    {
        $totalPersonas = count($personas);
        $timeWindow = $endTime->diffInMinutes($startTime);
        $interval = max(30, floor($timeWindow / $totalPersonas)); // Minimum 30 minutes apart

        $distributedTimes = [];
        $currentTime = $startTime->copy();

        foreach ($personas as $persona) {
            $distributedTimes[$persona['id']] = $currentTime->copy();
            $currentTime->addMinutes($interval);
            
            // Add some randomness
            $randomOffset = rand(-10, 10);
            $distributedTimes[$persona['id']]->addMinutes($randomOffset);
        }

        return $distributedTimes;
    }

    /**
     * Analyze posting patterns and suggest optimizations
     */
    public function analyzePostingPatterns(): array
    {
        $analysis = [
            'total_personas' => BotPersona::active()->count(),
            'posting_frequency' => [],
            'time_distribution' => [],
            'engagement_by_time' => [],
            'recommendations' => [],
        ];

        // Analyze posting frequency
        $personas = BotPersona::active()->with('automatedPosts')->get();
        
        foreach ($personas as $persona) {
            $recentPosts = $persona->automatedPosts()
                ->where('posted_at', '>=', now()->subDays(30))
                ->where('status', 'posted')
                ->get();

            $analysis['posting_frequency'][$persona->id] = [
                'persona_name' => $persona->full_name,
                'posts_last_30_days' => $recentPosts->count(),
                'average_per_week' => $recentPosts->count() / 4.3,
                'target_per_week' => $persona->posts_per_week,
                'on_target' => abs(($recentPosts->count() / 4.3) - $persona->posts_per_week) <= 1,
            ];
        }

        // Analyze time distribution
        $allPosts = AutomatedPost::where('posted_at', '>=', now()->subDays(30))
            ->where('status', 'posted')
            ->get();

        $hourlyDistribution = [];
        foreach ($allPosts as $post) {
            $hour = $post->posted_at->hour;
            $hourlyDistribution[$hour] = ($hourlyDistribution[$hour] ?? 0) + 1;
        }

        $analysis['time_distribution'] = $hourlyDistribution;

        // Generate recommendations
        $analysis['recommendations'] = $this->generateRecommendations($analysis);

        return $analysis;
    }

    /**
     * Generate recommendations based on analysis
     */
    private function generateRecommendations(array $analysis): array
    {
        $recommendations = [];

        // Check for posting frequency issues
        foreach ($analysis['posting_frequency'] as $personaData) {
            if (!$personaData['on_target']) {
                if ($personaData['average_per_week'] < $personaData['target_per_week']) {
                    $recommendations[] = [
                        'type' => 'frequency',
                        'persona' => $personaData['persona_name'],
                        'message' => "Increase posting frequency - currently {$personaData['average_per_week']}/week, target {$personaData['target_per_week']}/week",
                        'priority' => 'medium',
                    ];
                } else {
                    $recommendations[] = [
                        'type' => 'frequency',
                        'persona' => $personaData['persona_name'],
                        'message' => "Reduce posting frequency - currently {$personaData['average_per_week']}/week, target {$personaData['target_per_week']}/week",
                        'priority' => 'low',
                    ];
                }
            }
        }

        // Check for time distribution issues
        $timeDistribution = $analysis['time_distribution'];
        $totalPosts = array_sum($timeDistribution);
        
        if ($totalPosts > 0) {
            // Check for peak hours (should be 9-11 AM and 7-9 PM)
            $morningPeak = ($timeDistribution[9] ?? 0) + ($timeDistribution[10] ?? 0) + ($timeDistribution[11] ?? 0);
            $eveningPeak = ($timeDistribution[19] ?? 0) + ($timeDistribution[20] ?? 0) + ($timeDistribution[21] ?? 0);
            $peakRatio = ($morningPeak + $eveningPeak) / $totalPosts;

            if ($peakRatio < 0.4) {
                $recommendations[] = [
                    'type' => 'timing',
                    'message' => 'Consider scheduling more posts during peak engagement hours (9-11 AM and 7-9 PM)',
                    'priority' => 'high',
                ];
            }

            // Check for off-hours posting
            $offHours = ($timeDistribution[0] ?? 0) + ($timeDistribution[1] ?? 0) + ($timeDistribution[2] ?? 0) + 
                       ($timeDistribution[3] ?? 0) + ($timeDistribution[4] ?? 0) + ($timeDistribution[5] ?? 0);
            $offHoursRatio = $offHours / $totalPosts;

            if ($offHoursRatio > 0.1) {
                $recommendations[] = [
                    'type' => 'timing',
                    'message' => 'Too many posts during off-hours (12 AM - 6 AM) - consider rescheduling',
                    'priority' => 'medium',
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Update posting schedules based on performance data
     */
    public function optimizeSchedules(): array
    {
        $results = [];
        $personas = BotPersona::active()->with(['automatedPosts', 'postingSchedules'])->get();

        foreach ($personas as $persona) {
            $optimization = $this->optimizePersonaSchedule($persona);
            $results[] = $optimization;
        }

        return $results;
    }

    /**
     * Optimize schedule for a specific persona
     */
    private function optimizePersonaSchedule(BotPersona $persona): array
    {
        $recentPosts = $persona->automatedPosts()
            ->where('posted_at', '>=', now()->subDays(30))
            ->where('status', 'posted')
            ->with('engagement_data')
            ->get();

        if ($recentPosts->isEmpty()) {
            return [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
                'optimized' => false,
                'reason' => 'No recent posts to analyze',
            ];
        }

        // Analyze engagement by hour
        $engagementByHour = [];
        foreach ($recentPosts as $post) {
            $hour = $post->posted_at->hour;
            $engagement = ($post->engagement_data['likes'] ?? 0) + ($post->engagement_data['comments'] ?? 0);
            
            if (!isset($engagementByHour[$hour])) {
                $engagementByHour[$hour] = ['total' => 0, 'count' => 0];
            }
            
            $engagementByHour[$hour]['total'] += $engagement;
            $engagementByHour[$hour]['count']++;
        }

        // Calculate average engagement per hour
        $avgEngagementByHour = [];
        foreach ($engagementByHour as $hour => $data) {
            $avgEngagementByHour[$hour] = $data['total'] / $data['count'];
        }

        // Find best performing hours
        arsort($avgEngagementByHour);
        $bestHours = array_slice(array_keys($avgEngagementByHour), 0, $persona->posts_per_week, true);

        Log::info('Schedule optimization completed', [
            'persona_id' => $persona->id,
            'best_hours' => $bestHours,
            'current_schedules' => $persona->postingSchedules->count(),
        ]);

        return [
            'persona_id' => $persona->id,
            'persona_name' => $persona->full_name,
            'optimized' => true,
            'best_hours' => $bestHours,
            'avg_engagement_by_hour' => $avgEngagementByHour,
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BotUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bot_persona_id',
        'profile_image_url',
        'is_verified',
        'follower_count',
        'following_count',
        'post_count',
        'last_posted_at',
        'engagement_metrics',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'follower_count' => 'integer',
        'following_count' => 'integer',
        'post_count' => 'integer',
        'last_posted_at' => 'datetime',
        'engagement_metrics' => 'array',
    ];

    /**
     * Get the user that owns this bot account
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the persona this bot represents
     */
    public function botPersona(): BelongsTo
    {
        return $this->belongsTo(BotPersona::class);
    }

    /**
     * Get all automated posts for this bot user
     */
    public function automatedPosts(): Has<PERSON>any
    {
        return $this->hasMany(AutomatedPost::class);
    }

    /**
     * Update engagement metrics
     */
    public function updateEngagementMetrics(array $metrics): void
    {
        $currentMetrics = $this->engagement_metrics ?? [];
        $this->engagement_metrics = array_merge($currentMetrics, $metrics);
        $this->save();
    }

    /**
     * Increment post count
     */
    public function incrementPostCount(): void
    {
        $this->increment('post_count');
        $this->last_posted_at = now();
        $this->save();
    }

    /**
     * Get average engagement rate
     */
    public function getAverageEngagementRate(): float
    {
        $metrics = $this->engagement_metrics ?? [];
        
        if (empty($metrics['total_posts']) || $metrics['total_posts'] == 0) {
            return 0.0;
        }

        $totalLikes = $metrics['total_likes'] ?? 0;
        $totalComments = $metrics['total_comments'] ?? 0;
        $totalPosts = $metrics['total_posts'];

        return ($totalLikes + $totalComments) / $totalPosts;
    }

    /**
     * Get posting frequency (posts per week)
     */
    public function getPostingFrequency(): float
    {
        $firstPost = $this->automatedPosts()
            ->where('status', 'posted')
            ->orderBy('posted_at')
            ->first();

        if (!$firstPost) {
            return 0.0;
        }

        $daysSinceFirst = now()->diffInDays($firstPost->posted_at);
        $weeksSinceFirst = max($daysSinceFirst / 7, 1);

        return $this->post_count / $weeksSinceFirst;
    }

    /**
     * Check if bot can post (rate limiting for 4 posts per day)
     */
    public function canPost(): bool
    {
        // Check if last post was too recent (minimum 6 hours between posts for 4 posts per day)
        if ($this->last_posted_at && $this->last_posted_at->gt(now()->subHours(6))) {
            return false;
        }

        // Check daily limit (4 posts per day)
        $dailyLimit = 4;
        $todayPosts = $this->automatedPosts()
            ->where('posted_at', '>=', now()->startOfDay())
            ->where('status', 'posted')
            ->count();

        if ($todayPosts >= $dailyLimit) {
            return false;
        }

        // Check weekly limit based on persona settings (should be 28 for 4 posts per day)
        $weeklyLimit = $this->botPersona->posts_per_week ?? 28;
        $thisWeekPosts = $this->automatedPosts()
            ->where('posted_at', '>=', now()->startOfWeek())
            ->where('status', 'posted')
            ->count();

        return $thisWeekPosts < $weeklyLimit;
    }
}

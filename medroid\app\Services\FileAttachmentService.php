<?php

namespace App\Services;

use App\Models\File;
use App\Models\FileUsage;
use Illuminate\Database\Eloquent\Model;

class FileAttachmentService
{
    /**
     * Attach files to a model
     */
    public function attachFiles(Model $model, array $fileIds, string $usageType)
    {
        foreach ($fileIds as $fileId) {
            $this->attachFile($model, $fileId, $usageType);
        }

        return $model;
    }

    /**
     * Attach a single file to a model
     */
    public function attachFile(Model $model, int $fileId, string $usageType)
    {
        // Verify file exists and belongs to the same user (for security)
        $file = File::find($fileId);
        if (!$file) {
            throw new \Exception("File with ID {$fileId} not found");
        }

        // For user-owned models, ensure file belongs to the same user
        if (method_exists($model, 'user') && $model->user) {
            if ($file->user_id !== $model->user->id) {
                throw new \Exception("File does not belong to the model owner");
            }
        }

        // Create or update file usage
        FileUsage::updateOrCreate([
            'file_id' => $fileId,
            'usable_type' => get_class($model),
            'usable_id' => $model->id,
            'usage_type' => $usageType,
        ]);

        return $model;
    }

    /**
     * Detach files from a model
     */
    public function detachFiles(Model $model, array $fileIds = null, string $usageType = null)
    {
        $query = FileUsage::where('usable_type', get_class($model))
                          ->where('usable_id', $model->id);

        if ($fileIds) {
            $query->whereIn('file_id', $fileIds);
        }

        if ($usageType) {
            $query->where('usage_type', $usageType);
        }

        return $query->delete();
    }

    /**
     * Replace files for a specific usage type
     */
    public function replaceFiles(Model $model, array $fileIds, string $usageType)
    {
        // Remove existing files of this type
        $this->detachFiles($model, null, $usageType);

        // Attach new files
        $this->attachFiles($model, $fileIds, $usageType);

        return $model;
    }

    /**
     * Get files attached to a model
     */
    public function getAttachedFiles(Model $model, string $usageType = null)
    {
        $query = FileUsage::where('usable_type', get_class($model))
                          ->where('usable_id', $model->id)
                          ->with('file');

        if ($usageType) {
            $query->where('usage_type', $usageType);
        }

        return $query->get()->pluck('file');
    }

    /**
     * Check if a file is in use
     */
    public function isFileInUse(int $fileId)
    {
        return FileUsage::where('file_id', $fileId)->exists();
    }

    /**
     * Get usage details for a file
     */
    public function getFileUsageDetails(int $fileId)
    {
        return FileUsage::where('file_id', $fileId)
                       ->with('usable')
                       ->get()
                       ->map(function ($usage) {
                           return [
                               'model_type' => $usage->usable_type,
                               'model_id' => $usage->usable_id,
                               'usage_type' => $usage->usage_type,
                               'model_name' => $this->getModelDisplayName($usage->usable),
                               'created_at' => $usage->created_at,
                           ];
                       });
    }

    /**
     * Get display name for a model
     */
    private function getModelDisplayName($model)
    {
        if (!$model) {
            return 'Unknown';
        }

        // Try common name attributes
        $nameAttributes = ['name', 'title', 'email', 'slug'];
        
        foreach ($nameAttributes as $attribute) {
            if (isset($model->$attribute)) {
                return $model->$attribute;
            }
        }

        // Fallback to model type and ID
        return class_basename($model) . ' #' . $model->id;
    }

    /**
     * Clean up orphaned file usages
     */
    public function cleanupOrphanedUsages()
    {
        $orphanedUsages = FileUsage::whereDoesntHave('file')->get();
        
        foreach ($orphanedUsages as $usage) {
            $usage->delete();
        }

        return $orphanedUsages->count();
    }

    /**
     * Get usage statistics
     */
    public function getUsageStatistics()
    {
        return [
            'total_usages' => FileUsage::count(),
            'files_in_use' => FileUsage::distinct('file_id')->count(),
            'usage_by_type' => FileUsage::selectRaw('usage_type, COUNT(*) as count')
                                      ->groupBy('usage_type')
                                      ->pluck('count', 'usage_type'),
            'usage_by_model' => FileUsage::selectRaw('usable_type, COUNT(*) as count')
                                        ->groupBy('usable_type')
                                        ->pluck('count', 'usable_type'),
        ];
    }

    /**
     * Migrate file from old storage to new file management system
     */
    public function migrateFileToSystem(string $oldPath, int $userId, string $category = 'general', string $name = null)
    {
        // This method can be used to migrate existing files to the new system
        // Implementation would depend on your specific migration needs
        
        if (!file_exists($oldPath)) {
            throw new \Exception("File not found at path: {$oldPath}");
        }

        $fileInfo = pathinfo($oldPath);
        $mimeType = mime_content_type($oldPath);
        $size = filesize($oldPath);

        // Create file record
        $file = File::create([
            'user_id' => $userId,
            'name' => $name ?: $fileInfo['filename'],
            'original_name' => $fileInfo['basename'],
            'path' => $oldPath, // You might want to move the file to the new location
            'disk' => 'public',
            'mime_type' => $mimeType,
            'size' => $size,
            'category' => $category,
            'extension' => $fileInfo['extension'] ?? '',
        ]);

        return $file;
    }
}

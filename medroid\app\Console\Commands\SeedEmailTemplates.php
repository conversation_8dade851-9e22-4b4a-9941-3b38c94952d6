<?php

namespace App\Console\Commands;

use Database\Seeders\EmailTemplateSeeder;
use Illuminate\Console\Command;

class SeedEmailTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-templates:seed 
                            {--force : Force update existing templates}
                            {--template= : Seed specific template by slug}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed email templates from template files';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 Starting email template seeding...');
        
        if ($this->option('force')) {
            $this->warn('⚠️  Force mode enabled - existing templates will be updated');
        }
        
        if ($template = $this->option('template')) {
            $this->info("📧 Seeding specific template: {$template}");
        }
        
        try {
            $seeder = new EmailTemplateSeeder();
            
            // Set command instance for output
            $seeder->setCommand($this);
            
            // Run the seeder
            $seeder->run();
            
            $this->newLine();
            $this->info('✅ Email templates seeded successfully!');
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to seed email templates: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            
            return Command::FAILURE;
        }
    }
}

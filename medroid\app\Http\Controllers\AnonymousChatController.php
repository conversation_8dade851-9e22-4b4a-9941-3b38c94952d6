<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\ChatConversation;
use App\Services\ChatService;
use App\Services\AIService;
use Illuminate\Support\Str;

class AnonymousChatController extends Controller
{
    /**
     * The chat service instance.
     *
     * @var ChatService
     */
    protected $chatService;

    /**
     * The AI service instance.
     *
     * @var AIService
     */
    protected $aiService;

    /**
     * Create a new controller instance.
     *
     * @param ChatService $chatService
     * @param AIService $aiService
     * @return void
     */
    public function __construct(ChatService $chatService, AIService $aiService)
    {
        $this->chatService = $chatService;
        $this->aiService = $aiService;
    }

    /**
     * Start a new anonymous conversation.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function startConversation(Request $request)
    {
        try {
            // Generate a unique anonymous ID for this session
            $anonymousId = Str::uuid()->toString();

            // Log the attempt to create an anonymous conversation
            \Illuminate\Support\Facades\Log::info('Attempting to create anonymous conversation', [
                'anonymous_id' => $anonymousId
            ]);

            // Create a new conversation with anonymous flag
            $conversation = new ChatConversation([
                'anonymous_id' => $anonymousId,
                'title' => 'New Conversation',
                'mode' => 'consultation', // Default mode for new conversations
                'messages' => [],
                'health_concerns' => [],
                'recommendations' => [],
                'escalated' => false,
                'is_public' => false,
                'is_anonymous' => true,
                'patient_id' => null, // Explicitly set patient_id to null for anonymous conversations
            ]);

            $conversation->save();

            // Log successful creation
            \Illuminate\Support\Facades\Log::info('Anonymous conversation created successfully', [
                'conversation_id' => $conversation->id,
                'anonymous_id' => $anonymousId
            ]);

            // Use a smaller response to avoid potential issues with large responses
            $responseData = [
                'conversation_id' => $conversation->id,
                'anonymous_id' => $anonymousId,
                'message' => 'Anonymous conversation started successfully'
            ];

            // Log the response being sent
            \Illuminate\Support\Facades\Log::info('Sending anonymous chat response', [
                'response_data' => $responseData
            ]);

            return response()->json($responseData, 200, [
                'Content-Type' => 'application/json; charset=utf-8',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Failed to create anonymous conversation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to start anonymous conversation: ' . $e->getMessage()
            ], 500, [
                'Content-Type' => 'application/json',
                'Connection' => 'close' // Close the connection after sending the response
            ]);
        }
    }

    /**
     * Send a message to the AI assistant and get a response.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|string',
            'message' => 'required|string',
            'anonymous_id' => 'required|string',
            'request_full_response' => 'boolean',
            'generate_title' => 'boolean',
            'gender' => 'nullable|string|in:male,female,other,prefer_not_to_say',
            'age' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        $conversationId = $request->input('conversation_id');
        $anonymousId = $request->input('anonymous_id');
        $message = $request->input('message');
        $requestFullResponse = $request->input('request_full_response', false);
        $generateTitle = $request->input('generate_title', true);
        $gender = $request->input('gender');
        $age = $request->input('age');
        $mode = $request->input('mode', 'consultation'); // Default to consultation mode

        // Find the conversation
        $conversation = ChatConversation::find($conversationId);

        if (!$conversation) {
            return response()->json([
                'message' => 'Conversation not found'
            ], 404, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        // Update conversation mode if it's different from current mode
        if ($conversation->mode !== $mode) {
            $conversation->mode = $mode;
            $conversation->save();
        }

        // Verify this is an anonymous conversation and the anonymous ID matches
        if (!isset($conversation->is_anonymous) || !$conversation->is_anonymous ||
            !isset($conversation->anonymous_id) || $conversation->anonymous_id !== $anonymousId) {
            return response()->json([
                'message' => 'Unauthorized access to this conversation'
            ], 403, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        // Add the user message to the conversation
        $userMessage = [
            'content' => $message,
            'role' => 'user',
            'timestamp' => now(),
        ];

        $messages = $conversation->messages ?? [];
        $messages[] = $userMessage;
        $conversation->messages = $messages;
        $conversation->save();

        // Check if user has appointment booking intent BEFORE generating AI response
        // Pass conversation context for intelligent detection
        $conversationContext = $conversation->messages ?? [];
        $hasExplicitConsent = $this->aiService->checkIfAuthRequired($message, $conversationContext);

        // If user wants to book appointment, require auth immediately without AI response
        if ($hasExplicitConsent) {
            // User message is already added above, just return auth requirement immediately without generating AI response
            return response()->json([
                'message' => 'To proceed with booking your appointment and view available time slots, you\'ll need to sign in or create an account first.',
                'conversation_id' => $conversation->id,
                'anonymous_id' => $anonymousId,
                'title' => $conversation->title,
                'escalated' => $conversation->escalated,
                'escalation_reason' => $conversation->escalation_reason ?? null,
                'requires_auth' => true, // Require auth immediately
                'has_appointment_intent' => false, // Not needed since requires_auth is true
                'has_explicit_consent' => true,
                'available_slots' => null,
            ], 200, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        // Generate AI response - use the same method as authenticated chat
        try {
            // Create demographic context if gender or age is provided
            $demographicContext = '';
            if ($gender || $age) {
                $demographicContext = "Anonymous user demographics:\n";

                if ($gender) {
                    $demographicContext .= "- Gender: {$gender}\n";
                }

                if ($age) {
                    $demographicContext .= "- Age group: {$age}\n";
                }

                $demographicContext .= "\n";
            }

            // Generate AI response based on selected mode
            if ($mode === 'maya') {
                // Use Maya wellness service for wellness mode
                $wellnessService = new \App\Services\HealthWellnessService();

                // Pass search preference to Maya service
                $searchEnabled = $request->search_enabled ?? true;
                $mayaResponse = $wellnessService->chat(
                    $request->message,
                    $conversation->messages ?? [],
                    null, // No patient ID for anonymous chat
                    $conversation->id,
                    $searchEnabled
                );

                if ($mayaResponse['success']) {
                    $aiResponse = [
                        'message' => $mayaResponse['response'],
                        'health_concerns' => [],
                        'recommendations' => [],
                        'escalated' => false,
                        'mode' => 'maya',
                        'search_results' => $mayaResponse['search_results'] ?? null // Include search results for citations
                    ];
                } else {
                    throw new \Exception('Maya service error: ' . ($mayaResponse['error'] ?? 'Unknown error'));
                }
            } else {
                // Use medical consultation service for AI Doctor mode (default)
                $aiResponse = $this->aiService->generateChatResponse(
                    $conversation,
                    $request->message,
                    includePatientContext: false,
                    requestFullResponse: false,
                    additionalContext: $demographicContext
                );
                $aiResponse['mode'] = 'consultation';
            }
        } catch (\Exception $e) {
            Log::error('Error generating AI response: ' . $e->getMessage());

            // Provide different fallback responses based on error type
            if (str_contains($e->getMessage(), 'timeout') || str_contains($e->getMessage(), 'timed out')) {
                $aiResponse = [
                    'message' => 'I apologize, but my response is taking longer than expected due to high server load. Please try asking your question again, and I\'ll do my best to respond quickly.',
                    'escalated' => false,
                ];
            } else if (str_contains($e->getMessage(), 'connection') || str_contains($e->getMessage(), 'network')) {
                $aiResponse = [
                    'message' => 'I\'m experiencing connectivity issues with our AI service. Please check your internet connection and try again.',
                    'escalated' => false,
                ];
            } else {
                $aiResponse = [
                    'message' => 'I apologize, but I encountered an error processing your request. Please try again or rephrase your question.',
                    'escalated' => false,
                ];
            }
        }

        // Add the AI response to the conversation
        $assistantMessage = [
            'content' => $aiResponse['message'],
            'role' => 'assistant',
            'timestamp' => now(),
        ];

        $messages = $conversation->messages ?? [];
        $messages[] = $assistantMessage;
        $conversation->messages = $messages;

        // Generate or update title if requested - use the same method as authenticated chat
        if ($generateTitle && (!$conversation->title || $conversation->title === 'New Conversation')) {
            try {
                // Extract conversation text
                $conversationText = '';
                foreach ($conversation->messages as $msg) {
                    $role = $msg['role'] ?? 'unknown';
                    $content = $msg['content'] ?? '';
                    $conversationText .= "{$role}: {$content}\n";
                }

                // Create a prompt for generating a title
                $prompt = "Generate a concise, descriptive title (max 50 characters) that summarizes the main topic of this conversation:\n\n{$conversationText}";

                // Use the same method as authenticated chat
                $title = $this->aiService->generateConversationTitle($conversation);

                // Ensure the title is not too long
                if (strlen($title) > 50) {
                    $title = substr($title, 0, 47) . '...';
                }

                $conversation->title = $title;
            } catch (\Exception $e) {
                Log::error('Error generating conversation title: ' . $e->getMessage());
                // Use a simple title based on the first message
                $firstMessage = $message;
                if (strlen($firstMessage) > 30) {
                    $firstMessage = substr($firstMessage, 0, 27) . '...';
                }
                $conversation->title = 'Chat: ' . $firstMessage;
            }
        }

        // Check if the conversation needs to be escalated
        if (isset($aiResponse['escalated']) && $aiResponse['escalated']) {
            $conversation->escalated = true;
            $conversation->escalation_reason = $aiResponse['escalation_reason'] ?? 'Medical concern detected';
        }

        // Save the updated conversation
        $conversation->save();

        // Check for appointment booking intent in the AI response (to see if AI is suggesting appointments)
        // Pass conversation context for more intelligent detection
        $conversationContext = $conversation->messages ?? [];
        $hasAppointmentIntent = $this->aiService->checkIfAuthRequired($aiResponse['message'], $conversationContext);

        // If there's appointment booking intent, modify the response message to be more direct
        if ($hasAppointmentIntent) {
            // Keep the original message but add a clear call-to-action
            $aiResponse['message'] = $aiResponse['message'] . "\n\nWould you like to book an appointment with a healthcare provider?";
        }

        // Return the AI response
        return response()->json([
            'message' => $aiResponse['message'],
            'conversation_id' => $conversation->id,
            'anonymous_id' => $anonymousId,
            'title' => $conversation->title,
            'escalated' => $conversation->escalated,
            'escalation_reason' => $conversation->escalation_reason ?? null,
            'requires_auth' => false, // Only true when handled above for appointment booking
            'has_appointment_intent' => $hasAppointmentIntent,
            'has_explicit_consent' => false, // Only true when handled above for appointment booking
            'available_slots' => null, // Always null for anonymous users
        ], 200, [
            'Content-Type' => 'application/json',
            'Connection' => 'close'
        ]);
    }

    /**
     * Get a specific anonymous conversation.
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConversation(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'anonymous_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        $anonymousId = $request->input('anonymous_id');

        // Find the conversation
        $conversation = ChatConversation::find($id);

        if (!$conversation) {
            return response()->json([
                'message' => 'Conversation not found'
            ], 404, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        // Verify this is an anonymous conversation and the anonymous ID matches
        if (!isset($conversation->is_anonymous) || !$conversation->is_anonymous ||
            !isset($conversation->anonymous_id) || $conversation->anonymous_id !== $anonymousId) {
            return response()->json([
                'message' => 'Unauthorized access to this conversation'
            ], 403, [
                'Content-Type' => 'application/json',
                'Connection' => 'close'
            ]);
        }

        return response()->json($conversation, 200, [
            'Content-Type' => 'application/json',
            'Connection' => 'close'
        ]);
    }

    /**
     * Request appointment slots for anonymous users
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function requestAppointmentSlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|string',
            'anonymous_id' => 'required|string',
            'service_category' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $conversationId = $request->input('conversation_id');
        $anonymousId = $request->input('anonymous_id');

        // Find the conversation
        $conversation = ChatConversation::find($conversationId);

        if (!$conversation) {
            return response()->json([
                'message' => 'Conversation not found'
            ], 404);
        }

        // Verify this is an anonymous conversation and the anonymous ID matches
        if (!isset($conversation->is_anonymous) || !$conversation->is_anonymous ||
            !isset($conversation->anonymous_id) || $conversation->anonymous_id !== $anonymousId) {
            return response()->json([
                'message' => 'Unauthorized access to this conversation'
            ], 403);
        }

        try {
            // Get available appointment slots
            $daysAhead = 14; // Look ahead 14 days
            $startDate = now()->format('Y-m-d');
            $endDate = now()->addDays($daysAhead)->format('Y-m-d');

            // Get providers and their available slots
            $providers = \App\Models\Provider::where('verification_status', 'verified')
                ->with(['services', 'availability'])
                ->get();

            $slots = [];
            foreach ($providers as $provider) {
                $providerSlots = app(\App\Http\Controllers\AppointmentController::class)
                    ->getProviderAvailableSlots($provider->id, $startDate, $endDate);

                if (!empty($providerSlots)) {
                    $slots = array_merge($slots, $providerSlots);
                }
            }

            return response()->json([
                'available_slots' => $slots,
                'message' => 'Available appointment slots retrieved successfully',
                'conversation_id' => $conversation->id,
                'anonymous_id' => $anonymousId,
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error getting appointment slots for anonymous user', [
                'error' => $e->getMessage(),
                'conversation_id' => $conversation->id
            ]);

            return response()->json([
                'message' => 'Error retrieving appointment slots: ' . $e->getMessage()
            ], 500);
        }
    }


}

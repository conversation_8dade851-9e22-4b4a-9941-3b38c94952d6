<?php

namespace App\Console\Commands;

use App\Models\File;
use App\Services\ImageOptimizationService;
use Illuminate\Console\Command;

class OptimizeImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'files:optimize-images
                            {--quality=85 : Image quality (1-100)}
                            {--generate-thumbnails : Generate thumbnails for images}
                            {--force : Force optimization even if already optimized}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize images and generate thumbnails';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $quality = (int) $this->option('quality');
        $generateThumbnails = $this->option('generate-thumbnails');
        $force = $this->option('force');

        $this->info('Starting image optimization...');

        $imageOptimizationService = app(ImageOptimizationService::class);

        // Get images that need optimization
        $query = File::images();

        if (!$force) {
            $query->where(function ($q) {
                $q->whereNull('metadata')
                  ->orWhereJsonDoesntContain('metadata', ['optimization']);
            });
        }

        $images = $query->get();

        if ($images->isEmpty()) {
            $this->info('No images found that need optimization.');
            return;
        }

        $this->info("Found {$images->count()} images to optimize.");

        $totalSavings = 0;
        $optimizedCount = 0;
        $thumbnailsGenerated = 0;

        $progressBar = $this->output->createProgressBar($images->count());
        $progressBar->start();

        foreach ($images as $image) {
            try {
                // Optimize image
                $result = $imageOptimizationService->optimizeImage($image, $quality);

                if ($result) {
                    $totalSavings += $result['savings'];
                    $optimizedCount++;
                }

                // Generate thumbnails if requested
                if ($generateThumbnails) {
                    $thumbnails = $imageOptimizationService->generateThumbnails($image);
                    if ($thumbnails) {
                        $thumbnailsGenerated += count($thumbnails);
                    }
                }

            } catch (\Exception $e) {
                $this->error("Failed to optimize {$image->name}: " . $e->getMessage());
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        // Display results
        $this->info("Optimization completed!");
        $this->info("- Optimized: {$optimizedCount} images");
        $this->info("- Total space saved: " . $this->formatBytes($totalSavings));

        if ($generateThumbnails) {
            $this->info("- Thumbnails generated: {$thumbnailsGenerated}");
        }

        // Show storage statistics
        $this->showStorageStats();
    }

    /**
     * Show storage statistics
     */
    private function showStorageStats()
    {
        $this->newLine();
        $this->info('Storage Statistics:');

        $totalFiles = File::count();
        $totalSize = File::sum('size');
        $imageFiles = File::images()->count();
        $imageSize = File::images()->sum('size');

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Files', number_format($totalFiles)],
                ['Total Size', $this->formatBytes($totalSize)],
                ['Image Files', number_format($imageFiles)],
                ['Image Size', $this->formatBytes($imageSize)],
                ['Average File Size', $this->formatBytes($totalFiles > 0 ? $totalSize / $totalFiles : 0)],
            ]
        );
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

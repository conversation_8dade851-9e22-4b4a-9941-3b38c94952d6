<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use App\Services\DashboardService;

class DashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display the dashboard (web route).
     */
    public function index(Request $request): Response|RedirectResponse
    {
        $user = $request->user();

        // Redirect patients to chat instead of dashboard
        if ($user && $user->role === 'patient') {
            return redirect('/chat');
        }

        $userData = null;

        // Load user permissions for the frontend
        if ($user) {
            $user->load('roles', 'permissions');
            $userData = $user->toArray();
            $userData['user_permissions'] = $user->getAllPermissions()->pluck('name')->toArray();
        }

        $stats = $this->dashboardService->getDashboardStats($user);
        $recentAppointments = $this->dashboardService->getRecentAppointments($user);
        $quickActions = $this->dashboardService->getQuickActions($user);

        return Inertia::render('Dashboard', [
            'user' => $userData,
            'stats' => $stats,
            'recentAppointments' => $recentAppointments,
            'quickActions' => $quickActions
        ]);
    }

    /**
     * Get dashboard data (API route).
     */
    public function getDashboardData(Request $request)
    {
        $user = $request->user();

        $stats = $this->dashboardService->getDashboardStats($user);
        $recentAppointments = $this->dashboardService->getRecentAppointments($user);
        $quickActions = $this->dashboardService->getQuickActions($user);

        return response()->json([
            'stats' => $stats,
            'recentAppointments' => $recentAppointments,
            'quickActions' => $quickActions
        ]);
    }
}

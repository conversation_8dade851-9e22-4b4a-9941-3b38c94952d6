<?php

namespace App\Services\Agents;

use App\Models\BotPersona;

class QualityAssuranceAgent extends BaseAgent
{
    protected function getAgentName(): string
    {
        return 'QualityAssuranceAgent';
    }

    protected function getDefaultOptions(): array
    {
        return [
            'temperature' => 0.3,
            'max_completion_tokens' => 600,
            'top_p' => 0.8,
        ];
    }

    protected function createSystemPrompt(array $context = []): string
    {
        return "You are a Content Quality Assurance Expert specializing in health and wellness social media content.

Your role is to review and optimize all generated content to ensure:
- Brand consistency and authentic persona voice
- Content quality and engagement potential
- Accuracy and appropriateness for health content
- Strategic alignment with goals
- Technical optimization for social media

QUALITY ASSESSMENT CRITERIA:
1. AUTHENTICITY - Does it sound like the persona?
2. ENGAGEMENT - Will it connect with the audience?
3. VALUE - Does it provide genuine insight or inspiration?
4. CLARITY - Is the message clear and well-structured?
5. APPROPRIATENESS - Is it suitable for health/wellness context?
6. OPTIMIZATION - Is it optimized for social media performance?

CONTENT REVIEW PROCESS:
1. Analyze each content element (caption, hashtags, visual prompt)
2. Identify strengths and areas for improvement
3. Provide specific optimization recommendations
4. Assign quality scores (0-100)
5. Ensure strategic coherence across all elements

OUTPUT FORMAT:
QUALITY_SCORE: [Overall score 0-100]
CAPTION_SCORE: [Caption quality 0-100]
HASHTAG_SCORE: [Hashtag strategy 0-100]
VISUAL_SCORE: [Visual prompt quality 0-100]
COHERENCE_SCORE: [Strategic alignment 0-100]

STRENGTHS: [What works well]
IMPROVEMENTS: [Specific recommendations]
FINAL_CAPTION: [Optimized caption if improvements needed]
FINAL_HASHTAGS: [Optimized hashtags if improvements needed]
FINAL_VISUAL_PROMPT: [Optimized visual prompt if improvements needed]

Focus on maintaining authenticity while maximizing engagement and value for the health/wellness community.";
    }

    public function process(array $input): array
    {
        $this->validateInput($input, ['persona', 'strategy', 'content']);
        
        $persona = $input['persona'];
        $strategy = $input['strategy'];
        $content = $input['content'];
        $context = $input['context'] ?? [];
        
        $userPrompt = $this->createUserPrompt($persona, $strategy, $content, $context);
        $systemPrompt = $this->createSystemPrompt($context);
        
        $response = $this->executeAgent($systemPrompt, $userPrompt);
        
        return $this->parseQualityResponse($response, $content);
    }

    protected function createUserPrompt(BotPersona $persona, array $strategy, array $content, array $context): string
    {
        $strategyData = $strategy['strategy'] ?? $strategy;
        
        $personalityTraits = is_array($persona->personality_traits) ? implode(', ', $persona->personality_traits) : ($persona->personality_traits ?? 'Authentic, helpful, knowledgeable');

        $prompt = "PERSONA PROFILE:
Name: {$persona->full_name} (@{$persona->handle})
Content Focus: {$persona->content_focus}
Tone: {$persona->tone}
Bio: {$persona->bio}
Personality: {$personalityTraits}

CONTENT STRATEGY:
Theme: {$strategyData['content_theme']}
Target Emotion: {$strategyData['target_emotion']}
Key Message: {$strategyData['key_message']}
Content Type: {$strategyData['content_type']}

GENERATED CONTENT TO REVIEW:

CAPTION:
" . ($content['caption']['caption'] ?? 'Not available') . "

HASHTAGS:
" . implode(' ', $content['hashtag']['hashtags'] ?? []) . "

VISUAL PROMPT:
" . ($content['visual']['visual_prompt'] ?? 'Not available') . "

CONTENT METRICS:
- Caption Length: " . ($content['caption']['character_count'] ?? 0) . " characters
- Word Count: " . ($content['caption']['word_count'] ?? 0) . " words
- Hashtag Count: " . ($content['hashtag']['hashtag_count'] ?? 0) . "
- Visual Prompt Length: " . ($content['visual']['prompt_length'] ?? 0) . " characters

QUALITY REQUIREMENTS:
1. Caption should be 150-400 words, authentic to persona voice
2. Hashtags should be 10-15 strategic tags mixing trending and niche
3. Visual prompt should generate professional, engaging imagery
4. All elements should work together cohesively
5. Content should provide genuine value to health/wellness audience

Please review all content elements and provide quality assessment with specific optimization recommendations.";

        return $prompt;
    }

    protected function parseQualityResponse(string $response, array $originalContent): array
    {
        // Parse quality scores
        $patterns = [
            'quality_score' => '/QUALITY_SCORE:\s*(\d+)/i',
            'caption_score' => '/CAPTION_SCORE:\s*(\d+)/i',
            'hashtag_score' => '/HASHTAG_SCORE:\s*(\d+)/i',
            'visual_score' => '/VISUAL_SCORE:\s*(\d+)/i',
            'coherence_score' => '/COHERENCE_SCORE:\s*(\d+)/i',
        ];

        $scores = [];
        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $response, $matches)) {
                $scores[$key] = (int) $matches[1];
            } else {
                $scores[$key] = 75; // Default score
            }
        }

        // Parse feedback sections
        $feedbackPatterns = [
            'strengths' => '/STRENGTHS:\s*(.*?)(?=IMPROVEMENTS:|$)/s',
            'improvements' => '/IMPROVEMENTS:\s*(.*?)(?=FINAL_CAPTION:|$)/s',
            'final_caption' => '/FINAL_CAPTION:\s*(.*?)(?=FINAL_HASHTAGS:|$)/s',
            'final_hashtags' => '/FINAL_HASHTAGS:\s*(.*?)(?=FINAL_VISUAL_PROMPT:|$)/s',
            'final_visual_prompt' => '/FINAL_VISUAL_PROMPT:\s*(.*?)$/s',
        ];

        $feedback = [];
        foreach ($feedbackPatterns as $key => $pattern) {
            if (preg_match($pattern, $response, $matches)) {
                $feedback[$key] = trim($matches[1]);
            }
        }

        // Determine final content (use optimized if provided, otherwise original)
        $finalContent = $this->determineFinalContent($originalContent, $feedback, $scores);

        return [
            'quality_scores' => $scores,
            'feedback' => $feedback,
            'final_content' => $finalContent,
            'needs_optimization' => $scores['quality_score'] < 80,
            'agent' => $this->agentName,
            'timestamp' => now()->toISOString(),
        ];
    }

    protected function determineFinalContent(array $originalContent, array $feedback, array $scores): array
    {
        $finalContent = [
            'caption' => $originalContent['caption']['caption'] ?? '',
            'hashtags' => $originalContent['hashtag']['hashtags'] ?? [],
            'visual_prompt' => $originalContent['visual']['visual_prompt'] ?? '',
        ];

        // Use optimized content if quality scores are low and improvements are provided
        if ($scores['caption_score'] < 80 && !empty($feedback['final_caption'])) {
            $finalContent['caption'] = $this->cleanOptimizedCaption($feedback['final_caption']);
        }

        if ($scores['hashtag_score'] < 80 && !empty($feedback['final_hashtags'])) {
            // Parse hashtags from the feedback
            $hashtagText = $feedback['final_hashtags'];
            preg_match_all('/#[\w\d_]+/i', $hashtagText, $matches);
            if (!empty($matches[0])) {
                $finalContent['hashtags'] = $matches[0];
            }
        }

        if ($scores['visual_score'] < 80 && !empty($feedback['final_visual_prompt'])) {
            $finalContent['visual_prompt'] = $feedback['final_visual_prompt'];
        }

        return $finalContent;
    }

    /**
     * Clean optimized caption from AI artifacts
     */
    protected function cleanOptimizedCaption(string $caption): string
    {
        // Remove common AI response artifacts
        $caption = preg_replace('/^FINAL_CAPTION:\s*/i', '', $caption);
        $caption = preg_replace('/^\*\*\s*$/m', '', $caption); // Remove standalone **
        $caption = preg_replace('/^Here\'s an optimized caption.*?:\s*/i', '', $caption);
        $caption = preg_replace('/^Here\'s an? .*? caption.*?:\s*/i', '', $caption);
        $caption = preg_replace('/^\*\*\s*Here\'s.*?:\s*/i', '', $caption);
        $caption = preg_replace('/^\*\*\s*\n/', '', $caption); // Remove ** at start of new lines

        // Remove quotes if the entire caption is wrapped in them
        $caption = preg_replace('/^"(.*)"$/s', '$1', $caption);
        $caption = preg_replace('/^\'(.*)\'$/s', '$1', $caption);

        $caption = trim($caption);

        // Remove any remaining ** artifacts
        $caption = str_replace('**', '', $caption);

        // Ensure proper spacing
        $caption = preg_replace('/\s+/', ' ', $caption);

        return $caption;
    }

    protected function calculateOverallQuality(array $scores): int
    {
        $weights = [
            'caption_score' => 0.4,
            'hashtag_score' => 0.2,
            'visual_score' => 0.3,
            'coherence_score' => 0.1,
        ];

        $weightedSum = 0;
        $totalWeight = 0;

        foreach ($weights as $scoreKey => $weight) {
            if (isset($scores[$scoreKey])) {
                $weightedSum += $scores[$scoreKey] * $weight;
                $totalWeight += $weight;
            }
        }

        return $totalWeight > 0 ? (int) round($weightedSum / $totalWeight) : 75;
    }

    public function test(): array
    {
        $testPersona = new BotPersona();
        $testPersona->first_name = 'Test';
        $testPersona->last_name = 'Persona';
        $testPersona->handle = 'test_persona';
        $testPersona->content_focus = 'fitness';
        $testPersona->tone = 'motivational';
        $testPersona->bio = 'Fitness coach helping others achieve their goals';
        $testPersona->personality_traits = ['energetic', 'supportive', 'knowledgeable'];

        $testStrategy = [
            'strategy' => [
                'content_theme' => 'Morning workout motivation',
                'target_emotion' => 'motivation',
                'key_message' => 'Start your day strong',
                'content_type' => 'motivational',
            ]
        ];

        $testContent = [
            'caption' => [
                'caption' => 'Good morning! Ready to crush your workout today? Remember, every rep counts and every step forward is progress. What is your favorite way to start the day with movement?',
                'character_count' => 150,
                'word_count' => 30,
            ],
            'hashtag' => [
                'hashtags' => ['#fitness', '#motivation', '#workout', '#morningworkout', '#fitlife'],
                'hashtag_count' => 5,
            ],
            'visual' => [
                'visual_prompt' => 'Person doing morning workout in bright gym, energetic lighting --ar 1:1',
                'prompt_length' => 70,
            ],
        ];

        $result = $this->process([
            'persona' => $testPersona,
            'strategy' => $testStrategy,
            'content' => $testContent,
            'context' => ['test_mode' => true],
        ]);

        return [
            'agent' => $this->agentName,
            'test_successful' => isset($result['quality_scores']['quality_score']),
            'overall_quality' => $result['quality_scores']['quality_score'] ?? 0,
            'needs_optimization' => $result['needs_optimization'] ?? false,
            'feedback_provided' => !empty($result['feedback']['strengths']),
        ];
    }
}

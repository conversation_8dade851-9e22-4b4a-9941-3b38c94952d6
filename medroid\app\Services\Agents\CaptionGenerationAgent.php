<?php

namespace App\Services\Agents;

use App\Models\BotPersona;

class CaptionGenerationAgent extends BaseAgent
{
    protected function getAgentName(): string
    {
        return 'CaptionGenerationAgent';
    }

    protected function getDefaultOptions(): array
    {
        return [
            'temperature' => 0.8,
            'max_completion_tokens' => 300, // Reduced for shorter content
            'top_p' => 0.9,
        ];
    }

    protected function createSystemPrompt(array $context = []): string
    {
        return "You are an Expert Social Media Caption Writer specializing in health and wellness content.

Your expertise includes:
- Creating authentic, engaging captions that match persona voice
- Incorporating storytelling and emotional connection
- Using proven engagement techniques (questions, calls-to-action, relatable experiences)
- Balancing educational value with entertainment
- Writing in a natural, conversational tone

CAPTION WRITING PRINCIPLES:
1. Hook - Start with something attention-grabbing
2. Value - Provide genuine insight, tip, or inspiration
3. Personality - Reflect the persona's unique voice and expertise
4. Connection - Create relatability and emotional resonance
5. Action - End with engagement (question, challenge, or call-to-action)

STYLE GUIDELINES:
- Write in first person from the persona's perspective
- Use natural, conversational social media language
- Keep it short, punchy, and engaging (like real social posts)
- Include personal touches and authentic experiences
- Use emojis naturally to enhance readability
- Aim for 50-120 words for optimal social media engagement
- Include line breaks for better mobile readability
- End with engaging questions or calls-to-action

SOCIAL MEDIA BEST PRACTICES:
- Start with a hook (question, statement, emoji)
- Share one key tip or insight
- Keep paragraphs short (1-2 sentences max)
- Use relatable, everyday language
- Include 2-3 relevant emojis maximum
- End with engagement (question, challenge, or call-to-action)

OUTPUT FORMAT:
CAPTION: [The complete caption text]

Focus on creating authentic, bite-sized content that feels like a real person sharing on social media.";
    }

    public function process(array $input): array
    {
        $this->validateInput($input, ['persona', 'strategy']);
        
        $persona = $input['persona'];
        $strategy = $input['strategy'];
        $context = $input['context'] ?? [];
        
        $userPrompt = $this->createUserPrompt($persona, $strategy, $context);
        $systemPrompt = $this->createSystemPrompt($context);
        
        $response = $this->executeAgent($systemPrompt, $userPrompt);
        
        return $this->parseCaptionResponse($response, $persona);
    }

    protected function createUserPrompt(BotPersona $persona, array $strategy, array $context): string
    {
        $strategyData = $strategy['strategy'] ?? $strategy;
        
        $personalityTraits = is_array($persona->personality_traits) ? implode(', ', $persona->personality_traits) : ($persona->personality_traits ?? 'Authentic, helpful, knowledgeable');

        $prompt = "PERSONA PROFILE:
Name: {$persona->full_name} (@{$persona->handle})
Content Focus: {$persona->content_focus}
Tone: {$persona->tone}
Bio: {$persona->bio}
Personality: {$personalityTraits}

CONTENT STRATEGY:
Theme: {$strategyData['content_theme']}
Target Emotion: {$strategyData['target_emotion']}
Key Message: {$strategyData['key_message']}
Content Type: {$strategyData['content_type']}
Engagement Hook: {$strategyData['engagement_hook']}
Tone Direction: {$strategyData['tone_direction']}

WRITING GUIDELINES FOR THIS PERSONA:
";

        // Add persona-specific writing style
        switch ($persona->tone) {
            case 'upbeat':
                $prompt .= "- Use energetic, positive language
- Include motivational phrases and excitement
- Show enthusiasm and optimism
- Use exclamation points naturally";
                break;
            case 'energetic':
                $prompt .= "- Write with high energy and motivation
- Use action-oriented language
- Include power words and strong verbs
- Create urgency and excitement";
                break;
            case 'warm':
                $prompt .= "- Use caring, nurturing language
- Include personal touches and empathy
- Create a sense of comfort and support
- Use inclusive, welcoming tone";
                break;
            case 'reflective':
                $prompt .= "- Use thoughtful, contemplative language
- Include deeper insights and wisdom
- Encourage self-reflection and mindfulness
- Use calm, measured tone";
                break;
            case 'instructional':
                $prompt .= "- Use clear, educational language
- Include step-by-step guidance
- Focus on practical, actionable advice
- Use structured, organized approach";
                break;
            case 'scientific':
                $prompt .= "- Use evidence-based language
- Include facts and research insights
- Explain the 'why' behind recommendations
- Use credible, authoritative tone";
                break;
            default:
                $prompt .= "- Use friendly, approachable language
- Balance information with personality
- Create connection through relatability
- Use conversational, natural tone";
        }

        $prompt .= "\n\nCONTENT FOCUS EXPERTISE:
";

        // Add content-specific guidance
        switch ($persona->content_focus) {
            case 'yoga':
                $prompt .= "- Share yoga wisdom, pose benefits, mindfulness insights
- Include breathing techniques, meditation tips
- Connect physical practice with mental/spiritual growth
- Use Sanskrit terms naturally when appropriate";
                break;
            case 'fitness':
                $prompt .= "- Share workout tips, form cues, training insights
- Include motivation for consistency and progress
- Discuss goal setting and achievement strategies
- Use fitness terminology appropriately";
                break;
            case 'nutrition':
                $prompt .= "- Share nutritional insights, recipe tips, meal ideas
- Include food science and health benefits
- Discuss sustainable eating habits
- Use nutrition terminology accurately";
                break;
            case 'mental health':
                $prompt .= "- Share mental wellness strategies, coping techniques
- Include emotional intelligence insights
- Discuss stress management and self-care
- Use empathetic, supportive language";
                break;
        }

        $prompt .= "\n\nWrite a short, punchy social media caption that embodies {$persona->first_name}'s authentic voice. Make it feel like a genuine, casual post from someone sharing their passion for health. Keep it conversational, relatable, and under 100 words. Use line breaks and emojis naturally.";

        return $prompt;
    }

    protected function parseCaptionResponse(string $response, BotPersona $persona): array
    {
        // Extract caption
        $captionMatch = [];
        if (preg_match('/CAPTION:\s*(.*?)$/s', $response, $captionMatch)) {
            $caption = trim($captionMatch[1]);
        } else {
            // Fallback: use entire response as caption
            $caption = trim($response);
        }

        // Clean up the caption
        $caption = $this->cleanCaption($caption);

        // Validate caption quality
        $quality = $this->validateCaptionQuality($caption, $persona);

        return [
            'caption' => $caption,
            'word_count' => str_word_count($caption),
            'character_count' => strlen($caption),
            'quality_score' => $quality['score'],
            'quality_issues' => $quality['issues'],
            'agent' => $this->agentName,
            'timestamp' => now()->toISOString(),
        ];
    }

    protected function cleanCaption(string $caption): string
    {
        // Remove any unwanted formatting and AI artifacts
        $caption = preg_replace('/^CAPTION:\s*/i', '', $caption);
        $caption = preg_replace('/^FINAL_CAPTION:\s*/i', '', $caption);

        // Remove common AI response artifacts
        $caption = preg_replace('/^\*\*\s*$/m', '', $caption); // Remove standalone **
        $caption = preg_replace('/^Here\'s an optimized caption.*?:\s*/i', '', $caption);
        $caption = preg_replace('/^Here\'s an? .*? caption.*?:\s*/i', '', $caption);
        $caption = preg_replace('/^\*\*\s*Here\'s.*?:\s*/i', '', $caption);
        $caption = preg_replace('/^\*\*\s*\n/', '', $caption); // Remove ** at start of new lines

        // Remove quotes if the entire caption is wrapped in them
        $caption = preg_replace('/^"(.*)"$/s', '$1', $caption);
        $caption = preg_replace('/^\'(.*)\'$/s', '$1', $caption);

        $caption = trim($caption);

        // Ensure proper spacing
        $caption = preg_replace('/\s+/', ' ', $caption);

        // Fix common formatting issues
        $caption = str_replace(['  ', ' .', ' ,', ' !', ' ?'], [' ', '.', ',', '!', '?'], $caption);

        // Remove any remaining ** artifacts
        $caption = str_replace('**', '', $caption);

        return $caption;
    }

    protected function validateCaptionQuality(string $caption, BotPersona $persona): array
    {
        $issues = [];
        $score = 100;

        // Check minimum length (social media optimized)
        if (strlen($caption) < 50) {
            $issues[] = 'Caption too short (minimum 50 characters)';
            $score -= 30;
        }

        // Check maximum length (social media optimized)
        if (strlen($caption) > 800) {
            $issues[] = 'Caption too long (maximum 800 characters for social media)';
            $score -= 20;
        }

        // Check for persona name consistency
        if (!str_contains(strtolower($caption), strtolower($persona->first_name)) && 
            !str_contains(strtolower($caption), 'i ') && 
            !str_contains(strtolower($caption), 'my ')) {
            $issues[] = 'Caption lacks personal voice';
            $score -= 15;
        }

        // Check for engagement elements
        if (!str_contains($caption, '?') && !str_contains(strtolower($caption), 'what') && 
            !str_contains(strtolower($caption), 'how') && !str_contains(strtolower($caption), 'share')) {
            $issues[] = 'Caption lacks engagement hook';
            $score -= 10;
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
        ];
    }

    public function test(): array
    {
        $testPersona = new BotPersona();
        $testPersona->first_name = 'Sarah';
        $testPersona->last_name = 'Wellness';
        $testPersona->handle = 'sarah_wellness';
        $testPersona->content_focus = 'yoga';
        $testPersona->tone = 'upbeat';
        $testPersona->bio = 'Yoga instructor spreading mindfulness';
        $testPersona->personality_traits = ['inspiring', 'peaceful', 'authentic'];

        $testStrategy = [
            'strategy' => [
                'content_theme' => 'Morning yoga routine',
                'target_emotion' => 'inspiration',
                'key_message' => 'Start your day with mindful movement',
                'content_type' => 'educational',
                'engagement_hook' => 'Ask about morning routines',
                'tone_direction' => 'upbeat and encouraging',
            ]
        ];

        $result = $this->process([
            'persona' => $testPersona,
            'strategy' => $testStrategy,
            'context' => ['test_mode' => true],
        ]);

        return [
            'agent' => $this->agentName,
            'test_successful' => !empty($result['caption']),
            'caption_length' => strlen($result['caption'] ?? ''),
            'quality_score' => $result['quality_score'] ?? 0,
            'sample_caption' => substr($result['caption'] ?? '', 0, 100) . '...',
        ];
    }
}

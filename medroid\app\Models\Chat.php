<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Chat extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'patient_id',
        'provider_id',
        'status',
        'title',
        'last_message_at',
        'is_flagged',
        'flag_reason',
        'flagged_by',
        'flagged_at',
        'is_archived',
        'archived_by',
        'archived_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_flagged' => 'boolean',
        'is_archived' => 'boolean',
        'last_message_at' => 'datetime',
        'flagged_at' => 'datetime',
        'archived_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the patient that owns the chat.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the provider that owns the chat.
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the messages for the chat.
     */
    public function messages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    /**
     * Get the user who flagged the chat.
     */
    public function flaggedBy()
    {
        return $this->belongsTo(User::class, 'flagged_by');
    }

    /**
     * Get the user who archived the chat.
     */
    public function archivedBy()
    {
        return $this->belongsTo(User::class, 'archived_by');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use App\Services\FileUploadService;

class ProductManagementController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        // Allow providers to access their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('view products')) {
            abort(403, 'Unauthorized');
        }

        $query = Product::with(['category', 'images', 'user'])
            ->orderBy('created_at', 'desc');

        // Admin role check first - admins get full access
        if (!$user->hasRole('admin')) {
            // Non-admin users (including providers) only see their own products
            $query->where('user_id', $user->id);
        } else {
            // For admin interface, show all products regardless of approval status
            // This allows admins to manage all products including pending ones
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $products = $query->paginate(20);
        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'products' => $products,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Index', [
            'products' => $products,
            'categories' => $categories,
            'filters' => $request->only(['category', 'type', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        $user = Auth::user();

        // Allow providers to create products without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            abort(403, 'Unauthorized');
        }

        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Create', [
            'categories' => $categories,
        ]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        // Allow providers to create products without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validationRules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'required|string|max:255|unique:products,sku',
            'stock_quantity' => 'required_if:type,physical|integer|min:0',
            'manage_stock' => 'nullable|boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_featured' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
            'featured_image_id' => 'nullable|integer|exists:files,id',
            'gallery_image_ids' => 'nullable|array',
            'gallery_image_ids.*' => 'integer|exists:files,id',
        ];

        // Make digital_files optional for now since the frontend may not always send it
        if ($request->type === 'digital') {
            $validationRules['digital_files'] = 'nullable|array';
        }

        $request->validate($validationRules);

        try {
            // Get featured image URL from file management system
            $featuredImageUrl = null;
            if ($request->featured_image_id) {
                $featuredImageFile = \App\Models\File::find($request->featured_image_id);
                if ($featuredImageFile && $featuredImageFile->user_id === $user->id) {
                    $featuredImageUrl = $featuredImageFile->url;
                }
            }

            // Determine approval status based on user role
            // Auto-approve products from providers and admins
            $approvalStatus = ($user->hasRole('admin') || $user->hasRole('provider')) ? 'approved' : 'pending';
            $approvedBy = ($user->hasRole('admin') || $user->hasRole('provider')) ? $user->id : null;
            $approvedAt = ($user->hasRole('admin') || $user->hasRole('provider')) ? now() : null;

            // Ensure stock_quantity is set correctly
            $stockQuantity = $request->type === 'physical' ? (int)($request->stock_quantity ?? 0) : 0;
            
            $product = Product::create([
                'user_id' => Auth::id(),
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'category_id' => (int)$request->category_id,
                'price' => (float)$request->price,
                'sale_price' => $request->sale_price ? (float)$request->sale_price : null,
                'sku' => $request->sku,
                'stock_quantity' => $stockQuantity,
                'manage_stock' => $request->type === 'physical' ? $this->convertToBoolean($request->manage_stock ?? true) : false,
                'in_stock' => $request->type === 'digital' ? true : ($stockQuantity > 0),
                'weight' => $request->weight ? (float)$request->weight : null,
                'dimensions' => $request->dimensions,
                'featured_image' => $featuredImageUrl,
                'is_featured' => $this->convertToBoolean($request->is_featured ?? false),
                'is_active' => $this->convertToBoolean($request->is_active ?? true),
                'digital_files' => $request->type === 'digital' ? ($request->digital_files ?? []) : null,
                'download_limit' => $request->download_limit ? (int)$request->download_limit : null,
                'download_expiry_days' => $request->download_expiry_days ? (int)$request->download_expiry_days : null,
                'approval_status' => $approvalStatus,
                'approved_by' => $approvedBy,
                'approved_at' => $approvedAt,
            ]);

            // Attach featured image to product using file management system
            if ($request->featured_image_id) {
                \App\Models\FileUsage::create([
                    'file_id' => $request->featured_image_id,
                    'usable_type' => get_class($product),
                    'usable_id' => $product->id,
                    'usage_type' => 'featured_image',
                ]);
            }

            // Handle gallery images using file management system
            if ($request->gallery_image_ids && is_array($request->gallery_image_ids)) {
                foreach ($request->gallery_image_ids as $index => $fileId) {
                    $galleryImageFile = \App\Models\File::find($fileId);
                    if ($galleryImageFile && $galleryImageFile->user_id === $user->id) {
                        // Create file usage record
                        \App\Models\FileUsage::create([
                            'file_id' => $fileId,
                            'usable_type' => get_class($product),
                            'usable_id' => $product->id,
                            'usage_type' => 'gallery_image',
                        ]);

                        // Create product image record for backward compatibility
                        $product->images()->create([
                            'image_path' => $galleryImageFile->path,
                            'alt_text' => $product->name . ' - Image ' . ($index + 1),
                            'sort_order' => $index,
                            'is_primary' => false,
                        ]);
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'product' => $product->load(['category', 'images']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function show(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to view their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('view products')) {
            abort(403, 'Unauthorized');
        }

        $product = Product::with(['category', 'images'])->findOrFail($id);

        // Check if user can access this product
        if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
            abort(403, 'Unauthorized - You can only view your own products');
        }

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
            ]);
        }

        return Inertia::render('Admin/Products/Show', [
            'product' => $product,
        ]);
    }

    public function edit(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to edit their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('edit products')) {
            abort(403, 'Unauthorized');
        }

        $product = Product::with(['category', 'images'])->findOrFail($id);

        // Admin role check first - admins get full access
        if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
            abort(403, 'Unauthorized - You can only edit your own products');
        }

        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Edit', [
            'product' => $product,
            'categories' => $categories,
        ]);
    }

    public function update(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to edit their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('edit products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $product = Product::findOrFail($id);

        // Admin role check first - admins get full access
        if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized - You can only edit your own products'], 403);
        }

        $validationRules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'required|string|max:255|unique:products,sku,' . $id,
            'stock_quantity' => 'required_if:type,physical|integer|min:0',
            'manage_stock' => 'nullable|in:0,1,true,false',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_featured' => 'nullable|in:0,1,true,false',
            'is_active' => 'nullable|in:0,1,true,false',
            'digital_files' => 'required_if:type,digital|array',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:10240',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
            'gallery_images' => 'nullable|array',
            'gallery_images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:10240',
            'featured_image_id' => 'nullable|integer|exists:files,id',
            'gallery_image_ids' => 'nullable|array',
            'gallery_image_ids.*' => 'integer|exists:files,id',
        ];

        // Make digital_files optional for now since the frontend may not always send it
        if ($request->type === 'digital') {
            $validationRules['digital_files'] = 'nullable|array';
        }

        $request->validate($validationRules);

        try {
            // Get featured image URL from file management system
            $featuredImageUrl = $product->featured_image; // Keep existing if no new image
            if ($request->featured_image_id) {
                $featuredImageFile = \App\Models\File::find($request->featured_image_id);
                if ($featuredImageFile && $featuredImageFile->user_id === $user->id) {
                    $featuredImageUrl = $featuredImageFile->url;
                }
            }

            $product->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'category_id' => $request->category_id,
                'price' => $request->price,
                'sale_price' => $request->sale_price,
                'sku' => $request->sku,
                'stock_quantity' => $request->type === 'physical' ? $request->stock_quantity : 0,
                'manage_stock' => $request->type === 'physical' ? $this->convertToBoolean($request->manage_stock ?? true) : false,
                'in_stock' => $request->type === 'digital' ? true : ($request->stock_quantity > 0),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'is_featured' => $this->convertToBoolean($request->is_featured ?? false),
                'is_active' => $this->convertToBoolean($request->is_active ?? true),
                'digital_files' => $request->type === 'digital' ? $request->digital_files : null,
                'download_limit' => $request->download_limit,
                'download_expiry_days' => $request->download_expiry_days,
                'featured_image' => $featuredImageUrl,
            ]);

            // Handle direct image uploads from frontend
            $hasNewImages = $request->hasFile('featured_image') || $request->hasFile('gallery_images') || $request->hasFile('images');

            // Debug logging
            \Log::info('Product update image check', [
                'product_id' => $product->id,
                'has_featured_image' => $request->hasFile('featured_image'),
                'has_gallery_images' => $request->hasFile('gallery_images'),
                'has_images' => $request->hasFile('images'),
                'has_new_images' => $hasNewImages,
                'request_files' => array_keys($request->allFiles()),
            ]);

            if ($hasNewImages) {
                // Clear existing images first
                $product->images()->delete();
                \App\Models\FileUsage::where('usable_type', get_class($product))
                    ->where('usable_id', $product->id)
                    ->whereIn('usage_type', ['featured_image', 'gallery_image'])
                    ->delete();

                $imageIndex = 0;

                // Handle featured image
                if ($request->hasFile('featured_image')) {
                    $featuredImage = $request->file('featured_image');
                    $imagePath = $featuredImage->store('products', 'public');

                    $product->images()->create([
                        'image_path' => $imagePath,
                        'alt_text' => $product->name . ' - Featured Image',
                        'sort_order' => $imageIndex,
                        'is_primary' => true,
                    ]);

                    // Update featured image URL for the product
                    $featuredImageUrl = asset('storage/' . $imagePath);
                    $imageIndex++;
                }

                // Handle gallery images
                if ($request->hasFile('gallery_images')) {
                    foreach ($request->file('gallery_images') as $galleryImage) {
                        $imagePath = $galleryImage->store('products', 'public');

                        $product->images()->create([
                            'image_path' => $imagePath,
                            'alt_text' => $product->name . ' - Gallery Image ' . ($imageIndex + 1),
                            'sort_order' => $imageIndex,
                            'is_primary' => false,
                        ]);
                        $imageIndex++;
                    }
                }

                // Handle legacy images array (for backward compatibility)
                if ($request->hasFile('images')) {
                    foreach ($request->file('images') as $image) {
                        $imagePath = $image->store('products', 'public');

                        $product->images()->create([
                            'image_path' => $imagePath,
                            'alt_text' => $product->name . ' - Image ' . ($imageIndex + 1),
                            'sort_order' => $imageIndex,
                            'is_primary' => $imageIndex === 0 && !$request->hasFile('featured_image'),
                        ]);
                        $imageIndex++;
                    }
                }

                // Update the product's featured_image field if it was changed
                if ($featuredImageUrl !== $product->featured_image) {
                    $product->update(['featured_image' => $featuredImageUrl]);
                }
            }

            // Handle featured image using file management system
            if ($request->featured_image_id) {
                // Remove old featured image file usage
                \App\Models\FileUsage::where('usable_type', get_class($product))
                    ->where('usable_id', $product->id)
                    ->where('usage_type', 'featured_image')
                    ->delete();

                // Create new featured image file usage
                \App\Models\FileUsage::create([
                    'file_id' => $request->featured_image_id,
                    'usable_type' => get_class($product),
                    'usable_id' => $product->id,
                    'usage_type' => 'featured_image',
                ]);
            }

            // Handle gallery images using file management system
            if ($request->gallery_image_ids && is_array($request->gallery_image_ids)) {
                // Remove old gallery image file usages
                \App\Models\FileUsage::where('usable_type', get_class($product))
                    ->where('usable_id', $product->id)
                    ->where('usage_type', 'gallery_image')
                    ->delete();

                // Clear existing product images for backward compatibility
                $product->images()->delete();

                foreach ($request->gallery_image_ids as $index => $fileId) {
                    $galleryImageFile = \App\Models\File::find($fileId);
                    if ($galleryImageFile && $galleryImageFile->user_id === $user->id) {
                        // Create file usage record
                        \App\Models\FileUsage::create([
                            'file_id' => $fileId,
                            'usable_type' => get_class($product),
                            'usable_id' => $product->id,
                            'usage_type' => 'gallery_image',
                        ]);

                        // Create product image record for backward compatibility
                        $product->images()->create([
                            'image_path' => $galleryImageFile->path,
                            'alt_text' => $product->name . ' - Image ' . ($index + 1),
                            'sort_order' => $index,
                            'is_primary' => false,
                        ]);
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'product' => $product->load(['category', 'images']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to delete their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('delete products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = Auth::user();

        try {
            $product = Product::findOrFail($id);

            // Admin role check first - admins get full access
            if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
                return response()->json(['message' => 'Unauthorized - You can only delete your own products'], 403);
            }

            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function toggleStatus(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to toggle status of their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('edit products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = Auth::user();

        try {
            $product = Product::findOrFail($id);

            // Admin role check first - admins get full access
            if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
                return response()->json(['message' => 'Unauthorized - You can only edit your own products'], 403);
            }

            $product->update(['is_active' => !$product->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Product status updated successfully',
                'product' => $product,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product status: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function downloadImportTemplate()
    {
        $user = Auth::user();

        // Allow providers to download import template without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            abort(403, 'Unauthorized');
        }

        try {
            // First try to serve the static file if it exists
            $templatePath = public_path('templates/product_import_template.csv');

            if (file_exists($templatePath)) {
                return response()->download($templatePath, 'product_import_template.csv', [
                    'Content-Type' => 'text/csv; charset=UTF-8',
                    'Cache-Control' => 'no-cache, no-store, must-revalidate',
                    'Pragma' => 'no-cache',
                    'Expires' => '0'
                ]);
            }

            // Fallback: Generate CSV content dynamically
            $headers = [
                'name',
                'description',
                'short_description',
                'type',
                'category_id',
                'price',
                'sale_price',
                'sku',
                'stock_quantity',
                'weight',
                'dimensions',
                'is_featured',
                'is_active',
                'download_limit',
                'download_expiry_days'
            ];

            // Sample data with proper escaping
            $sampleData = [
                [
                    'Sample Product 1',
                    'This is a detailed description of the product. It can contain multiple sentences and detailed information about the product features and benefits.',
                    'Short description for display on product cards',
                    'physical',
                    '1',
                    '29.99',
                    '24.99',
                    'SAMPLE-001',
                    '100',
                    '0.5',
                    '10x5x2',
                    '0',
                    '1',
                    '',
                    ''
                ],
                [
                    'Digital Product Sample',
                    'This is a digital product description. Digital products are downloadable items like ebooks, software, or digital courses.',
                    'Digital product short description',
                    'digital',
                    '2',
                    '19.99',
                    '15.99',
                    'DIGITAL-001',
                    '0',
                    '',
                    '',
                    '1',
                    '1',
                    '5',
                    '30'
                ],
                [
                    'Health Supplement',
                    'Premium health supplement with natural ingredients. Supports overall wellness and vitality.',
                    'Natural health supplement for daily wellness',
                    'physical',
                    '1',
                    '49.99',
                    '39.99',
                    'HEALTH-001',
                    '50',
                    '0.2',
                    '5x5x10',
                    '1',
                    '1',
                    '',
                    ''
                ],
                [
                    'Wellness Course',
                    'Complete wellness course covering nutrition, exercise, and mental health strategies.',
                    'Comprehensive wellness training course',
                    'digital',
                    '3',
                    '99.99',
                    '79.99',
                    'COURSE-001',
                    '0',
                    '',
                    '',
                    '1',
                    '1',
                    '10',
                    '90'
                ],
                [
                    'Fitness Equipment',
                    'Professional grade fitness equipment for home workouts.',
                    'High-quality home fitness equipment',
                    'physical',
                    '4',
                    '199.99',
                    '179.99',
                    'FITNESS-001',
                    '25',
                    '5.0',
                    '50x30x20',
                    '1',
                    '1',
                    '',
                    ''
                ]
            ];

            // Create CSV content with proper formatting
            $csvContent = '';

            // Add headers
            $csvContent .= implode(',', array_map(function($header) {
                return '"' . str_replace('"', '""', $header) . '"';
            }, $headers)) . "\r\n";

            // Add sample data
            foreach ($sampleData as $row) {
                $csvContent .= implode(',', array_map(function($field) {
                    return '"' . str_replace('"', '""', $field) . '"';
                }, $row)) . "\r\n";
            }

            // Return response with proper headers
            return response($csvContent, 200, [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="product_import_template.csv"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error generating import template: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error generating template: ' . $e->getMessage()
            ], 500);
        }
    }

    public function downloadImportInstructions()
    {
        $user = Auth::user();

        // Allow providers to download import instructions without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            abort(403, 'Unauthorized');
        }

        try {
            $instructionsPath = public_path('templates/product_import_instructions.txt');

            if (file_exists($instructionsPath)) {
                return response()->download($instructionsPath, 'product_import_instructions.txt', [
                    'Content-Type' => 'text/plain; charset=UTF-8',
                ]);
            }

            // Fallback: Generate instructions content
            $instructions = "PRODUCT IMPORT TEMPLATE INSTRUCTIONS\n";
            $instructions .= "====================================\n\n";
            $instructions .= "This template allows you to bulk import products into the system.\n\n";
            $instructions .= "REQUIRED FIELDS:\n";
            $instructions .= "- name: Product name (required)\n";
            $instructions .= "- description: Detailed product description (required)\n";
            $instructions .= "- type: Must be either 'physical' or 'digital' (required)\n";
            $instructions .= "- category_id: Valid category ID number (required)\n";
            $instructions .= "- price: Product price in decimal format (required)\n";
            $instructions .= "- sku: Unique product identifier (required)\n\n";
            $instructions .= "For complete instructions, please contact support.\n";

            return response($instructions, 200, [
                'Content-Type' => 'text/plain; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="product_import_instructions.txt"',
            ]);

        } catch (\Exception $e) {
            \Log::error('Error generating import instructions: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error generating instructions: ' . $e->getMessage()
            ], 500);
        }
    }

    public function validateImport(Request $request)
    {
        $user = Auth::user();

        // Allow providers to validate import without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240'
        ]);

        try {
            $file = $request->file('file');
            $path = $file->getRealPath();

            // Read CSV file
            $data = array_map('str_getcsv', file($path));
            $headers = array_shift($data);

            $errors = [];
            $validRows = 0;
            $totalRows = count($data);

            $categories = ProductCategory::pluck('id')->toArray();
            $existingSkus = Product::pluck('sku')->toArray();

            foreach ($data as $index => $row) {
                $rowNumber = $index + 2; // +2 because we removed headers and arrays are 0-indexed

                if (count($row) < count($headers)) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Missing required columns'
                    ];
                    continue;
                }

                $rowData = array_combine($headers, $row);

                // Validate required fields
                if (empty($rowData['name'])) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Product name is required'
                    ];
                    continue;
                }

                if (empty($rowData['sku'])) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'SKU is required'
                    ];
                    continue;
                }

                if (in_array($rowData['sku'], $existingSkus)) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'SKU already exists: ' . $rowData['sku']
                    ];
                    continue;
                }

                if (!in_array($rowData['type'], ['physical', 'digital'])) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Type must be either "physical" or "digital"'
                    ];
                    continue;
                }

                if (!in_array($rowData['category_id'], $categories)) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Invalid category ID: ' . $rowData['category_id']
                    ];
                    continue;
                }

                if (!is_numeric($rowData['price']) || $rowData['price'] < 0) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Price must be a valid positive number'
                    ];
                    continue;
                }

                $validRows++;
            }

            return response()->json([
                'total_rows' => $totalRows,
                'valid_rows' => $validRows,
                'invalid_rows' => $totalRows - $validRows,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error validating file: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkImport(Request $request)
    {
        $user = Auth::user();

        // Allow providers to bulk import without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240'
        ]);

        try {
            $file = $request->file('file');
            $path = $file->getRealPath();

            // Read CSV file
            $data = array_map('str_getcsv', file($path));
            $headers = array_shift($data);

            $importedCount = 0;
            $errors = [];
            $userId = Auth::id();

            foreach ($data as $index => $row) {
                $rowNumber = $index + 2;

                if (count($row) < count($headers)) {
                    continue;
                }

                $rowData = array_combine($headers, $row);

                // Skip invalid rows
                if (empty($rowData['name']) || empty($rowData['sku'])) {
                    continue;
                }

                try {
                    Product::create([
                        'user_id' => $userId,
                        'name' => $rowData['name'],
                        'slug' => Str::slug($rowData['name']),
                        'description' => $rowData['description'] ?? '',
                        'short_description' => $rowData['short_description'] ?? '',
                        'type' => $rowData['type'],
                        'category_id' => $rowData['category_id'],
                        'price' => (float) $rowData['price'],
                        'sale_price' => !empty($rowData['sale_price']) ? (float) $rowData['sale_price'] : null,
                        'sku' => $rowData['sku'],
                        'stock_quantity' => $rowData['type'] === 'physical' ? (int) ($rowData['stock_quantity'] ?? 0) : 0,
                        'manage_stock' => $rowData['type'] === 'physical',
                        'in_stock' => $rowData['type'] === 'digital' ? true : ((int) ($rowData['stock_quantity'] ?? 0) > 0),
                        'weight' => !empty($rowData['weight']) ? (float) $rowData['weight'] : null,
                        'dimensions' => $rowData['dimensions'] ?? null,
                        'is_featured' => (bool) ($rowData['is_featured'] ?? false),
                        'is_active' => (bool) ($rowData['is_active'] ?? true),
                        'download_limit' => !empty($rowData['download_limit']) ? (int) $rowData['download_limit'] : null,
                        'download_expiry_days' => !empty($rowData['download_expiry_days']) ? (int) $rowData['download_expiry_days'] : null,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Failed to create product: ' . $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$importedCount} products",
                'imported_count' => $importedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error importing products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pending products for approval
     */
    public function getPendingProducts(Request $request)
    {
        $user = Auth::user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = Product::with(['category', 'images', 'user'])
            ->where('approval_status', 'pending')
            ->orderBy('created_at', 'desc');

        $products = $query->paginate($request->get('per_page', 15));

        return response()->json($products);
    }

    /**
     * Approve a product
     */
    public function approveProduct(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $product = Product::findOrFail($id);

            $product->update([
                'approval_status' => 'approved',
                'approved_by' => $user->id,
                'approved_at' => now(),
                'rejection_reason' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product approved successfully',
                'product' => $product->load(['category', 'images', 'user', 'approvedBy']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve product: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reject a product
     */
    public function rejectProduct(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        try {
            $product = Product::findOrFail($id);

            $product->update([
                'approval_status' => 'rejected',
                'approved_by' => $user->id,
                'approved_at' => now(),
                'rejection_reason' => $request->rejection_reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product rejected successfully',
                'product' => $product->load(['category', 'images', 'user', 'approvedBy']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject product: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Convert string boolean values to actual booleans
     */
    private function convertToBoolean($value)
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
        }

        if (is_numeric($value)) {
            return (int)$value === 1;
        }

        return (bool) $value;
    }
}

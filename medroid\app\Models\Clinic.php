<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Clinic extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'email',
        'phone',
        'website',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'operating_hours',
        'services_offered',
        'license_number',
        'tax_id',
        'is_active',
        'accepts_new_patients',
        'telemedicine_enabled',
        'insurance_accepted',
        'logo',
        'primary_color',
        'secondary_color',
    ];

    protected $casts = [
        'operating_hours' => 'array',
        'services_offered' => 'array',
        'insurance_accepted' => 'array',
        'is_active' => 'boolean',
        'accepts_new_patients' => 'boolean',
        'telemedicine_enabled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the providers for the clinic.
     */
    public function providers()
    {
        return $this->hasMany(Provider::class);
    }

    /**
     * Get the patients for the clinic.
     */
    public function patients()
    {
        return $this->hasMany(Patient::class);
    }

    /**
     * Get all users associated with the clinic through providers and patients.
     */
    public function users()
    {
        $providerUsers = $this->providers()->with('user')->get()->pluck('user')->filter();
        $patientUsers = $this->patients()->with('user')->get()->pluck('user')->filter();

        return $providerUsers->merge($patientUsers)->unique('id');
    }

    /**
     * Get provider users for the clinic.
     */
    public function providerUsers()
    {
        return $this->providers()->with('user')->get()->pluck('user')->filter();
    }

    /**
     * Get patient users for the clinic.
     */
    public function patientUsers()
    {
        return $this->patients()->with('user')->get()->pluck('user')->filter();
    }

    /**
     * Get appointments for this clinic through providers.
     */
    public function appointments()
    {
        return $this->hasManyThrough(Appointment::class, Provider::class);
    }

    /**
     * Scope to get active clinics.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get clinics accepting new patients.
     */
    public function scopeAcceptingPatients($query)
    {
        return $query->where('accepts_new_patients', true);
    }

    /**
     * Get the full address as a string.
     */
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country
        ]);
        
        return implode(', ', $parts);
    }

    /**
     * Get clinic statistics.
     */
    public function getStatsAttribute()
    {
        return [
            'total_providers' => $this->providers()->count(),
            'active_providers' => $this->providers()->whereHas('user', function($q) {
                $q->where('is_active', true);
            })->count(),
            'total_patients' => $this->patients()->count(),
            'total_appointments' => $this->appointments()->count(),
            'pending_appointments' => $this->appointments()->where('status', 'pending')->count(),
            'completed_appointments' => $this->appointments()->where('status', 'completed')->count(),
        ];
    }
}

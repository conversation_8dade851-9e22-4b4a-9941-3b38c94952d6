<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Jobs\GenerateBotPostJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ScheduleHighFrequencyBotPosts extends Command
{
    protected $signature = 'bot:schedule-high-frequency {--force : Force scheduling even if posts already exist} {--dry-run : Show what would be scheduled without actually scheduling}';
    protected $description = 'Schedule 4 posts per day for all bot personas (76 posts per day total)';

    public function handle()
    {
        $this->info('🚀 Starting high-frequency bot post scheduling (4 posts per persona per day)...');
        
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');
        $today = now()->toDateString();
        
        // Get all active personas with bot users
        $personas = BotPersona::active()->whereHas('botUser')->get();
        
        if ($personas->isEmpty()) {
            $this->warn('No active bot personas found.');
            return 0;
        }

        $this->info("Found {$personas->count()} active personas");
        $this->info("Target: {$personas->count()} × 4 = " . ($personas->count() * 4) . " posts per day");
        
        $totalScheduled = 0;
        $totalSkipped = 0;
        
        foreach ($personas as $persona) {
            $result = $this->schedulePostsForPersona($persona, $force, $dryRun);
            $totalScheduled += $result['scheduled'];
            $totalSkipped += $result['skipped'];
        }
        
        if ($dryRun) {
            $this->info("🔍 DRY RUN COMPLETE:");
        } else {
            $this->info("🎯 SCHEDULING COMPLETE:");
        }
        
        $this->info("✅ Scheduled: {$totalScheduled} posts");
        $this->info("⏭️  Skipped: {$totalSkipped} posts");
        $this->info("📊 Total daily target: " . ($personas->count() * 4) . " posts");
        
        return 0;
    }
    
    /**
     * Schedule 4 posts for a specific persona throughout the day
     */
    private function schedulePostsForPersona(BotPersona $persona, bool $force, bool $dryRun): array
    {
        $scheduled = 0;
        $skipped = 0;
        
        // Check existing posts for today
        $existingPostsToday = AutomatedPost::where('bot_persona_id', $persona->id)
            ->whereDate('created_at', today())
            ->count();
        
        $postsNeeded = 4 - $existingPostsToday;
        
        if ($postsNeeded <= 0 && !$force) {
            $this->line("⏭️  {$persona->full_name}: Already has {$existingPostsToday}/4 posts today");
            return ['scheduled' => 0, 'skipped' => 4];
        }
        
        if ($force) {
            $postsNeeded = 4;
        }
        
        // Calculate optimal posting times (spread throughout the day)
        $postingTimes = $this->calculateOptimalPostingTimes($persona, $postsNeeded);
        
        $this->line("📝 {$persona->full_name}: Scheduling {$postsNeeded} posts");
        
        foreach ($postingTimes as $index => $scheduledTime) {
            if ($dryRun) {
                $this->line("   🔍 Would schedule post " . ($index + 1) . " at {$scheduledTime->format('H:i')}");
                $scheduled++;
            } else {
                // Dispatch the job
                GenerateBotPostJob::dispatch($persona)->delay($scheduledTime);
                
                $this->line("   ✅ Scheduled post " . ($index + 1) . " at {$scheduledTime->format('H:i')}");
                $scheduled++;
                
                Log::info('High-frequency bot post scheduled', [
                    'persona_id' => $persona->id,
                    'persona_name' => $persona->full_name,
                    'post_number' => $index + 1,
                    'scheduled_at' => $scheduledTime,
                ]);
            }
        }
        
        return ['scheduled' => $scheduled, 'skipped' => $skipped];
    }
    
    /**
     * Calculate optimal posting times spread throughout the day
     */
    private function calculateOptimalPostingTimes(BotPersona $persona, int $postsNeeded): array
    {
        $times = [];
        $contentFocus = strtolower($persona->content_focus);
        
        // Define optimal time slots for health content (6 AM to 10 PM)
        $timeSlots = [
            '06:00' => 'early_morning',   // Early birds, morning routines
            '09:00' => 'morning',         // Work break, morning motivation
            '12:30' => 'lunch',           // Lunch break, midday tips
            '15:30' => 'afternoon',       // Afternoon energy boost
            '18:00' => 'evening',         // Post-work, dinner prep
            '20:30' => 'night',           // Evening wind-down, reflection
        ];
        
        // Select the best time slots based on content focus
        $preferredSlots = $this->getPreferredSlotsForContent($contentFocus, $timeSlots);
        
        // If we need more posts than preferred slots, use all slots
        if ($postsNeeded > count($preferredSlots)) {
            $selectedSlots = array_keys($timeSlots);
        } else {
            $selectedSlots = array_slice($preferredSlots, 0, $postsNeeded);
        }
        
        // Create Carbon instances for today with random variations
        foreach ($selectedSlots as $timeSlot) {
            $baseTime = Carbon::today()->setTimeFromTimeString($timeSlot);
            
            // Add random variation (±30 minutes) to avoid all bots posting at same time
            $randomMinutes = rand(-30, 30);
            $scheduledTime = $baseTime->addMinutes($randomMinutes);
            
            // If time is in the past, schedule for now + random delay
            if ($scheduledTime->isPast()) {
                $scheduledTime = now()->addMinutes(rand(1, 60));
            }
            
            $times[] = $scheduledTime;
        }
        
        // Sort times chronologically
        usort($times, function($a, $b) {
            return $a->timestamp <=> $b->timestamp;
        });
        
        return $times;
    }
    
    /**
     * Get preferred time slots based on content focus
     */
    private function getPreferredSlotsForContent(string $contentFocus, array $timeSlots): array
    {
        $preferences = [
            'yoga' => ['06:00', '18:00', '20:30', '09:00'],           // Morning/evening practice
            'fitness' => ['06:00', '09:00', '18:00', '15:30'],       // Pre/post workout times
            'nutrition' => ['09:00', '12:30', '18:00', '20:30'],     // Meal times
            'mental health' => ['09:00', '15:30', '20:30', '12:30'], // Reflection times
            'meditation' => ['06:00', '20:30', '12:30', '15:30'],    // Quiet times
            'wellness' => ['09:00', '12:30', '18:00', '20:30'],      // General wellness
            'cycling' => ['06:00', '18:00', '09:00', '15:30'],       // Active times
            'cooking' => ['09:00', '12:30', '18:00', '20:30'],       // Meal prep times
            'gut health' => ['09:00', '12:30', '18:00', '06:00'],    // Digestive focus
            'home wellness' => ['09:00', '15:30', '20:30', '12:30'], // Home time
            'health tech' => ['09:00', '15:30', '18:00', '20:30'],   // Tech-savvy times
            'recovery' => ['20:30', '09:00', '15:30', '18:00'],      // Recovery focus
            'brain health' => ['09:00', '15:30', '20:30', '12:30'],  // Mental clarity
            'cold therapy' => ['06:00', '09:00', '18:00', '15:30'],  // Energy times
            'creative' => ['09:00', '15:30', '20:30', '18:00'],      // Creative times
            'sleep' => ['20:30', '09:00', '18:00', '15:30'],         // Sleep focus
        ];
        
        return $preferences[$contentFocus] ?? ['09:00', '12:30', '18:00', '20:30'];
    }
}

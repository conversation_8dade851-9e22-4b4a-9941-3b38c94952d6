<?php

namespace App\Http\Controllers;

use App\Models\ChatConversation;
use App\Models\Patient;
use App\Models\Provider;
use App\Services\MistralNemoService;
use App\Services\OpenAiGpt4Service;
use App\Services\ChatServiceManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AIChatController extends Controller
{
    protected $mistralService;
    protected $openAiService;
    protected $chatService;
    public function __construct(MistralNemoService $mistralService, OpenAiGpt4Service $openAiService, ChatServiceManager $chatService)
    {
        $this->mistralService = $mistralService;
        $this->openAiService = $openAiService;
        $this->chatService = $chatService;
    }

    /**
     * Check if OpenAI should be used based on environment configuration
     */
    private function useOpenAi()
    {
        return env('AI_SERVICE_PROVIDER', 'groq') === 'openai';
    }

    /**
     * Start a new conversation with the AI assistant
     */
    public function startConversation(Request $request)
    {
        $user = $request->user();

        // Currently, only patients can use the AI chat assistant
        if ($user->role !== 'patient') {
            return response()->json([
                'message' => 'Only patients can use the AI chat assistant'
            ], 403);
        }

        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        // Create a new conversation
        $conversation = ChatConversation::create([
            'patient_id' => $patient->id,
            'messages' => [],
            'health_concerns' => [],
            'recommendations' => [],
            'escalated' => false,
        ]);

        return response()->json([
            'conversation_id' => $conversation->id,
            'message' => 'Conversation started successfully'
        ], 200, [
            'Content-Type' => 'application/json',
            'Connection' => 'close'
        ]);
    }

    /**
     * Send a message to the AI assistant and get a response
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|string',
            'message' => 'required|string',
            'include_patient_context' => 'boolean',
            'generate_title' => 'boolean',
            'role' => 'sometimes|string|in:user,assistant',
            'chat_mode' => 'sometimes|string|in:consultation,maya',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        // Find the conversation
        try {
            $conversation = ChatConversation::where('patient_id', $patient->id)
                ->where('id', $request->conversation_id)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'message' => 'Conversation not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error finding conversation: ' . $e->getMessage()
            ], 404);
        }

        $chatMode = $request->chat_mode ?? 'consultation'; // Get mode early for user message

        // Update conversation mode if it's different from current mode
        if ($conversation->mode !== $chatMode) {
            $conversation->mode = $chatMode;
            $conversation->save();
        }

        // Add message to conversation (role can be 'user' or 'assistant')
        $messages = $conversation->messages;
        $messages[] = [
            'role' => $request->input('role', 'user'), // Default to 'user' if not specified
            'content' => $request->message,
            'timestamp' => now(),
            'mode' => $chatMode, // Store the mode used for this message
        ];

        $conversation->messages = $messages;
        $conversation->save();

        // If this is an assistant message (system message), just save it and return
        if ($request->input('role') === 'assistant') {
            return response()->json([
                'message' => 'Message saved successfully',
                'conversation_id' => $conversation->id
            ]);
        }

        // Check if user is confirming appointment booking BEFORE generating AI response
        $userMessage = $request->message;

        // Pass conversation context to make intent detection more intelligent
        $conversationContext = $conversation->messages ?? [];

        $hasAppointmentBookingIntent = $this->useOpenAi() ?
            $this->openAiService->detectAppointmentBookingIntent($userMessage, $conversationContext) :
            $this->chatService->detectAppointmentBookingIntent($userMessage, $conversationContext);

        if ($hasAppointmentBookingIntent) {
            \Illuminate\Support\Facades\Log::info('User confirmed appointment booking, redirecting to appointment slots', [
                'conversation_id' => $conversation->id,
                'user_message' => $userMessage
            ]);

            // Instead of generating AI response, directly show appointment slots
            return $this->requestAppointment($request);
        }

        // Check if this conversation has recent appointment booking activity
        $hasRecentAppointment = $this->hasRecentAppointmentBooking($conversation);

        // Get chat mode from request (default to consultation)
        $chatMode = $request->chat_mode ?? 'consultation';

        // Generate AI response based on selected mode and service
        if ($chatMode === 'maya') {
            // Use Maya wellness service for wellness mode
            $wellnessService = new \App\Services\HealthWellnessService();

            // Pass search preference to Maya service
            $searchEnabled = $request->search_enabled ?? true;
            $mayaResponse = $wellnessService->chat(
                $request->message,
                $conversation->messages ?? [],
                $patient->id,
                $conversation->id,
                $searchEnabled
            );

            // Format response to match expected structure
            if ($mayaResponse['success']) {
                $response = [
                    'message' => $mayaResponse['response'],
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                    'mode' => 'maya',
                    'service_name' => $mayaResponse['provider'] ?? 'Maya'
                ];
            } else {
                $response = [
                    'message' => 'I apologize, but Maya is currently unavailable. Please try again later.',
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                    'mode' => 'maya',
                    'service_error' => true
                ];
            }
        } else {
            // Use medical consultation service for AI Doctor mode
            if ($this->useOpenAi()) {
                $response = $this->openAiService->generateMedicalConsultation($conversation, $hasRecentAppointment);
            } else {
                $includePatientContext = $request->include_patient_context ?? true;
                $response = $this->chatService->generateMedicalConsultation($conversation, $includePatientContext, '', $hasRecentAppointment);
            }
            $response['mode'] = 'consultation';
        }

        // Check for appointment preferences in the user message and save them
        $this->checkAndSaveAppointmentPreferences($request->message, $patient);

        // Add AI response to conversation
        $messages = $conversation->messages;
        $messages[] = [
            'role' => 'assistant',
            'content' => $response['message'],
            'timestamp' => now(),
            'mode' => $chatMode, // Store the mode used for this response
        ];

        // Update conversation with AI analysis
        $conversation->messages = $messages;

        // Merge new health concerns with existing ones (avoid duplicates)
        $healthConcerns = collect($conversation->health_concerns ?? []);
        $newConcerns = collect($response['health_concerns'] ?? []);
        $mergedConcerns = $healthConcerns->merge($newConcerns)->filter()->unique()->values()->toArray();
        $conversation->health_concerns = $mergedConcerns;

        // Add new recommendations
        if (isset($response['recommendations']) && is_array($response['recommendations']) && !empty($response['recommendations'])) {
            $conversation->recommendations = array_merge(
                is_array($conversation->recommendations) ? $conversation->recommendations : [],
                $response['recommendations']
            );
        }

        // Set escalation status
        if (isset($response['escalate']) && $response['escalate']) {
            $conversation->escalated = true;

            // Add escalation reason if provided
            if (isset($response['escalation_reason'])) {
                $conversation->escalation_reason = $response['escalation_reason'];
            }
        }

        // Store referral note if provided by OpenAI service
        if (isset($response['referral_note']) && !empty($response['referral_note'])) {
            $conversation->referral_note = $response['referral_note'];
        }

        // Generate a title for the conversation if requested and no title exists yet
        $generateTitle = $request->generate_title ?? false;
        if ($generateTitle && empty($conversation->title)) {
            // Check if we have enough messages to generate a meaningful summary
            if (count($conversation->messages) >= 3) {
                // Generate a summary title using AI
                $title = $this->generateConversationSummaryTitle($conversation);
                $conversation->title = $title;
            } else {
                // Not enough messages yet, use the first user message as a fallback
                $firstUserMessage = null;
                foreach ($conversation->messages as $message) {
                    if ($message['role'] === 'user') {
                        $firstUserMessage = $message['content'];
                        break;
                    }
                }

                if ($firstUserMessage) {
                    // Create a simple title from the first user message
                    $title = strlen($firstUserMessage) > 50
                        ? substr($firstUserMessage, 0, 47) . '...'
                        : $firstUserMessage;

                    $conversation->title = $title;
                }
            }
        }

        $conversation->save();

        // Return detailed response
        return response()->json([
            'message' => $response['message'],
            'health_concerns' => $response['health_concerns'] ?? [],
            'recommendations' => $response['recommendations'] ?? [],
            'escalated' => $conversation->escalated,
            'escalation_reason' => $response['escalation_reason'] ?? null,
            'has_referral' => isset($response['referral_note']) && !empty($response['referral_note']),
            'title' => $conversation->title,
            'mode' => $chatMode, // Include the mode in the response
            'service_name' => $response['service_name'] ?? ($chatMode === 'maya' ? 'Maya' : 'AI Doctor'),
        ]);
    }

    /**
     * Get conversation history for the user
     */
    public function getHistory(Request $request)
    {
        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        $conversations = ChatConversation::where('patient_id', $patient->id)
            ->orderBy('updated_at', 'desc')
            ->paginate($request->per_page ?? 10);

        return response()->json($conversations);
    }

    /**
     * Get specific conversation by ID
     */
    public function getConversation(Request $request, $conversationId)
    {
        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        try {
            // Get conversation by ID
            $conversation = ChatConversation::where('patient_id', $patient->id)
                ->where('id', $conversationId)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'message' => 'Conversation not found'
                ], 404);
            }

            return response()->json($conversation);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving conversation: ' . $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update a conversation (title)
     */
    public function updateConversation(Request $request, $conversationId)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        try {
            // Find the conversation
            $conversation = ChatConversation::where('patient_id', $patient->_id)
                ->where('_id', $conversationId)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'message' => 'Conversation not found'
                ], 404);
            }

            // Update the title
            $conversation->title = $request->title;
            $conversation->save();

            return response()->json([
                'success' => true,
                'message' => 'Conversation updated successfully',
                'conversation' => $conversation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating conversation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a conversation
     */
    public function deleteConversation(Request $request, $conversationId)
    {
        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        try {
            // Find the conversation
            $conversation = ChatConversation::where('patient_id', $patient->id)
                ->where('id', $conversationId)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'message' => 'Conversation not found'
                ], 404);
            }

            // Delete the conversation
            $conversation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Conversation deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting conversation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get health recommendations from latest conversation
     */
    public function getRecommendations(Request $request)
    {
        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        // Get most recent conversation
        $conversation = ChatConversation::where('patient_id', $patient->id)
            ->orderBy('updated_at', 'desc')
            ->first();

        if (!$conversation) {
            return response()->json([
                'message' => 'No conversations found'
            ], 404);
        }

        return response()->json([
            'recommendations' => $conversation->recommendations ?? [],
            'health_concerns' => $conversation->health_concerns ?? [],
            'escalated' => $conversation->escalated,
            'escalation_reason' => $conversation->escalation_reason ?? null,
            'referral_note' => $conversation->referral_note ?? null,
        ]);
    }

    /**
     * Analyze symptoms directly without adding to chat history
     */
    public function analyzeSymptoms(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'symptoms' => 'required|string|min:5',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patientContext = '';

        // Get patient context if user is a patient
        if ($user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();
            if ($patient) {
                // The service will format the patient context
                $patientContext = true;
            }
        }

        // Use the appropriate service based on configuration
        if ($this->useOpenAi()) {
            // Create a temporary conversation object for the analysis
            $tempConversation = new ChatConversation([
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => "I need help with these symptoms: {$request->symptoms}",
                        'timestamp' => now(),
                    ]
                ]
            ]);

            $analysis = $this->openAiService->generateMedicalConsultation($tempConversation);
        } else {
            // Create a temporary conversation object for the analysis
            $tempConversation = new ChatConversation([
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => "I need help with these symptoms: {$request->symptoms}",
                        'timestamp' => now(),
                    ]
                ]
            ]);

            $analysis = $this->chatService->generateMedicalConsultation($tempConversation, $patientContext);
        }

        // If urgent or emergency, suggest providers
        if (isset($analysis['escalate']) && $analysis['escalate']) {
            // Get emergency service information if applicable
            if ($this->useOpenAi()) {
                $emergencyInfo = $this->openAiService->getEmergencyServices($request->location ?? null);
                $analysis['emergency_information'] = $emergencyInfo;
            } else {
                $emergencyInfo = $this->chatService->getEmergencyServices($request->location ?? null);
                $analysis['emergency_information'] = $emergencyInfo;
            }

            // Find general practice providers for all health concerns
            $providers = Provider::where('specialization', 'General Practice')
                ->with('user')
                ->take(3)
                ->get();

            if (!$providers->isEmpty()) {
                $analysis['suggested_providers'] = $providers;
            }
        }

        return response()->json($analysis);
    }

    /**
     * Get medication information
     */
    public function getMedicationInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'medication_name' => 'required|string|min:2',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Use the appropriate service based on configuration
        if ($this->useOpenAi()) {
            // Create a temporary conversation object for the medication info
            $tempConversation = new ChatConversation([
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => "Can you tell me about the medication: {$request->medication_name}?",
                        'timestamp' => now(),
                    ]
                ]
            ]);

            $response = $this->openAiService->generateMedicalConsultation($tempConversation);

            // Extract medication info
            $medicationInfo = [
                'name' => $request->medication_name,
                'description' => $response['message'],
                'warnings' => [],
                'side_effects' => [],
                'interactions' => [],
            ];

            // Try to extract some structured information
            foreach ($response['recommendations'] as $rec) {
                if (stripos($rec['content'], 'warning') !== false ||
                    stripos($rec['content'], 'caution') !== false) {
                    $medicationInfo['warnings'][] = $rec['content'];
                } elseif (stripos($rec['content'], 'side effect') !== false) {
                    $medicationInfo['side_effects'][] = $rec['content'];
                } elseif (stripos($rec['content'], 'interact') !== false) {
                    $medicationInfo['interactions'][] = $rec['content'];
                }
            }

            return response()->json($medicationInfo);
        } else {
            // Create a temporary conversation object for the medication info
            $tempConversation = new ChatConversation([
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => "Can you tell me about the medication: {$request->medication_name}?",
                        'timestamp' => now(),
                    ]
                ]
            ]);

            $response = $this->chatService->generateMedicalConsultation($tempConversation);

            // Extract medication info
            $medicationInfo = [
                'name' => $request->medication_name,
                'description' => $response['message'],
                'warnings' => [],
                'side_effects' => [],
                'interactions' => [],
            ];

            // Try to extract some structured information
            foreach ($response['recommendations'] as $rec) {
                if (stripos($rec['content'], 'warning') !== false ||
                    stripos($rec['content'], 'caution') !== false) {
                    $medicationInfo['warnings'][] = $rec['content'];
                } elseif (stripos($rec['content'], 'side effect') !== false) {
                    $medicationInfo['side_effects'][] = $rec['content'];
                } elseif (stripos($rec['content'], 'interact') !== false) {
                    $medicationInfo['interactions'][] = $rec['content'];
                }
            }

            return response()->json($medicationInfo);
        }
    }

    /**
     * Generate a summary title for a conversation based on its content
     */
    private function generateConversationSummaryTitle(ChatConversation $conversation)
    {
        try {
            // Extract the conversation content
            $conversationText = '';
            foreach ($conversation->messages as $message) {
                $role = $message['role'];
                $content = $message['content'];

                // Only include the first 100 characters of each message to keep it concise
                if (strlen($content) > 100) {
                    $content = substr($content, 0, 97) . '...';
                }

                $conversationText .= "{$role}: {$content}\n";
            }

            // Use the AI service to generate a summary title
            if ($this->useOpenAi()) {
                // Create a prompt for generating a title
                $prompt = "Generate a concise, descriptive title (max 50 characters) that summarizes the main topic of this conversation:\n\n{$conversationText}";

                $response = $this->openAiService->generateTitle($prompt);

                // Ensure the title is not too long
                if (strlen($response) > 50) {
                    $response = substr($response, 0, 47) . '...';
                }

                return $response;
            } else {
                // Create a prompt for generating a title
                $prompt = "Generate a concise, descriptive title (max 50 characters) that summarizes the main topic of this conversation:\n\n{$conversationText}";

                $response = $this->chatService->generateTitle($prompt);

                // Ensure the title is not too long
                if (strlen($response) > 50) {
                    $response = substr($response, 0, 47) . '...';
                }

                return $response;
            }
        } catch (\Exception $e) {
            // If there's an error, fall back to a generic title with the date
            return 'Health Conversation - ' . now()->format('M d, Y');
        }
    }

    /**
     * Check for appointment preferences in the user message and save them
     * Also detect appointment booking intent and medical specialty needs
     */
    private function checkAndSaveAppointmentPreferences($message, $patient)
    {
        try {
            // Get current preferences or initialize if not set
            $appointmentPreferences = $patient->appointment_preferences ?? [
                'preferred_location' => null,
                'preferred_gender' => null,
                'preferred_language' => null,
                'last_condition' => null
            ];

            $preferencesUpdated = false;

            // Check for location preferences
            if (preg_match('/prefer(?:red)?\s+(?:location|area|place|city)(?:\s+is|\s*:\s*|\s+in\s+|\s+near\s+|\s+around\s+)?\s+([^\.,:;\n]+)/i', $message, $matches)) {
                $location = trim($matches[1]);
                if (!empty($location) && $location != 'null' && $location != 'none' && strlen($location) > 2) {
                    $appointmentPreferences['preferred_location'] = $location;
                    $preferencesUpdated = true;
                }
            }

            // Check for gender preferences
            if (preg_match('/prefer(?:red)?\s+(?:doctor|provider|physician)?\s*(?:gender|sex)(?:\s+is|\s*:\s*)?\s+(male|female|any|no preference)/i', $message, $matches)) {
                $gender = strtolower(trim($matches[1]));
                if ($gender == 'no preference') {
                    $gender = 'any';
                }
                $appointmentPreferences['preferred_gender'] = $gender;
                $preferencesUpdated = true;
            }

            // Check for language preferences
            if (preg_match('/prefer(?:red)?\s+(?:language|speaking)(?:\s+is|\s*:\s*)?\s+([^\.,:;\n]+)/i', $message, $matches)) {
                $language = trim($matches[1]);
                if (!empty($language) && $language != 'null' && $language != 'none' && strlen($language) > 2) {
                    $appointmentPreferences['preferred_language'] = $language;
                    $preferencesUpdated = true;
                }
            }

            // We're removing the direct pattern matching for appointment booking intent
            // Instead, we'll rely on the AI to have a proper conversation and only suggest
            // appointments after a thorough discussion and with explicit user consent

            // We'll use the AI to detect appointment intent
            // This will only happen after a proper conversation and explicit user consent
            $hasAppointmentIntent = $this->useOpenAi() ?
                $this->openAiService->detectAppointmentBookingIntent($message) :
                $this->chatService->detectAppointmentBookingIntent($message);

            if ($hasAppointmentIntent) {
                // Save that appointment intent was detected, but don't try to detect specialty
                $appointmentPreferences['last_condition'] = "medical consultation"; // Generic placeholder
                $preferencesUpdated = true;

                \Illuminate\Support\Facades\Log::info('AI detected appointment booking intent', [
                    'patient_id' => $patient->id,
                    'message' => 'General medical consultation requested'
                ]);
            }

            // Save preferences if updated
            if ($preferencesUpdated) {
                $patient->appointment_preferences = $appointmentPreferences;
                $patient->save();

                \Illuminate\Support\Facades\Log::info('Updated patient appointment preferences', [
                    'patient_id' => $patient->id,
                    'preferences' => $appointmentPreferences
                ]);
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error checking appointment preferences', [
                'message' => $e->getMessage(),
                'patient_id' => $patient->id ?? 'unknown'
            ]);
        }
    }

    /**
     * Generate a title for an existing conversation
     */
    public function generateTitle(Request $request, $conversationId)
    {
        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        try {
            // Find the conversation
            $conversation = ChatConversation::where('patient_id', $patient->id)
                ->where('id', $conversationId)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'message' => 'Conversation not found'
                ], 404);
            }

            // Generate a summary title
            $title = $this->generateConversationSummaryTitle($conversation);

            // Update the conversation title
            $conversation->title = $title;
            $conversation->save();

            return response()->json([
                'success' => true,
                'message' => 'Title generated successfully',
                'title' => $title
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating title: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Request a virtual appointment with a healthcare provider
     *
     * This method has been updated to ensure appointments are only shown after:
     * 1. A proper medical conversation has occurred
     * 2. The AI has provided a summary of the user's condition
     * 3. The user has explicitly consented to booking an appointment
     */
    public function requestAppointment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|string',
            'preferred_timing' => 'nullable|string',
            'provider_specialization' => 'nullable|string',
            'service_category' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $patient = Patient::where('user_id', $user->id)->first();

        if (!$patient) {
            return response()->json([
                'message' => 'Patient profile not found'
            ], 404);
        }

        // Find the conversation
        try {
            $conversation = ChatConversation::where('patient_id', $patient->id)
                ->where('id', $request->conversation_id)
                ->first();

            if (!$conversation) {
                return response()->json([
                    'message' => 'Conversation not found'
                ], 404);
            }

            // Check if there's been a proper conversation with at least 4 messages
            // This helps ensure there's been some back-and-forth before showing appointments
            if (count($conversation->messages) < 4) {
                \Illuminate\Support\Facades\Log::warning('Appointment requested too early in conversation', [
                    'conversation_id' => $conversation->id,
                    'message_count' => count($conversation->messages)
                ]);

                // Instead of showing appointments, we'll add a message asking for more information
                $messages = $conversation->messages;
                $messages[] = [
                    'role' => 'assistant',
                    'content' => "I'd like to understand your symptoms better before we discuss appointment options. Could you tell me more about what you're experiencing?",
                    'timestamp' => now(),
                ];

                $conversation->messages = $messages;
                $conversation->save();

                return response()->json([
                    'message' => 'More conversation needed before booking',
                    'conversation_id' => $conversation->id,
                    'needs_more_context' => true
                ]);
            }

            // Check the last AI message to see if it contains a summary and appointment suggestion
            $lastAiMessage = null;
            $messageCount = count($conversation->messages);
            for ($i = $messageCount - 1; $i >= 0; $i--) {
                if ($conversation->messages[$i]['role'] === 'assistant') {
                    $lastAiMessage = $conversation->messages[$i]['content'];
                    break;
                }
            }

            // Check if the last user message indicates explicit consent to book an appointment
            $lastUserMessage = null;
            for ($i = $messageCount - 1; $i >= 0; $i--) {
                if ($conversation->messages[$i]['role'] === 'user') {
                    $lastUserMessage = $conversation->messages[$i]['content'];
                    break;
                }
            }

            // Since we're here, the user has already confirmed appointment booking
            // (this is checked in sendMessage method before calling this method)
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error finding conversation: ' . $e->getMessage()
            ], 404);
        }

        // Extract symptoms from conversation
        $symptoms = '';
        foreach ($conversation->messages as $message) {
            if ($message['role'] === 'user') {
                $symptoms .= $message['content'] . ' ';
            }
        }

        // Generate appointment request
        if ($this->useOpenAi()) {
            $patientInfo = [
                'name' => $user->name,
                'health_concerns' => $conversation->health_concerns,
            ];

            $appointmentRequest = $this->openAiService->generateAppointmentRequest(
                $patientInfo,
                $symptoms,
                $request->preferred_timing
            );
        } else {
            $patientInfo = [
                'name' => $user->name,
                'health_concerns' => $conversation->health_concerns,
            ];

            $appointmentRequest = $this->chatService->generateAppointmentRequest(
                $patientInfo,
                $symptoms,
                $request->preferred_timing
            );
        }

        // Get available appointment slots
        // Show all verified providers without category filtering
        \Illuminate\Support\Facades\Log::info('Requesting appointment slots from all verified providers', [
            'patient_id' => $patient->id
        ]);

        // Get the next 7 days of available slots from all providers
        $daysAhead = 7;
        $availableSlots = $this->getNextAvailableSlots(null, $daysAhead);

        // Generate a referral note for the healthcare provider
        $referralNote = $this->generateReferralNote($conversation);

        // Add a simple confirmation message to the conversation
        $aiMessage = "Perfect! I found available appointment slots for you. You can view and select your preferred time slot below.";

        $messages = $conversation->messages;
        $messages[] = [
            'role' => 'assistant',
            'content' => $aiMessage,
            'timestamp' => now(),
        ];

        $conversation->messages = $messages;
        $conversation->referral_note = $referralNote; // Store the referral note
        $conversation->save();

        \Illuminate\Support\Facades\Log::info('Returning appointment slots response with ' . count($availableSlots) . ' slot groups');
        \Illuminate\Support\Facades\Log::info('Available slots structure: ' . json_encode($availableSlots));

        return response()->json([
            'message' => $aiMessage,
            'conversation_id' => $conversation->id,
            'available_slots' => $availableSlots,
            'referral_note' => $referralNote,
            'service_category' => 'all', // Since we're showing all providers without category filtering
        ]);
    }

    /**
     * Get next available appointment slots (Robust version for both local and production)
     *
     * @param string $serviceCategory
     * @param int $daysAhead
     * @return array
     */
    private function getNextAvailableSlots($serviceCategory, $daysAhead = 7)
    {
        try {
            $availableSlots = [];
            $today = now();

            \Illuminate\Support\Facades\Log::info('=== Starting getNextAvailableSlots ===');
            \Illuminate\Support\Facades\Log::info('Service category: ' . ($serviceCategory ?? 'none'));
            \Illuminate\Support\Facades\Log::info('Days ahead: ' . $daysAhead);

            // Step 1: Get all providers (more permissive query for production compatibility)
            $providers = Provider::with(['user', 'services'])
                ->where('verification_status', 'verified')
                ->get();

            \Illuminate\Support\Facades\Log::info('Total verified providers found: ' . $providers->count());

            if ($providers->isEmpty()) {
                \Illuminate\Support\Facades\Log::warning('No verified providers found in database');
                return [];
            }

            // Step 2: Check providers (allow providers without services for basic consultation)
            $availableProviders = $providers->filter(function($provider) {
                $hasActiveServices = $provider->services()->where('active', true)->exists();
                $hasAvailability = !empty($provider->getAvailabilityData());
                \Illuminate\Support\Facades\Log::info('Provider ID ' . $provider->id . ' - Active services: ' . ($hasActiveServices ? 'Yes' : 'No') . ', Has availability: ' . ($hasAvailability ? 'Yes' : 'No'));

                // Allow providers with either active services OR basic availability for consultation
                return $hasActiveServices || $hasAvailability;
            });

            \Illuminate\Support\Facades\Log::info('Available providers (with services or availability): ' . $availableProviders->count());

            if ($availableProviders->isEmpty()) {
                \Illuminate\Support\Facades\Log::warning('No providers with services or availability found');
                return [];
            }

            // Step 3: Check each provider's availability
            foreach ($availableProviders as $provider) {
                $providerName = $provider->user ? $provider->user->name : 'Provider ' . $provider->id;
                \Illuminate\Support\Facades\Log::info('=== Checking provider: ' . $providerName . ' (ID: ' . $provider->id . ') ===');

                // Check provider's weekly availability data
                $weeklyAvailability = $provider->getAvailabilityData();
                \Illuminate\Support\Facades\Log::info('Provider ' . $provider->id . ' weekly availability: ' . json_encode($weeklyAvailability));

                // Check if provider has any availability at all
                $hasAnyAvailability = false;
                foreach ($weeklyAvailability as $day) {
                    if (isset($day['slots']) && is_array($day['slots']) && !empty($day['slots'])) {
                        $hasAnyAvailability = true;
                        break;
                    }
                }

                if (!$hasAnyAvailability) {
                    \Illuminate\Support\Facades\Log::info('Provider ' . $provider->id . ' has no weekly availability configured');
                    continue;
                }

                // Check availability for each day
                for ($i = 0; $i < $daysAhead; $i++) {
                    $date = $today->copy()->addDays($i)->format('Y-m-d');
                    $dayOfWeek = $today->copy()->addDays($i)->format('l');

                    \Illuminate\Support\Facades\Log::info('Checking date: ' . $date . ' (' . $dayOfWeek . ')');

                    try {
                        // Use the Provider model's method directly for better reliability
                        $daySlots = $provider->getAvailableTimeSlots($date);

                        \Illuminate\Support\Facades\Log::info('Provider ' . $provider->id . ' slots for ' . $date . ': ' . json_encode($daySlots));

                        if (!empty($daySlots)) {
                            // Add service information if available
                            $slotData = [
                                'provider' => [
                                    'id' => $provider->id,
                                    'name' => $providerName,
                                    'specialization' => $provider->specialization ?? 'General Practice',
                                ],
                                'date' => $date,
                                'day_of_week' => $dayOfWeek,
                                'slots' => array_values($daySlots),
                            ];

                            // Add service information if this provider has services
                            $firstService = $provider->services()->where('active', true)->first();
                            if ($firstService) {
                                $slotData['service'] = [
                                    'id' => $firstService->id,
                                    'name' => $firstService->name,
                                    'duration' => $firstService->duration ?? 30,
                                    'price' => $firstService->price ?? 0,
                                ];
                            } else {
                                // Provide default service info for providers without specific services
                                $slotData['service'] = [
                                    'id' => null,
                                    'name' => 'General Consultation',
                                    'duration' => 30,
                                    'price' => 0,
                                ];
                            }

                            $availableSlots[] = $slotData;
                            \Illuminate\Support\Facades\Log::info('Added slot group for provider ' . $provider->id . ' on ' . $date);
                        }
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('Error getting slots for provider ' . $provider->id . ' on ' . $date . ': ' . $e->getMessage());
                        continue;
                    }
                }
            }

            // Step 4: Sort results
            if (!empty($availableSlots)) {
                usort($availableSlots, function($a, $b) {
                    if ($a['date'] === $b['date']) {
                        // Sort by first slot time if on same date
                        if (!empty($a['slots']) && !empty($b['slots'])) {
                            return strtotime($a['slots'][0]['start_time']) - strtotime($b['slots'][0]['start_time']);
                        }
                        return 0;
                    }
                    return strtotime($a['date']) - strtotime($b['date']);
                });
            }

            \Illuminate\Support\Facades\Log::info('=== Final Results ===');
            \Illuminate\Support\Facades\Log::info('Total available slot groups: ' . count($availableSlots));
            \Illuminate\Support\Facades\Log::info('Available slots data: ' . json_encode($availableSlots));

            return $availableSlots;

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Critical error in getNextAvailableSlots: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error('Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }

    /**
     * Generate a referral note for the healthcare provider based on the conversation
     */
    private function generateReferralNote($conversation)
    {
        try {
            // Get the conversation messages
            $messages = $conversation->messages ?? [];

            // Extract user messages (patient's reported symptoms and concerns)
            $userMessages = array_filter($messages, function($message) {
                return $message['role'] === 'user';
            });

            // Extract AI responses (medical assessment and recommendations)
            $aiMessages = array_filter($messages, function($message) {
                return $message['role'] === 'assistant';
            });

            // Create a summary of the patient's condition
            $patientSymptoms = [];
            $medicalHistory = [];

            foreach ($userMessages as $message) {
                $content = $message['content'] ?? '';
                if (strlen($content) > 10) { // Filter out very short responses
                    $patientSymptoms[] = $content;
                }
            }

            // Get the latest AI assessment
            $latestAssessment = '';
            if (!empty($aiMessages)) {
                $lastAiMessage = end($aiMessages);
                $latestAssessment = $lastAiMessage['content'] ?? '';
            }

            // Generate the referral note
            $referralNote = "REFERRAL NOTE - AI Doctor CONSULTATION\n\n";
            $referralNote .= "Date: " . now()->format('Y-m-d H:i:s') . "\n";
            $referralNote .= "Patient ID: " . $conversation->patient_id . "\n\n";

            $referralNote .= "CHIEF COMPLAINT & SYMPTOMS:\n";
            if (!empty($patientSymptoms)) {
                foreach ($patientSymptoms as $index => $symptom) {
                    $referralNote .= ($index + 1) . ". " . $symptom . "\n";
                }
            } else {
                $referralNote .= "Patient requested general consultation.\n";
            }

            $referralNote .= "\nAI ASSESSMENT:\n";
            if (!empty($latestAssessment)) {
                $referralNote .= $latestAssessment . "\n";
            } else {
                $referralNote .= "Initial consultation - detailed assessment pending.\n";
            }

            $referralNote .= "\nREASON FOR REFERRAL:\n";
            $referralNote .= "Patient has requested to schedule an appointment following AI health consultation. ";
            $referralNote .= "Please conduct thorough examination and provide appropriate medical care.\n\n";

            $referralNote .= "URGENCY LEVEL: Routine\n";
            $referralNote .= "RECOMMENDED ACTION: Clinical evaluation and management as appropriate\n\n";

            $referralNote .= "Note: This referral is generated from an AI Doctor conversation. ";
            $referralNote .= "Please conduct your own independent medical assessment.\n";

            return $referralNote;

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error generating referral note: ' . $e->getMessage());

            // Return a basic referral note if there's an error
            return "REFERRAL NOTE - AI DOCTOR CONSULTATION\n\n" .
                   "Date: " . now()->format('Y-m-d H:i:s') . "\n" .
                   "Patient ID: " . $conversation->patient_id . "\n\n" .
                   "Patient has requested an appointment following AI health consultation. " .
                   "Please conduct thorough examination and provide appropriate medical care.\n";
        }
    }

    /**
     * Check if this conversation has recent appointment booking activity
     */
    private function hasRecentAppointmentBooking($conversation)
    {
        // Check if there's a referral note (indicates appointment was discussed)
        if (!empty($conversation->referral_note)) {
            return true;
        }

        // Check recent messages for appointment-related content
        $messages = $conversation->messages ?? [];
        $recentMessages = array_slice($messages, -10); // Check last 10 messages

        foreach ($recentMessages as $message) {
            if ($message['role'] === 'assistant' &&
                (strpos($message['content'], 'appointment') !== false ||
                 strpos($message['content'], 'booking') !== false ||
                 strpos($message['content'], 'scheduled') !== false ||
                 strpos($message['content'], 'confirmed') !== false)) {
                return true;
            }
        }

        return false;
    }

}
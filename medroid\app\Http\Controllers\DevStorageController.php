<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class DevStorageController extends Controller
{
    /**
     * Serve storage files with CORS headers and security checks
     * For development only - use proper CDN/signed URLs in production
     */
    public function serve($folder, $filename, Request $request = null)
    {
        // If request is still null, get it from the container
        if (!$request instanceof Request) {
            $request = request();
        }

        // Handle preflight OPTIONS request
        if ($request->getMethod() === 'OPTIONS') {
            return $this->corsResponse('', 200);
        }

        // Security: Only allow in development environment
        if (!app()->environment('local')) {
            return response('Not available in production', 403);
        }

        // Security: Validate folder and filename
        if (!$this->isValidPath($folder, $filename)) {
            Log::warning('Invalid file path attempted', [
                'folder' => $folder,
                'filename' => $filename,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            return $this->corsResponse('Invalid path', 400);
        }

        // Security: Check if user has access to this resource
        if (!$this->hasAccess($folder, $filename, $request)) {
            return $this->corsResponse('Access denied', 403);
        }

        // Check both possible storage locations
        $filePath1 = storage_path("app/public/{$folder}/{$filename}");
        $filePath2 = storage_path("app/private/public/{$folder}/{$filename}");

        $filePath = null;
        if (file_exists($filePath1)) {
            $filePath = $filePath1;
        } elseif (file_exists($filePath2)) {
            $filePath = $filePath2;
        }

        if (!$filePath) {
            return $this->corsResponse('File not found', 404);
        }

        $mimeType = mime_content_type($filePath);
        $fileContent = file_get_contents($filePath);

        // Return file with secure CORS headers
        return $this->corsResponse($fileContent, 200, [
            'Content-Type' => $mimeType,
            'Cache-Control' => 'private, max-age=3600',
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
        ]);
    }

    /**
     * Serve storage files from nested folders with CORS headers and security checks
     * For development only - use proper CDN/signed URLs in production
     */
    public function serveNested($folder, $subfolder, $filename, Request $request = null)
    {
        // If request is still null, get it from the container
        if (!$request instanceof Request) {
            $request = request();
        }

        // Handle preflight OPTIONS request
        if ($request->getMethod() === 'OPTIONS') {
            return $this->corsResponse('', 200);
        }

        // Security: Only allow in development environment
        if (!app()->environment('local')) {
            return response('Not available in production', 403);
        }

        // Build the full path
        $fullPath = "{$folder}/{$subfolder}";

        // Security: Validate folder and filename
        if (!$this->isValidPath($fullPath, $filename)) {
            Log::warning('Invalid file path attempted', [
                'folder' => $folder,
                'subfolder' => $subfolder,
                'filename' => $filename,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            return $this->corsResponse('Invalid path', 400);
        }

        // Security: Check if user has access to this resource
        if (!$this->hasAccess($folder, $filename, $request)) {
            return $this->corsResponse('Access denied', 403);
        }

        // Check both possible storage locations
        $filePath1 = storage_path("app/public/{$folder}/{$subfolder}/{$filename}");
        $filePath2 = storage_path("app/private/public/{$folder}/{$subfolder}/{$filename}");

        $filePath = null;
        if (file_exists($filePath1)) {
            $filePath = $filePath1;
        } elseif (file_exists($filePath2)) {
            $filePath = $filePath2;
        }

        if (!$filePath) {
            return $this->corsResponse('File not found', 404);
        }

        $mimeType = mime_content_type($filePath);
        $fileContent = file_get_contents($filePath);

        // Return file with secure CORS headers
        return $this->corsResponse($fileContent, 200, [
            'Content-Type' => $mimeType,
            'Cache-Control' => 'private, max-age=3600',
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
        ]);
    }

    /**
     * Validate that the path is safe and allowed
     */
    private function isValidPath($folderOrPath, $filename)
    {
        // Handle both single folder and nested path
        if (is_string($folderOrPath)) {
            $pathParts = explode('/', $folderOrPath);
            $baseFolder = $pathParts[0];
        } else {
            $baseFolder = $folderOrPath;
            $pathParts = [$baseFolder];
        }

        // Allowed base folders
        $allowedFolders = ['social_media', 'profile_images', 'stories'];

        // Check base folder is allowed
        if (!in_array($baseFolder, $allowedFolders)) {
            return false;
        }

        // Security: Prevent directory traversal in any part of the path
        foreach ($pathParts as $part) {
            if (Str::contains($part, ['..', '\\']) || empty($part)) {
                return false;
            }
        }

        if (Str::contains($filename, ['..', '/', '\\'])) {
            return false;
        }

        // Validate filename format (allow various formats including UUID + extension)
        if (!preg_match('/^[a-zA-Z0-9\-_]+\.(jpg|jpeg|png|gif|webp)$/i', $filename)) {
            return false;
        }

        return true;
    }

    /**
     * Check if user has access to this resource
     */
    private function hasAccess($folder, $filename, Request $request)
    {
        // For social_media, stories, and profile_images folders, allow public access
        // Profile images are public since they're displayed in feeds and profiles
        if (in_array($folder, ['social_media', 'stories', 'profile_images'])) {
            return true;
        }

        return false;
    }

    /**
     * Return response with consistent CORS headers
     */
    private function corsResponse($content, $status, $additionalHeaders = [])
    {
        // Determine allowed origin based on environment
        $origin = request()->header('Origin');
        $allowedOrigin = '*';

        if (app()->environment('local')) {
            // For local development: allow any localhost or 127.0.0.1 with any port
            if ($origin && (
                preg_match('/^https?:\/\/localhost(:\d+)?$/', $origin) ||
                preg_match('/^https?:\/\/127\.0\.0\.1(:\d+)?$/', $origin)
            )) {
                $allowedOrigin = $origin;
            } else {
                $allowedOrigin = '*'; // Fallback for local development
            }
        } else {
            // For production: only allow specific domains
            $frontendUrl = env('FRONTEND_URL', 'https://app.medroid.ai');
            $allowedOrigins = [
                $frontendUrl,
                str_replace('app.', 'www.', $frontendUrl),
                str_replace('app.', '', $frontendUrl)
            ];

            if ($origin && in_array($origin, $allowedOrigins)) {
                $allowedOrigin = $origin;
            } else {
                $allowedOrigin = $frontendUrl; // Default for production
            }
        }

        $headers = array_merge([
            'Access-Control-Allow-Origin' => $allowedOrigin,
            'Access-Control-Allow-Methods' => 'GET, OPTIONS',
            'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept, Authorization, X-Requested-With',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
        ], $additionalHeaders);

        return response($content, $status, $headers);
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GoogleSearchService
{
    private $projectId;
    private $engineId;
    private $maxResults;
    private $timeout;
    private $cacheTtl;
    private $enabled;

    public function __construct()
    {
        $this->projectId = config('services.google.vertex_project_id', '1026523769008');
        $this->engineId = config('services.google.discovery_engine_id', 'trusted-sources_1748608202932');
        $this->maxResults = 5; // Limit to top 5 results as requested
        $this->timeout = config('services.google.discovery_timeout', 30);
        $this->cacheTtl = config('services.google.discovery_cache_ttl', 3600);
        $this->enabled = config('services.google.discovery_enabled', true);
    }
    
    /**
     * Perform Google Search with caching and error handling
     */
    public function search(string $query): ?array
    {
        if (!$this->enabled) {
            Log::info('Google Search disabled in configuration');
            return $this->createEmptySearchResult();
        }

        try {
            // Create cache key
            $cacheKey = 'google_search_' . md5($query);

            // Check cache first
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult) {
                Log::info('Google Search cache hit', ['query' => $query]);
                return $cachedResult;
            }

            // Get OAuth token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::warning('Failed to obtain Google OAuth token - returning empty results');
                return $this->createEmptySearchResult();
            }

            // Prepare search query
            $searchQuery = $this->optimizeSearchQuery($query);

            // Make search request
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$accessToken}",
                'Content-Type' => 'application/json'
            ])->timeout($this->timeout)->post(
                "https://discoveryengine.googleapis.com/v1alpha/projects/{$this->projectId}/locations/global/collections/default_collection/engines/{$this->engineId}/servingConfigs/default_search:search",
                [
                    'query' => $searchQuery,
                    'pageSize' => $this->maxResults,
                    'queryExpansionSpec' => ['condition' => 'AUTO'],
                    'spellCorrectionSpec' => ['mode' => 'AUTO'],
                    'languageCode' => 'en-US',
                    'userInfo' => ['timeZone' => 'UTC']
                ]
            );

            if ($response->successful()) {
                $results = $response->json();

                Log::info('Google Discovery Engine raw response', [
                    'query' => $searchQuery,
                    'results_structure' => array_keys($results),
                    'has_results' => isset($results['results']),
                    'results_count' => isset($results['results']) ? count($results['results']) : 0
                ]);

                // Process and format results
                $formattedResults = $this->formatSearchResults($results);

                // If Discovery Engine returns no results, try Custom Search API as fallback
                if (empty($formattedResults['results'])) {
                    Log::info('Discovery Engine returned no results, trying Custom Search API fallback', ['query' => $searchQuery]);
                    $customSearchResults = $this->customSearchFallback($searchQuery);
                    if ($customSearchResults && !empty($customSearchResults['results'])) {
                        Log::info('Custom Search API fallback successful', ['results_count' => count($customSearchResults['results'])]);
                        $formattedResults = $customSearchResults;
                    } else {
                        Log::warning('Both Discovery Engine and Custom Search API returned no results - returning empty result');
                        $formattedResults = $this->createEmptySearchResult();
                    }
                }

                // Cache the results
                Cache::put($cacheKey, $formattedResults, $this->cacheTtl);

                Log::info('Google Search successful', [
                    'query' => $query,
                    'results_count' => count($formattedResults['results'] ?? [])
                ]);

                return $formattedResults;
            }

            Log::warning('Google Search API error', [
                'status' => $response->status(),
                'query' => $query
            ]);
            return $this->createEmptySearchResult();

        } catch (\Exception $e) {
            Log::error('Google Search failed', [
                'error' => $e->getMessage(),
                'query' => $query
            ]);
            return $this->createEmptySearchResult();
        }
    }

    /**
     * Create empty search result structure
     */
    private function createEmptySearchResult(): array
    {
        return [
            'results' => [],
            'citations' => [],
            'total_results' => 0
        ];
    }

    /**
     * Legacy method for backward compatibility
     */
    public function searchMedicalEvidence($query, $options = [])
    {
        return $this->search($query);
    }

    /**
     * Get OAuth access token using service account
     */
    private function getAccessToken(): ?string
    {
        try {
            // Try using gcloud CLI first (for development)
            $accessTokenRaw = shell_exec('gcloud auth print-access-token 2>/dev/null');
            $accessToken = $accessTokenRaw ? trim($accessTokenRaw) : null;

            if (!empty($accessToken) && $accessToken !== 'null') {
                return $accessToken;
            }

            // Fallback to service account authentication
            return $this->getServiceAccountToken();

        } catch (\Exception $e) {
            Log::error('Failed to get Google access token', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get access token using service account credentials
     */
    private function getServiceAccountToken(): ?string
    {
        $clientEmail = config('services.google.client_email');
        $privateKey = config('services.google.private_key');
        $projectId = config('services.google.project_id');

        if (!$clientEmail || !$privateKey || !$projectId) {
            Log::warning('Google service account credentials not configured');
            return null;
        }

        try {
            // Create JWT for service account authentication
            $now = time();
            $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
            $payload = json_encode([
                'iss' => $clientEmail,
                'scope' => 'https://www.googleapis.com/auth/cloud-platform',
                'aud' => 'https://oauth2.googleapis.com/token',
                'exp' => $now + 3600,
                'iat' => $now
            ]);

            $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
            $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

            $signature = '';
            openssl_sign($base64Header . '.' . $base64Payload, $signature, $privateKey, OPENSSL_ALGO_SHA256);
            $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

            $jwt = $base64Header . '.' . $base64Payload . '.' . $base64Signature;

            // Exchange JWT for access token
            $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['access_token'] ?? null;
            }

            Log::error('Service account token exchange failed', ['status' => $response->status()]);
            return null;

        } catch (\Exception $e) {
            Log::error('Service account authentication failed', ['error' => $e->getMessage()]);
            return null;
        }
    }
    /**
     * Optimize search query for better results
     */
    private function optimizeSearchQuery(string $query): string
    {
        // Remove common question words but keep health-specific terms
        $stopWords = ['what', 'how', 'when', 'where', 'why', 'is', 'are', 'can', 'should', 'tell', 'me', 'about'];
        $words = explode(' ', strtolower($query));
        $filteredWords = array_filter($words, function($word) use ($stopWords) {
            return !in_array($word, $stopWords) && strlen($word) > 2;
        });

        $searchTerms = implode(' ', array_slice($filteredWords, 0, 6));

        // Add health context if not already present
        if (!stripos($searchTerms, 'health') && !stripos($searchTerms, 'research') && !stripos($searchTerms, 'study')) {
            $searchTerms .= ' health research';
        }

        return $searchTerms;
    }

    /**
     * Format search results with Perplexity-style structure
     */
    private function formatSearchResults(array $results): array
    {
        if (!isset($results['results']) || empty($results['results'])) {
            Log::info('formatSearchResults: No results found in response', [
                'has_results_key' => isset($results['results']),
                'results_empty' => empty($results['results'] ?? []),
                'response_keys' => array_keys($results)
            ]);
            return ['results' => [], 'citations' => []];
        }

        $formattedResults = [];
        $citations = [];
        $citationIndex = 1;

        Log::info('formatSearchResults: Processing results', [
            'total_results' => count($results['results'])
        ]);

        foreach ($results['results'] as $index => $result) {
            if (!isset($result['document']['derivedStructData'])) {
                Log::info("formatSearchResults: Skipping result {$index} - no derivedStructData", [
                    'result_keys' => array_keys($result),
                    'document_keys' => isset($result['document']) ? array_keys($result['document']) : 'no document'
                ]);
                continue;
            }

            $data = $result['document']['derivedStructData'];
            $title = $data['title'] ?? 'Untitled';
            $snippet = $data['snippet'] ?? '';
            $link = $data['link'] ?? '';

            if (empty($snippet) || empty($link)) {
                Log::info("formatSearchResults: Skipping result {$index} - missing snippet or link", [
                    'title' => $title,
                    'has_snippet' => !empty($snippet),
                    'has_link' => !empty($link)
                ]);
                continue;
            }

            // Create citation
            $citation = [
                'index' => $citationIndex,
                'title' => $title,
                'url' => $link,
                'snippet' => $snippet
            ];

            $citations[] = $citation;

            // Format result with citation reference
            $formattedResults[] = [
                'title' => $title,
                'snippet' => $snippet,
                'url' => $link,
                'citation_index' => $citationIndex
            ];

            $citationIndex++;
        }

        return [
            'results' => $formattedResults,
            'citations' => $citations,
            'total_results' => count($formattedResults)
        ];
    }

    /**
     * Format search context for LLM with Perplexity-style citations
     */
    public function formatSearchContext(array $searchResults): string
    {
        if (empty($searchResults['results'])) {
            return '';
        }

        $context = "Here are current, trusted sources I found:\n\n";

        foreach ($searchResults['results'] as $result) {
            $context .= "Source [{$result['citation_index']}]: {$result['title']}\n";
            $context .= "Content: {$result['snippet']}\n\n";
        }

        return $context;
    }

    /**
     * Generate Perplexity-style citations for response
     */
    public function generateCitations(array $searchResults): string
    {
        if (empty($searchResults['citations'])) {
            return '';
        }

        $citations = "\n\n**Sources:**\n";

        foreach ($searchResults['citations'] as $citation) {
            $citations .= "[{$citation['index']}] <a href=\"{$citation['url']}\" target=\"_blank\" class=\"text-blue-600 hover:text-blue-800 underline\">{$citation['title']}</a>\n";
        }

        return $citations;
    }

    /**
     * Fallback to Google Custom Search API when Discovery Engine fails
     */
    private function customSearchFallback(string $query): ?array
    {
        try {
            $apiKey = config('services.google.ai_studio_api_key');
            $searchEngineId = '017576662512468239146:omuauf_lfve'; // Programmable Search Engine ID

            if (!$apiKey) {
                Log::warning('Google Custom Search API key not configured');
                return null;
            }

            $response = Http::timeout($this->timeout)->get('https://www.googleapis.com/customsearch/v1', [
                'key' => $apiKey,
                'cx' => $searchEngineId,
                'q' => $query,
                'num' => $this->maxResults,
                'safe' => 'active'
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (!isset($data['items']) || empty($data['items'])) {
                    return null;
                }

                $formattedResults = [];
                $citations = [];
                $citationIndex = 1;

                foreach (array_slice($data['items'], 0, $this->maxResults) as $item) {
                    $title = $item['title'] ?? 'Untitled';
                    $snippet = $item['snippet'] ?? '';
                    $link = $item['link'] ?? '';

                    if (empty($title) || empty($link)) {
                        continue;
                    }

                    $citations[] = [
                        'index' => $citationIndex,
                        'title' => $title,
                        'url' => $link,
                        'snippet' => $snippet
                    ];

                    $formattedResults[] = [
                        'title' => $title,
                        'snippet' => $snippet,
                        'url' => $link,
                        'citation_index' => $citationIndex
                    ];

                    $citationIndex++;
                }

                Log::info('Custom Search API fallback successful', [
                    'query' => $query,
                    'results_count' => count($formattedResults)
                ]);

                return [
                    'results' => $formattedResults,
                    'citations' => $citations,
                    'total_results' => count($formattedResults)
                ];
            }

            Log::warning('Custom Search API fallback failed', [
                'status' => $response->status(),
                'query' => $query
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('Custom Search API fallback error', [
                'error' => $e->getMessage(),
                'query' => $query
            ]);
            return null;
        }
    }

    /**
     * Create realistic health-related search results when APIs fail
     */
    private function createRealisticHealthResults(string $query): array
    {
        // Map common health queries to realistic sources
        $healthSources = [
            'weight loss' => [
                ['title' => 'Weight Loss: 6 Strategies for Success - Mayo Clinic', 'url' => 'https://www.mayoclinic.org/healthy-lifestyle/weight-loss/in-depth/weight-loss/art-20047752', 'snippet' => 'Successful weight loss requires a long-term commitment to making healthy lifestyle changes in eating, physical activity and behavior.'],
                ['title' => 'Healthy Weight Loss - NHS', 'url' => 'https://www.nhs.uk/live-well/healthy-weight/managing-your-weight/12-tips-to-help-you-lose-weight/', 'snippet' => 'The best way to lose weight is to make small, realistic changes to your diet and physical activity that you can stick to for life.'],
                ['title' => 'Weight Management - Harvard Health', 'url' => 'https://www.health.harvard.edu/topics/diet-and-weight-loss', 'snippet' => 'Research shows that the most effective approach to weight loss combines dietary changes with increased physical activity.']
            ],
            'protein' => [
                ['title' => 'Protein: How Much Do You Need? - Harvard Health', 'url' => 'https://www.health.harvard.edu/blog/how-much-protein-do-you-need-every-day-201506188096', 'snippet' => 'The Recommended Dietary Allowance (RDA) for protein is 0.8 grams per kilogram of body weight for healthy adults.'],
                ['title' => 'Protein Requirements - British Nutrition Foundation', 'url' => 'https://www.nutrition.org.uk/healthy-sustainable-diets/protein/', 'snippet' => 'Most adults need around 0.75g of protein per kilo of body weight per day (for the average woman, this is 45g, or 55g for men).'],
                ['title' => 'Protein and Health - Mayo Clinic', 'url' => 'https://www.mayoclinic.org/healthy-lifestyle/nutrition-and-healthy-eating/in-depth/protein/art-20043983', 'snippet' => 'Protein is essential for building and maintaining muscle mass, especially as you age.']
            ],
            'vitamin' => [
                ['title' => 'Vitamin D - NHS', 'url' => 'https://www.nhs.uk/conditions/vitamins-and-minerals/vitamin-d/', 'snippet' => 'Vitamin D helps regulate the amount of calcium and phosphate in the body, which are needed to keep bones, teeth and muscles healthy.'],
                ['title' => 'Vitamin D Benefits - Harvard Health', 'url' => 'https://www.health.harvard.edu/staying-healthy/vitamin-d-and-your-health-breaking-old-rules-raising-new-hopes', 'snippet' => 'Research suggests vitamin D may play a role in preventing and treating conditions including heart disease, diabetes, and certain cancers.'],
                ['title' => 'Vitamins and Minerals - Mayo Clinic', 'url' => 'https://www.mayoclinic.org/healthy-lifestyle/nutrition-and-healthy-eating/in-depth/supplements/art-20044894', 'snippet' => 'Most people can get adequate vitamins and minerals from a balanced diet, but supplements may be beneficial in certain cases.']
            ],
            'exercise' => [
                ['title' => 'Exercise Benefits - NHS', 'url' => 'https://www.nhs.uk/live-well/exercise/exercise-health-benefits/', 'snippet' => 'Regular exercise can reduce your risk of major illnesses, such as coronary heart disease, stroke, type 2 diabetes and cancer.'],
                ['title' => 'Physical Activity Guidelines - Harvard Health', 'url' => 'https://www.health.harvard.edu/staying-healthy/why-you-should-exercise', 'snippet' => 'Adults should get at least 150 minutes of moderate-intensity aerobic activity or 75 minutes of vigorous activity each week.'],
                ['title' => 'Exercise and Health - Mayo Clinic', 'url' => 'https://www.mayoclinic.org/healthy-lifestyle/fitness/in-depth/exercise/art-20048389', 'snippet' => 'Regular physical activity can improve your muscle strength and boost your endurance, delivering oxygen and nutrients to your tissues.']
            ]
        ];

        // Find the most relevant sources based on query
        $relevantSources = [];
        $queryLower = strtolower($query);

        foreach ($healthSources as $topic => $sources) {
            if (strpos($queryLower, $topic) !== false) {
                $relevantSources = array_merge($relevantSources, $sources);
            }
        }

        // If no specific match, use general health sources
        if (empty($relevantSources)) {
            $relevantSources = [
                ['title' => 'Health Information - NHS', 'url' => 'https://www.nhs.uk/conditions/', 'snippet' => 'Find reliable health information and advice from the NHS on a wide range of conditions and treatments.'],
                ['title' => 'Health Topics - Mayo Clinic', 'url' => 'https://www.mayoclinic.org/diseases-conditions', 'snippet' => 'Comprehensive health information from Mayo Clinic, covering symptoms, causes, and treatments for various conditions.'],
                ['title' => 'Harvard Health Publishing', 'url' => 'https://www.health.harvard.edu/', 'snippet' => 'Trusted health information from Harvard Medical School experts on nutrition, fitness, and wellness.']
            ];
        }

        // Format results with citations
        $formattedResults = [];
        $citations = [];
        $citationIndex = 1;

        foreach (array_slice($relevantSources, 0, 3) as $source) {
            $citations[] = [
                'index' => $citationIndex,
                'title' => $source['title'],
                'url' => $source['url'],
                'snippet' => $source['snippet']
            ];

            $formattedResults[] = [
                'title' => $source['title'],
                'snippet' => $source['snippet'],
                'url' => $source['url'],
                'citation_index' => $citationIndex
            ];

            $citationIndex++;
        }

        Log::info('Using realistic health search results as fallback', [
            'query' => $query,
            'results_count' => count($formattedResults)
        ]);

        return [
            'results' => $formattedResults,
            'citations' => $citations,
            'total_results' => count($formattedResults)
        ];
    }
}

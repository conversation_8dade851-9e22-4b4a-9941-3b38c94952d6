<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductReview;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ProductReviewController extends Controller
{
    /**
     * Get reviews for a product
     */
    public function index(Request $request, $productId)
    {
        $product = Product::findOrFail($productId);

        $query = $product->reviews()->with(['user']);

        // Filter by current user only (for order detail page)
        if ($request->boolean('user_only') && Auth::check()) {
            $query->where('user_id', Auth::id());
        }

        // Filter by rating
        if ($request->filled('rating')) {
            $query->byRating($request->rating);
        }

        // Filter by verified purchases only
        if ($request->boolean('verified_only')) {
            $query->verifiedPurchase();
        }

        $reviews = $query->paginate(10);

        // Get rating distribution
        $ratingDistribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $ratingDistribution[$i] = $product->reviews()->byRating($i)->count();
        }

        return response()->json([
            'reviews' => $reviews,
            'rating_distribution' => $ratingDistribution,
            'average_rating' => $product->average_rating,
            'total_reviews' => $product->review_count,
        ]);
    }

    /**
     * Store a new review
     */
    public function store(Request $request, $productId)
    {
        $user = Auth::user();
        $product = Product::findOrFail($productId);

        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'required|string|max:1000',
            'order_item_id' => 'nullable|exists:order_items,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if user already reviewed this product
        $existingReview = ProductReview::where('user_id', $user->id)
            ->where('product_id', $productId)
            ->first();

        if ($existingReview) {
            return response()->json(['message' => 'You have already reviewed this product'], 422);
        }

        // Check if this is a verified purchase
        $isVerifiedPurchase = false;
        if ($request->order_item_id) {
            $orderItem = OrderItem::where('id', $request->order_item_id)
                ->where('product_id', $productId)
                ->whereHas('order', function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                          ->where('status', 'completed');
                })
                ->first();

            if ($orderItem) {
                $isVerifiedPurchase = true;
            }
        }

        $review = ProductReview::create([
            'product_id' => $productId,
            'user_id' => $user->id,
            'order_item_id' => $request->order_item_id,
            'rating' => $request->rating,
            'title' => $request->title,
            'comment' => $request->comment,
            'is_verified_purchase' => $isVerifiedPurchase,
            'is_approved' => true, // Auto-approve for now
            'approved_at' => now(),
        ]);

        $review->load('user');

        return response()->json([
            'message' => 'Review submitted successfully',
            'review' => $review,
        ], 201);
    }

    /**
     * Update a review
     */
    public function update(Request $request, $productId, $reviewId)
    {
        $user = Auth::user();
        $review = ProductReview::where('id', $reviewId)
            ->where('product_id', $productId)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $review->update([
            'rating' => $request->rating,
            'title' => $request->title,
            'comment' => $request->comment,
        ]);

        $review->load('user');

        return response()->json([
            'message' => 'Review updated successfully',
            'review' => $review,
        ]);
    }

    /**
     * Delete a review
     */
    public function destroy($productId, $reviewId)
    {
        $user = Auth::user();
        $review = ProductReview::where('id', $reviewId)
            ->where('product_id', $productId)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $review->delete();

        return response()->json([
            'message' => 'Review deleted successfully',
        ]);
    }

    /**
     * Check if user can review a product
     */
    public function canReview($productId)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['can_review' => false, 'reason' => 'Not authenticated']);
        }

        // Check if user already reviewed this product
        $existingReview = ProductReview::where('user_id', $user->id)
            ->where('product_id', $productId)
            ->first();

        if ($existingReview) {
            return response()->json(['can_review' => false, 'reason' => 'Already reviewed']);
        }

        // Check if user has purchased this product
        $hasPurchased = OrderItem::where('product_id', $productId)
            ->whereHas('order', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->where('status', 'completed');
            })
            ->exists();

        return response()->json([
            'can_review' => true,
            'has_purchased' => $hasPurchased,
        ]);
    }
}

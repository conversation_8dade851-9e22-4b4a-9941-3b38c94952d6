<?php

namespace App\Console\Commands;

use App\Models\SocialContent;
use App\Services\FakeEngagementService;
use Illuminate\Console\Command;

class AddFakeEngagement extends Command
{
    protected $signature = 'bot:add-engagement {--all : Add engagement to all bot posts} {--recent : Add engagement to recent bot posts only}';
    protected $description = 'Add fake engagement metrics to bot-generated posts';

    public function handle()
    {
        $this->info('🎯 Adding fake engagement to bot posts...');
        
        $engagementService = new FakeEngagementService();
        
        if ($this->option('all')) {
            $this->addEngagementToAllBotPosts($engagementService);
        } elseif ($this->option('recent')) {
            $this->addEngagementToRecentBotPosts($engagementService);
        } else {
            $this->addEngagementToTodaysBotPosts($engagementService);
        }
        
        $this->info('✅ Fake engagement addition completed!');
        
        return 0;
    }
    
    private function addEngagementToAllBotPosts(FakeEngagementService $service): void
    {
        $posts = SocialContent::where('source', 'bot_generated')
            ->whereNull('engagement_metrics')
            ->get();
        
        $this->info("Found {$posts->count()} bot posts without engagement metrics");
        
        $bar = $this->output->createProgressBar($posts->count());
        $bar->start();
        
        foreach ($posts as $post) {
            $service->addFakeEngagement($post);
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        $this->info("Added engagement to {$posts->count()} bot posts");
    }
    
    private function addEngagementToRecentBotPosts(FakeEngagementService $service): void
    {
        $count = $service->addEngagementToRecentBotPosts();
        $this->info("Added engagement to {$count} recent bot posts");
    }
    
    private function addEngagementToTodaysBotPosts(FakeEngagementService $service): void
    {
        $posts = SocialContent::where('source', 'bot_generated')
            ->whereDate('created_at', today())
            ->whereNull('engagement_metrics')
            ->get();
        
        $this->info("Found {$posts->count()} bot posts from today without engagement metrics");
        
        foreach ($posts as $post) {
            $service->addFakeEngagement($post);
            $this->line("✓ Added engagement to post {$post->id}");
        }
        
        $this->info("Added engagement to {$posts->count()} today's bot posts");
    }
}

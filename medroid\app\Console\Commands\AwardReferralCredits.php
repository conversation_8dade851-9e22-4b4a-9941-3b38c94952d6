<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Referral;
use App\Services\ReferralService;
use App\Services\UserActivityService;
use Illuminate\Support\Facades\Log;

class AwardReferralCredits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'referrals:award-credits {--force : Force award credits without activity check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Award credits for referrals where referred users have been active for 3+ consecutive days';

    protected $referralService;
    protected $userActivityService;

    /**
     * Create a new command instance.
     */
    public function __construct(ReferralService $referralService, UserActivityService $userActivityService)
    {
        parent::__construct();
        $this->referralService = $referralService;
        $this->userActivityService = $userActivityService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting referral credit award process...');
        
        $force = $this->option('force');
        
        // Get all completed referrals that haven't been credited yet
        $referrals = Referral::where('status', 'completed')
            ->where('credit_awarded', false)
            ->whereNotNull('referred_id')
            ->with(['referrer', 'referred'])
            ->get();
            
        $this->info("Found {$referrals->count()} referrals to check");
        
        $awarded = 0;
        $skipped = 0;
        
        foreach ($referrals as $referral) {
            try {
                if (!$referral->referred) {
                    $this->warn("Referral ID {$referral->id}: Referred user not found");
                    $skipped++;
                    continue;
                }
                
                if (!$referral->referrer) {
                    $this->warn("Referral ID {$referral->id}: Referrer not found");
                    $skipped++;
                    continue;
                }
                
                // Check if referred user has 3+ consecutive days of activity
                $hasActivity = $this->userActivityService->hasConsecutiveDaysActivity($referral->referred, 3);
                
                if ($hasActivity || $force) {
                    $result = $this->referralService->awardReferralCredit($referral, $force);
                    
                    if ($result) {
                        $this->info("✓ Awarded ${referral->credit_amount} credit to {$referral->referrer->name} for referring {$referral->referred->name}");
                        $awarded++;
                    } else {
                        $this->warn("✗ Failed to award credit for referral ID {$referral->id}");
                        $skipped++;
                    }
                } else {
                    $activityDays = $this->userActivityService->getUserActivityStreak($referral->referred);
                    $this->info("- Referral ID {$referral->id}: User {$referral->referred->name} has {$activityDays} days of activity (needs 3)");
                    $skipped++;
                }
                
            } catch (\Exception $e) {
                $this->error("Error processing referral ID {$referral->id}: " . $e->getMessage());
                Log::error("Error in referral credit award command", [
                    'referral_id' => $referral->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $skipped++;
            }
        }
        
        $this->info("\nReferral credit award process completed!");
        $this->info("Credits awarded: {$awarded}");
        $this->info("Referrals skipped: {$skipped}");
        
        return 0;
    }
}

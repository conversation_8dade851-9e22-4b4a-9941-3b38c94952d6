<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'user_id',
        'status',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'payment_status',
        'payment_method',
        'payment_intent_id',
        'billing_address',
        'shipping_address',
        'shipping_method',
        'shipping_company',
        'tracking_number',
        'shipped_at',
        'dispatched_at',
        'dispatched_by',
        'delivered_at',
        'review_follow_up_sent_at',
        'notes',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'shipped_at' => 'datetime',
        'dispatched_at' => 'datetime',
        'delivered_at' => 'datetime',
        'review_follow_up_sent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_total',
        'formatted_subtotal',
        'formatted_shipping',
        'formatted_tax',
        'status_label',
        'payment_status_label',
        'status_color',
        'can_be_cancelled',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function digitalDownloads()
    {
        return $this->hasMany(DigitalProductDownload::class);
    }

    public function dispatchedBy()
    {
        return $this->belongsTo(User::class, 'dispatched_by');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPaymentStatus($query, $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    public function getFormattedTotalAttribute()
    {
        return '£' . number_format($this->total_amount, 2);
    }

    public function getFormattedSubtotalAttribute()
    {
        return '£' . number_format($this->subtotal, 2);
    }

    public function getFormattedShippingAttribute()
    {
        return '£' . number_format($this->shipping_amount, 2);
    }

    public function getFormattedTaxAttribute()
    {
        return '£' . number_format($this->tax_amount, 2);
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'pending' => 'Pending',
            'processing' => 'Processing',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
            default => ucfirst($this->status)
        };
    }

    public function getPaymentStatusLabelAttribute()
    {
        return match($this->payment_status) {
            'pending' => 'Pending',
            'paid' => 'Paid',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
            default => ucfirst($this->payment_status)
        };
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'processing' => 'blue',
            'shipped' => 'purple',
            'delivered' => 'green',
            'cancelled' => 'red',
            'refunded' => 'gray',
            default => 'gray'
        };
    }

    public function hasPhysicalItems()
    {
        return $this->items()->where('product_type', 'physical')->exists();
    }

    public function hasDigitalItems()
    {
        return $this->items()->where('product_type', 'digital')->exists();
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    public function getCanBeCancelledAttribute()
    {
        return $this->canBeCancelled();
    }

    public function canBeShipped()
    {
        return $this->status === 'processing' && $this->hasPhysicalItems();
    }

    public function markAsShipped($trackingNumber = null)
    {
        $this->update([
            'status' => 'shipped',
            'shipped_at' => now(),
            'tracking_number' => $trackingNumber,
        ]);
    }

    public function markAsDispatched($trackingNumber = null, $shippingCompany = null, $dispatchedBy = null)
    {
        $this->update([
            'status' => 'shipped',
            'shipped_at' => now(),
            'dispatched_at' => now(),
            'tracking_number' => $trackingNumber,
            'shipping_company' => $shippingCompany,
            'dispatched_by' => $dispatchedBy,
        ]);
    }

    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function cancel()
    {
        if (!$this->canBeCancelled()) {
            throw new \Exception('Order cannot be cancelled');
        }

        $this->update(['status' => 'cancelled']);
        
        // Restore stock for physical items
        foreach ($this->items as $item) {
            if ($item->product_type === 'physical') {
                $item->product->increaseStock($item->quantity);
            }
        }
    }

    public static function generateOrderNumber()
    {
        do {
            $orderNumber = 'ORD-' . strtoupper(uniqid());
        } while (static::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }
}

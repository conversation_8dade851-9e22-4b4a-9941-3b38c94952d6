<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bot_personas', function (Blueprint $table) {
            $table->text('avatar_url')->nullable()->after('image_prompt');
            $table->text('face_reference_url')->nullable()->after('avatar_url');
            $table->text('physical_description')->nullable()->after('face_reference_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bot_personas', function (Blueprint $table) {
            $table->dropColumn(['avatar_url', 'face_reference_url', 'physical_description']);
        });
    }
};

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:admin-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new admin user for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating admin user...');

        // Check if admin already exists
        $existingAdmin = User::where('email', '<EMAIL>')->first();

        if ($existingAdmin) {
            $this->info('Admin user already exists with email: <EMAIL>');
            return 0;
        }

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'phone_number' => '******-000-0001',
            'is_active' => true,
            'signup_source' => 'admin_created',
        ]);

        $admin->assignRole('admin');
        $admin->update(['role' => 'admin']);

        $this->info('Admin user created successfully!');
        $this->info('Email: <EMAIL>');
        $this->info('Password: password');
        $this->warn('Please change the password after first login.');

        return 0;
    }
}

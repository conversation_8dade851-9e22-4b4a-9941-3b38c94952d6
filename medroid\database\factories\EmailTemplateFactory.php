<?php

namespace Database\Factories;

use App\Models\EmailTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $templateTypes = [
            'user-registration' => [
                'name' => 'User Registration',
                'subject' => 'Welcome to {{ $appName }}!',
                'content' => 'Hello {{ $userName }}, welcome to {{ $appName }}! Your account has been created successfully.',
            ],
            'appointment-reminder' => [
                'name' => 'Appointment Reminder',
                'subject' => 'Appointment Reminder - {{ $appointmentDate }}',
                'content' => 'Dear {{ $patientName }}, this is a reminder for your appointment with {{ $providerName }} on {{ $appointmentDate }}.',
            ],
            'password-reset' => [
                'name' => 'Password Reset',
                'subject' => 'Reset Your Password',
                'content' => 'Hello {{ $userName }}, click the link below to reset your password: {{ $resetUrl }}',
            ],
            'notification' => [
                'name' => 'General Notification',
                'subject' => 'Notification from {{ $appName }}',
                'content' => 'Hello {{ $userName }}, you have a new notification: {{ $message }}',
            ],
        ];

        $type = $this->faker->randomElement(array_keys($templateTypes));
        $template = $templateTypes[$type];
        
        $uniqueId = $this->faker->unique()->numberBetween(1000, 9999);
        
        return [
            'name' => $template['name'] . ' ' . $uniqueId,
            'slug' => $type . '-' . $uniqueId,
            'subject' => $template['subject'],
            'content' => $template['content'],
            'description' => $this->faker->sentence(),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the email template should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a user registration template.
     */
    public function userRegistration(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'User Registration',
            'slug' => 'user-registration-' . $this->faker->unique()->numberBetween(1000, 9999),
            'subject' => 'Welcome to {{ $appName }}!',
            'content' => 'Hello {{ $userName }}, welcome to {{ $appName }}! Your account has been created successfully. Click here to get started: {{ $loginUrl }}',
            'description' => 'Email sent to users when they register',
        ]);
    }

    /**
     * Create an appointment template.
     */
    public function appointment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Appointment Notification',
            'slug' => 'appointment-' . $this->faker->unique()->numberBetween(1000, 9999),
            'subject' => 'Appointment Update - {{ $appointmentDate }}',
            'content' => 'Dear {{ $patientName }}, your appointment with {{ $providerName }} on {{ $appointmentDate }} has been updated. Please contact us if you have any questions.',
            'description' => 'Email sent for appointment notifications',
        ]);
    }

    /**
     * Create a password reset template.
     */
    public function passwordReset(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Password Reset',
            'slug' => 'password-reset-' . $this->faker->unique()->numberBetween(1000, 9999),
            'subject' => 'Reset Your Password',
            'content' => 'Hello {{ $userName }}, you requested a password reset. Click the link below to reset your password: {{ $resetUrl }}. If you did not request this, please ignore this email.',
            'description' => 'Email sent for password reset requests',
        ]);
    }

    /**
     * Create a template with HTML content.
     */
    public function withHtml(): static
    {
        return $this->state(fn (array $attributes) => [
            'content' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
                        <h1 style="color: #2563eb;">{{ $appName }}</h1>
                    </div>
                    <div style="padding: 20px;">
                        <h2>Hello {{ $userName }}!</h2>
                        <p>This is an HTML email template with styling.</p>
                        <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p><strong>Important:</strong> This is a highlighted message.</p>
                        </div>
                        <p>Thank you for using {{ $appName }}!</p>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 10px; text-align: center; font-size: 12px; color: #666;">
                        <p>&copy; 2024 {{ $appName }}. All rights reserved.</p>
                    </div>
                </div>
            ',
        ]);
    }

    /**
     * Create a template with long content.
     */
    public function withLongContent(): static
    {
        $longContent = 'Hello {{ $userName }}, ' . str_repeat('This is a very long email template content that tests how the system handles large amounts of text. ', 50) . ' Thank you for using {{ $appName }}!';
        
        return $this->state(fn (array $attributes) => [
            'content' => $longContent,
        ]);
    }

    /**
     * Create a template with special characters and emojis.
     */
    public function withSpecialCharacters(): static
    {
        return $this->state(fn (array $attributes) => [
            'subject' => '🎉 Welcome to {{ $appName }}! 🚀',
            'content' => 'Hello {{ $userName }}! 👋 Welcome to {{ $appName }}! 🎊 Your account is ready. Special characters: @#$%^&*()_+-=[]{}|;:,.<>? Emojis: 😊 🏥 💊 📅 ✅',
        ]);
    }

    /**
     * Create a referral invitation template.
     */
    public function referralInvitation(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Referral Invitation',
            'slug' => 'referral-invitation-' . $this->faker->unique()->numberBetween(1000, 9999),
            'subject' => '🎁 You\'ve been invited to join {{ $appName }}!',
            'content' => 'Hi there! {{ $referrerName }} has invited you to join {{ $appName }}. Use referral code {{ $referralCode }} to sign up and get special benefits: {{ $signupUrl }}',
            'description' => 'Email sent for referral invitations',
        ]);
    }

    /**
     * Create a waitlist invitation template.
     */
    public function waitlistInvitation(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Waitlist Invitation',
            'slug' => 'waitlist-invitation-' . $this->faker->unique()->numberBetween(1000, 9999),
            'subject' => '🤖 EXCLUSIVE: Welcome to the {{ $appName }} Founders\' Club',
            'content' => 'Congratulations! You\'ve been selected from our waitlist to join {{ $appName }} early access. Click here to complete your registration: {{ $signupUrl }}',
            'description' => 'Email sent for waitlist invitations',
        ]);
    }
}

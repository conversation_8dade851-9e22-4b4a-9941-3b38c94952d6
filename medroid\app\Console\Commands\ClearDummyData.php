<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Provider;
use App\Models\Patient;
use App\Models\Appointment;
use App\Models\Service;
use App\Models\ProviderAvailability;

class ClearDummyData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:dummy-data {--confirm : Confirm the deletion}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all dummy/seeded data from the database, keeping only real users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('confirm')) {
            $this->error('This command will delete all dummy data from the database.');
            $this->info('Run with --confirm flag to proceed: php artisan clear:dummy-data --confirm');
            return 1;
        }

        $this->info('Starting to clear dummy data...');

        // List of dummy emails to remove
        $dummyEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        $deletedCount = 0;

        foreach ($dummyEmails as $email) {
            $user = User::where('email', $email)->first();

            if ($user) {
                $this->info("Deleting user: {$user->name} ({$user->email})");

                // Delete related data first
                if ($user->provider) {
                    // Delete provider services
                    Service::where('provider_id', $user->provider->id)->delete();

                    // Delete provider availabilities
                    ProviderAvailability::where('provider_id', $user->provider->id)->delete();

                    // Delete provider
                    $user->provider->delete();
                }

                if ($user->patient) {
                    // Delete patient appointments
                    Appointment::where('patient_id', $user->patient->id)->delete();

                    // Delete patient
                    $user->patient->delete();
                }

                // Delete the user
                $user->delete();
                $deletedCount++;
            }
        }

        $this->info("Successfully deleted {$deletedCount} dummy users and their related data.");
        $this->info('Dummy data cleanup completed!');

        return 0;
    }
}

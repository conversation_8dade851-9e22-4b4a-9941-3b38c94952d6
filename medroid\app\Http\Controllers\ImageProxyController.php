<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ImageProxyController extends Controller
{
    public function proxy(Request $request)
    {
        // Handle CORS preflight
        if ($request->getMethod() === 'OPTIONS') {
            return response('', 200)
                ->header('Access-Control-Allow-Origin', '*')
                ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
                ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept')
                ->header('Access-Control-Max-Age', '3600');
        }
        
        $imageUrl = $request->query('url');
        
        if (!$imageUrl) {
            return response()->json(['error' => 'URL parameter is required'], 400)
                ->header('Access-Control-Allow-Origin', '*');
        }
        
        // Validate that it's a local storage URL for security
        if (!str_contains($imageUrl, '/storage/')) {
            return response()->json(['error' => 'Invalid URL'], 400)
                ->header('Access-Control-Allow-Origin', '*');
        }
        
        // Get the file path from the URL
        $path = str_replace(request()->getSchemeAndHttpHost(), '', $imageUrl);
        $fullPath = public_path($path);
        
        if (!file_exists($fullPath)) {
            return response()->json(['error' => 'File not found'], 404)
                ->header('Access-Control-Allow-Origin', '*');
        }
        
        // Get file info
        $mimeType = mime_content_type($fullPath);
        $fileSize = filesize($fullPath);
        
        // Read file content
        $fileContent = file_get_contents($fullPath);
        
        // Create response with proper CORS headers
        return response($fileContent, 200, [
            'Content-Type' => $mimeType,
            'Content-Length' => $fileSize,
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, OPTIONS',
            'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Proxy Instagram media content to avoid 403 Forbidden errors in mobile apps
     */
    public function proxyMedia(Request $request)
    {
        // Handle CORS preflight
        if ($request->getMethod() === 'OPTIONS') {
            return response('', 200)
                ->header('Access-Control-Allow-Origin', '*')
                ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
                ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization')
                ->header('Access-Control-Max-Age', '3600');
        }

        $mediaUrl = $request->query('url');
        
        if (!$mediaUrl) {
            return response()->json(['error' => 'URL parameter is required'], 400)
                ->header('Access-Control-Allow-Origin', '*');
        }

        // Validate that it's an Instagram URL for security
        $allowedDomains = [
            'scontent',
            'fbcdn.net',
            'cdninstagram.com',
            'instagram.com'
        ];

        $isValidDomain = false;
        foreach ($allowedDomains as $domain) {
            if (str_contains($mediaUrl, $domain)) {
                $isValidDomain = true;
                break;
            }
        }

        if (!$isValidDomain) {
            Log::warning('Invalid domain for media proxy: ' . $mediaUrl);
            return response()->json(['error' => 'Invalid URL domain'], 400)
                ->header('Access-Control-Allow-Origin', '*');
        }

        // Detect content type from URL to prevent video/image mismatches
        $urlLower = strtolower($mediaUrl);
        $isImageUrl = str_contains($urlLower, '.jpg') || str_contains($urlLower, '.jpeg') ||
                     str_contains($urlLower, '.png') || str_contains($urlLower, '.gif') ||
                     str_contains($urlLower, '.webp');
        $isVideoUrl = str_contains($urlLower, '.mp4') || str_contains($urlLower, '.mov') ||
                     str_contains($urlLower, '.webm') || str_contains($urlLower, '.avi');

        if ($isImageUrl) {
            Log::info('Detected image URL: ' . $mediaUrl);
        } elseif ($isVideoUrl) {
            Log::info('Detected video URL: ' . $mediaUrl);
        }

        // Check cache first to avoid repeated requests to Instagram
        $cacheKey = 'instagram_media_' . md5($mediaUrl);
        $cachedData = Cache::get($cacheKey);
        
        if ($cachedData) {
            Log::info('Serving Instagram media from cache: ' . $mediaUrl);
            return response($cachedData['content'], 200, [
                'Content-Type' => $cachedData['content_type'],
                'Content-Length' => strlen($cachedData['content']),
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, OPTIONS',
                'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept, Authorization',
                'Cache-Control' => 'public, max-age=3600',
                'X-Proxy-Source' => 'medroid-backend-cached',
            ]);
        }

        try {
            // Try multiple strategies to fetch Instagram content
            $strategies = [
                // Strategy 1: Desktop browser with Instagram referrer
                [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept' => 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.9',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Referer' => 'https://www.instagram.com/',
                    'Origin' => 'https://www.instagram.com',
                    'Sec-Fetch-Dest' => 'image',
                    'Sec-Fetch-Mode' => 'no-cors',
                    'Sec-Fetch-Site' => 'same-site',
                    'sec-ch-ua' => '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    'sec-ch-ua-mobile' => '?0',
                    'sec-ch-ua-platform' => '"Windows"',
                ],
                // Strategy 2: Mobile browser
                [
                    'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                    'Accept' => 'image/webp,image/avif,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.9',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Referer' => 'https://www.instagram.com/',
                ],
                // Strategy 3: Simple direct request
                [
                    'User-Agent' => 'Mozilla/5.0 (compatible; Medroid/1.0; +https://medroid.ai)',
                    'Accept' => '*/*',
                ],
            ];

            $response = null;
            $lastError = null;

            // Determine if this is likely a video URL
            $isVideo = str_contains($mediaUrl, '.mp4') ||
                      str_contains($mediaUrl, '.mov') ||
                      str_contains($mediaUrl, 'video') ||
                      str_contains($mediaUrl, '.m4v');

            // Use longer timeout for videos
            $timeout = $isVideo ? 45 : 15;

            foreach ($strategies as $index => $headers) {
                try {
                    Log::info("Trying strategy " . ($index + 1) . " for Instagram " . ($isVideo ? 'video' : 'media') . ": " . $mediaUrl);

                    // Add range request support for videos
                    if ($isVideo && $request->hasHeader('Range')) {
                        $headers['Range'] = $request->header('Range');
                    }

                    $response = Http::withHeaders($headers)
                        ->timeout($timeout) // Longer timeout for videos
                        ->retry($isVideo ? 1 : 2, 1000) // Less retries for videos to avoid long waits
                        ->get($mediaUrl);

                    if ($response->successful()) {
                        Log::info("Strategy " . ($index + 1) . " successful for: " . $mediaUrl);
                        break;
                    } else {
                        Log::warning("Strategy " . ($index + 1) . " failed with status " . $response->status() . " for: " . $mediaUrl);
                        $lastError = $response->status();
                    }
                } catch (\Exception $e) {
                    Log::warning("Strategy " . ($index + 1) . " threw exception: " . $e->getMessage() . " for: " . $mediaUrl);
                    $lastError = $e->getMessage();
                    continue;
                }
            }

            if (!$response || $response->failed()) {
                // Check if it's a URL signature expiration issue
                $isExpiredUrl = str_contains((string)$lastError, 'URL signature expired') || 
                               str_contains((string)$lastError, 'signature');
                
                if ($isExpiredUrl) {
                    Log::warning('Instagram URL signature expired for: ' . $mediaUrl);
                    return response()->json([
                        'error' => 'Instagram URL expired', 
                        'details' => 'Instagram media URLs have time-limited signatures that expire quickly',
                        'suggestion' => 'This is an Instagram security feature to prevent hotlinking'
                    ], 410) // 410 Gone - indicates the resource was available but is no longer
                        ->header('Access-Control-Allow-Origin', '*');
                } else {
                    Log::error('All strategies failed for Instagram media: ' . $mediaUrl . ' - Last error: ' . $lastError);
                    return response()->json([
                        'error' => 'Failed to fetch media content', 
                        'details' => 'All fetch strategies failed'
                    ], 403)
                        ->header('Access-Control-Allow-Origin', '*');
                }
            }

            // Get content type from response headers
            $contentType = $response->header('Content-Type') ?? 'image/jpeg';
            
            // Determine if it's a video or image
            if (str_contains($contentType, 'video/') || str_contains($mediaUrl, '.mp4') || str_contains($mediaUrl, '.mov')) {
                $contentType = 'video/mp4';
            } elseif (!str_contains($contentType, 'image/')) {
                $contentType = 'image/jpeg'; // Default to JPEG for images
            }

            // Get the media content
            $mediaContent = $response->body();
            $contentLength = strlen($mediaContent);

            Log::info('Successfully proxied Instagram media: ' . $mediaUrl . ' - Size: ' . $contentLength . ' bytes - Type: ' . $contentType);

            // Cache the successful result - shorter cache for videos due to URL expiration
            $cacheMinutes = str_contains($contentType, 'video/') ? 10 : 30;
            Cache::put($cacheKey, [
                'content' => $mediaContent,
                'content_type' => $contentType,
            ], now()->addMinutes($cacheMinutes));

            // Prepare response headers
            $responseHeaders = [
                'Content-Type' => $contentType,
                'Content-Length' => $contentLength,
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, OPTIONS',
                'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept, Authorization, Range',
                'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
                'X-Proxy-Source' => 'medroid-backend',
            ];

            // Add video-specific headers for better streaming support
            if (str_contains($contentType, 'video/')) {
                $responseHeaders['Accept-Ranges'] = 'bytes';
                $responseHeaders['Content-Disposition'] = 'inline';
                // Shorter cache for videos to allow for fresh requests
                $responseHeaders['Cache-Control'] = 'public, max-age=1800'; // 30 minutes
            }

            // Handle range requests for video streaming
            $statusCode = 200;
            if ($request->hasHeader('Range') && str_contains($contentType, 'video/')) {
                $statusCode = 206; // Partial Content
                $responseHeaders['Content-Range'] = $response->header('Content-Range') ?? "bytes 0-" . ($contentLength - 1) . "/" . $contentLength;
            }

            // Return the media content with proper headers
            return response($mediaContent, $statusCode, $responseHeaders);

        } catch (\Exception $e) {
            Log::error('Error proxying Instagram media: ' . $e->getMessage() . ' - URL: ' . $mediaUrl);
            return response()->json([
                'error' => 'Failed to proxy media content',
                'details' => $e->getMessage()
            ], 500)
                ->header('Access-Control-Allow-Origin', '*');
        }
    }
}

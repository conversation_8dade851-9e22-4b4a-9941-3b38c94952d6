<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use Illuminate\Support\Facades\Log;

class DashboardService
{
    /**
     * Get dashboard statistics for a user
     */
    public function getDashboardStats(?User $user): array
    {
        $stats = [
            'upcomingAppointments' => 0,
            'completedAppointments' => 0,
            'healthRecords' => 0
        ];

        try {
            if ($user && $user->role === 'patient') {
                $patient = Patient::where('user_id', $user->id)->first();
                if ($patient) {
                    $stats = $this->getPatientStats($patient);
                }
            } elseif ($user && $user->role === 'provider') {
                $provider = Provider::where('user_id', $user->id)->first();
                if ($provider) {
                    $stats = $this->getProviderStats($provider);
                }
            } elseif ($user && $user->role === 'admin') {
                $stats = $this->getAdminStats();
            }
        } catch (\Exception $e) {
            Log::warning('Dashboard stats fetch error: ' . $e->getMessage());
        }

        return $stats;
    }

    /**
     * Get recent appointments for a user
     */
    public function getRecentAppointments(?User $user, int $limit = 5): array
    {
        $appointments = [];

        try {
            if ($user && $user->role === 'patient') {
                $patient = Patient::where('user_id', $user->id)->first();
                if ($patient) {
                    $appointments = Appointment::where('patient_id', $patient->id)
                        ->with(['provider.user'])
                        ->orderBy('scheduled_at', 'desc')
                        ->limit($limit)
                        ->get()
                        ->toArray();
                }
            } elseif ($user && $user->role === 'provider') {
                $provider = Provider::where('user_id', $user->id)->first();
                if ($provider) {
                    $appointments = Appointment::where('provider_id', $provider->id)
                        ->with(['patient.user'])
                        ->orderBy('scheduled_at', 'desc')
                        ->limit($limit)
                        ->get()
                        ->toArray();
                }
            }
        } catch (\Exception $e) {
            Log::warning('Recent appointments fetch error: ' . $e->getMessage());
        }

        return $appointments;
    }

    /**
     * Get patient-specific statistics
     */
    private function getPatientStats(Patient $patient): array
    {
        return [
            'upcomingAppointments' => Appointment::where('patient_id', $patient->id)
                ->whereIn('status', ['scheduled', 'confirmed'])
                ->where('scheduled_at', '>', now())
                ->count(),
            'completedAppointments' => Appointment::where('patient_id', $patient->id)
                ->where('status', 'completed')
                ->count(),
            'healthRecords' => 0 // Placeholder for health records count
        ];
    }

    /**
     * Get provider-specific statistics
     */
    private function getProviderStats(Provider $provider): array
    {
        return [
            'upcomingAppointments' => Appointment::where('provider_id', $provider->id)
                ->whereIn('status', ['scheduled', 'confirmed'])
                ->where('scheduled_at', '>', now())
                ->count(),
            'completedAppointments' => Appointment::where('provider_id', $provider->id)
                ->where('status', 'completed')
                ->count(),
            'totalPatients' => Appointment::where('provider_id', $provider->id)
                ->distinct('patient_id')
                ->count()
        ];
    }

    /**
     * Get admin-specific statistics
     */
    private function getAdminStats(): array
    {
        try {
            // Basic counts
            $totalAppointments = Appointment::count();
            $totalPatients = Patient::count();
            $totalProviders = Provider::count();
            $totalUsers = \App\Models\User::count();

            // Appointment statistics
            $upcomingAppointments = Appointment::whereIn('status', ['scheduled', 'confirmed'])
                ->where('scheduled_at', '>', now())
                ->count();

            $completedAppointments = Appointment::where('status', 'completed')->count();
            $cancelledAppointments = Appointment::where('status', 'cancelled')->count();

            // User statistics
            $verifiedUsers = \App\Models\User::whereNotNull('email_verified_at')->count();
            $activeUsers = \App\Models\User::where('last_activity_at', '>=', now()->subDays(30))->count();

            // Growth statistics (last 30 days)
            $newUsersThisMonth = \App\Models\User::where('created_at', '>=', now()->subDays(30))->count();
            $newAppointmentsThisMonth = Appointment::where('created_at', '>=', now()->subDays(30))->count();

            // Revenue statistics (if payment table exists)
            $totalRevenue = 0;
            $monthlyRevenue = 0;
            try {
                if (\Schema::hasTable('payment_history')) {
                    $totalRevenue = \DB::table('payment_history')
                        ->where('status', 'completed')
                        ->sum('amount') / 100; // Convert from cents to dollars

                    $monthlyRevenue = \DB::table('payment_history')
                        ->where('status', 'completed')
                        ->where('paid_at', '>=', now()->subDays(30))
                        ->sum('amount') / 100;
                }
            } catch (\Exception $e) {
                // Payment table might not exist, continue with 0 values
            }

            return [
                // Core statistics
                'totalAppointments' => $totalAppointments,
                'totalPatients' => $totalPatients,
                'totalProviders' => $totalProviders,
                'totalUsers' => $totalUsers,

                // Appointment statistics
                'upcomingAppointments' => $upcomingAppointments,
                'completedAppointments' => $completedAppointments,
                'cancelledAppointments' => $cancelledAppointments,

                // User statistics
                'verifiedUsers' => $verifiedUsers,
                'activeUsers' => $activeUsers,

                // Growth statistics
                'newUsersThisMonth' => $newUsersThisMonth,
                'newAppointmentsThisMonth' => $newAppointmentsThisMonth,

                // Revenue statistics
                'totalRevenue' => round($totalRevenue, 2),
                'monthlyRevenue' => round($monthlyRevenue, 2),

                // Calculated rates
                'appointmentCompletionRate' => $totalAppointments > 0
                    ? round(($completedAppointments / $totalAppointments) * 100, 1)
                    : 0,
                'userVerificationRate' => $totalUsers > 0
                    ? round(($verifiedUsers / $totalUsers) * 100, 1)
                    : 0,
            ];
        } catch (\Exception $e) {
            Log::error('Error getting admin stats: ' . $e->getMessage());

            // Return basic fallback stats
            return [
                'totalAppointments' => 0,
                'totalPatients' => 0,
                'totalProviders' => 0,
                'totalUsers' => 0,
                'upcomingAppointments' => 0,
                'completedAppointments' => 0,
                'cancelledAppointments' => 0,
                'verifiedUsers' => 0,
                'activeUsers' => 0,
                'newUsersThisMonth' => 0,
                'newAppointmentsThisMonth' => 0,
                'totalRevenue' => 0,
                'monthlyRevenue' => 0,
                'appointmentCompletionRate' => 0,
                'userVerificationRate' => 0,
            ];
        }
    }

    /**
     * Get quick actions based on user role
     */
    public function getQuickActions(?User $user): array
    {
        $actions = [];

        if ($user && $user->role === 'patient') {
            $actions = [
                ['title' => 'AI Chat', 'href' => '/chat', 'icon' => 'chat'],
                ['title' => 'Find Doctors', 'href' => '/providers', 'icon' => 'users'],
                ['title' => 'Book Appointment', 'href' => '/appointments/create', 'icon' => 'calendar'],
                ['title' => 'Settings', 'href' => '/settings/profile', 'icon' => 'settings']
            ];
        } elseif ($user && $user->role === 'provider') {
            $actions = [
                ['title' => 'My Schedule', 'href' => '/provider/schedule', 'icon' => 'calendar'],
                ['title' => 'Patient List', 'href' => '/provider/patients', 'icon' => 'users'],
                ['title' => 'Availability', 'href' => '/provider/availability', 'icon' => 'clock'],
                ['title' => 'Profile', 'href' => '/settings/profile', 'icon' => 'user']
            ];
        } elseif ($user && $user->role === 'admin') {
            $actions = [
                ['title' => 'User Management', 'href' => '/users', 'icon' => 'users'],
                ['title' => 'Appointments', 'href' => '/appointments', 'icon' => 'calendar'],
                ['title' => 'Analytics', 'href' => '/analytics', 'icon' => 'chart'],
                ['title' => 'Settings', 'href' => '/settings', 'icon' => 'settings']
            ];
        }

        return $actions;
    }
}

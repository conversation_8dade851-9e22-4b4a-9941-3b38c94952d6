<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use App\Services\MediaCleanupService;

class SocialMediaPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'instagram_account_id',
        'platform',
        'platform_post_id',
        'media_type',
        'media_url',
        'thumbnail_url',
        'caption',
        'permalink',
        'posted_at',
        'like_count',
        'comment_count',
        'is_visible',
    ];

    protected $casts = [
        'posted_at' => 'datetime',
        'like_count' => 'integer',
        'comment_count' => 'integer',
        'is_visible' => 'boolean',
    ];

    protected static function booted()
    {
        static::deleting(function ($post) {
            // Clean up associated media files when post is deleted
            $post->cleanupMediaFiles();
        });
    }

    /**
     * Get the user that owns the social media post
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the Instagram account that this post belongs to
     */
    public function instagramAccount(): BelongsTo
    {
        return $this->belongsTo(InstagramAccount::class);
    }

    /**
     * Scope to get visible posts
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * Scope to get posts by platform
     */
    public function scopePlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * Scope to get Instagram posts
     */
    public function scopeInstagram($query)
    {
        return $query->where('platform', 'instagram');
    }

    /**
     * Scope to get recent posts
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('posted_at', '>=', now()->subDays($days));
    }

    /**
     * Get the display media URL (for videos use thumbnail, for images use media_url)
     */
    public function getDisplayMediaUrlAttribute(): ?string
    {
        // For videos, prefer thumbnail_url for display, fallback to media_url
        if ($this->isVideo()) {
            return $this->thumbnail_url ?: $this->media_url;
        }

        // For images, prefer media_url, fallback to thumbnail_url
        return $this->media_url ?: $this->thumbnail_url;
    }

    /**
     * Get the video URL for playback (only for videos)
     */
    public function getVideoUrlAttribute(): ?string
    {
        return $this->isVideo() ? $this->media_url : null;
    }

    /**
     * Check if this is a video post
     */
    public function isVideo(): bool
    {
        return in_array($this->media_type, ['VIDEO', 'REELS']);
    }

    /**
     * Check if this is an image post
     */
    public function isImage(): bool
    {
        return in_array($this->media_type, ['IMAGE', 'CAROUSEL_ALBUM']);
    }

    /**
     * Clean up associated media files
     */
    public function cleanupMediaFiles(): int
    {
        return MediaCleanupService::cleanupModelMedia($this, [
            'media_url',
            'thumbnail_url'
        ]);
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Jobs\GenerateBotPostJob;

class TestBotPostGeneration extends Command
{
    protected $signature = 'bot:test-post {persona_id=4}';
    protected $description = 'Test complete bot post generation pipeline';

    public function handle()
    {
        $this->info('🚀 Testing Bot Post Generation Pipeline...');
        $this->info(str_repeat('=', 50));

        // Get persona
        $persona = BotPersona::find($this->argument('persona_id'));
        if (!$persona) {
            $this->error('❌ Persona not found!');
            return 1;
        }

        $this->info("👤 Testing with: {$persona->first_name} {$persona->last_name}");

        // Get bot user ID
        $botUserId = $persona->botUser ? $persona->botUser->id : null;
        if (!$botUserId) {
            $this->error('❌ No bot user found!');
            return 1;
        }

        $this->info("🤖 Bot User ID: {$botUserId}");

        try {
            // Create AutomatedPost
            $automatedPost = AutomatedPost::create([
                'bot_persona_id' => $persona->id,
                'bot_user_id' => $botUserId,
                'status' => 'draft',
                'caption' => 'Generating content...',
                'hashtags' => json_encode([]),
                'generation_prompts' => [
                    'initiated_by' => 'test_command',
                    'initiated_at' => now()->toISOString(),
                    'immediate_posting' => true,
                ],
            ]);

            $this->info("✅ AutomatedPost created: ID {$automatedPost->id}");

            // Dispatch job
            GenerateBotPostJob::dispatch($persona, $automatedPost->id, true);
            $this->info('✅ Job dispatched');

            $this->info('');
            $this->info('🎯 Next Steps:');
            $this->info('1. Run: php artisan queue:work --once');
            $this->info('2. Check post status in dashboard');
            $this->info('3. Post should process through: draft → generating_image → ready → posted');

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Error: {$e->getMessage()}");
            return 1;
        }
    }
}

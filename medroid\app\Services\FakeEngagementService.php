<?php

namespace App\Services;

use App\Models\SocialContent;
use App\Models\SocialContentComment;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class FakeEngagementService
{
    /**
     * Add fake engagement metrics to a social content post
     */
    public function addFakeEngagement(SocialContent $socialContent): void
    {
        try {
            // Generate realistic engagement metrics
            $metrics = $this->generateEngagementMetrics($socialContent);
            
            // Update the social content with engagement metrics
            $socialContent->update([
                'engagement_metrics' => $metrics
            ]);
            
            // Add fake comments
            $this->addFakeComments($socialContent, $metrics['comments']);
            
            Log::info('Added fake engagement to social content', [
                'content_id' => $socialContent->id,
                'likes' => $metrics['likes'],
                'comments' => $metrics['comments'],
                'shares' => $metrics['shares'],
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to add fake engagement', [
                'content_id' => $socialContent->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
    
    /**
     * Generate realistic engagement metrics based on content type and source
     */
    private function generateEngagementMetrics(SocialContent $socialContent): array
    {
        $baseMetrics = $this->getBaseMetricsForSource($socialContent->source);
        
        // Add some randomness to make it more realistic
        $likes = $this->addRandomVariation($baseMetrics['likes'], 0.3);
        $comments = $this->addRandomVariation($baseMetrics['comments'], 0.4);
        $shares = $this->addRandomVariation($baseMetrics['shares'], 0.5);
        $views = $this->addRandomVariation($baseMetrics['views'], 0.2);
        
        return [
            'likes' => $likes,
            'comments' => $comments,
            'shares' => $shares,
            'views' => $views,
            'engagement_rate' => round((($likes + $comments + $shares) / $views) * 100, 2),
            'generated_at' => now()->toISOString(),
        ];
    }
    
    /**
     * Get base metrics based on content source
     */
    private function getBaseMetricsForSource(string $source): array
    {
        switch ($source) {
            case 'bot_generated':
                return [
                    'likes' => rand(15, 45),
                    'comments' => rand(3, 8),
                    'shares' => rand(1, 5),
                    'views' => rand(150, 400),
                ];
            case 'internal':
                return [
                    'likes' => rand(20, 60),
                    'comments' => rand(4, 12),
                    'shares' => rand(2, 8),
                    'views' => rand(200, 500),
                ];
            case 'instagram':
                return [
                    'likes' => rand(50, 150),
                    'comments' => rand(8, 20),
                    'shares' => rand(5, 15),
                    'views' => rand(500, 1200),
                ];
            default:
                return [
                    'likes' => rand(10, 30),
                    'comments' => rand(2, 6),
                    'shares' => rand(1, 3),
                    'views' => rand(100, 250),
                ];
        }
    }
    
    /**
     * Add random variation to a base number
     */
    private function addRandomVariation(int $base, float $variationPercent): int
    {
        $variation = $base * $variationPercent;
        $min = max(1, $base - $variation);
        $max = $base + $variation;
        
        return rand((int)$min, (int)$max);
    }
    
    /**
     * Add fake comments to the social content
     */
    private function addFakeComments(SocialContent $socialContent, int $commentCount): void
    {
        // Get some random users to use as commenters (or create fake ones)
        $commenters = $this->getFakeCommenters();
        
        $comments = $this->generateHealthComments($commentCount);
        
        foreach ($comments as $index => $commentText) {
            $commenter = $commenters[array_rand($commenters)];
            
            SocialContentComment::create([
                'social_content_id' => $socialContent->id,
                'user_id' => $commenter['id'],
                'content' => $commentText,
                'created_at' => now()->subMinutes(rand(1, 120)), // Random time in last 2 hours
            ]);
        }
    }
    
    /**
     * Get fake commenters (create if needed)
     */
    private function getFakeCommenters(): array
    {
        // Try to get existing users first
        $existingUsers = User::where('email', 'like', 'fake_commenter_%')->limit(10)->get();
        
        if ($existingUsers->count() >= 5) {
            return $existingUsers->map(function($user) {
                return ['id' => $user->id, 'name' => $user->name];
            })->toArray();
        }
        
        // Create fake commenters if not enough exist
        $fakeCommenters = [];
        $names = [
            'Sarah Johnson', 'Mike Chen', 'Emma Wilson', 'David Rodriguez', 'Lisa Thompson',
            'Alex Kim', 'Rachel Green', 'Tom Anderson', 'Maya Patel', 'Chris Martinez',
            'Jessica Lee', 'Ryan Taylor', 'Amanda Brown', 'Kevin Wong', 'Nicole Davis'
        ];
        
        foreach (array_slice($names, 0, 10) as $index => $name) {
            $email = 'fake_commenter_' . ($index + 1) . '@example.com';
            
            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => $name,
                    'password' => bcrypt('fake_password'),
                    'email_verified_at' => now(),
                ]
            );
            
            $fakeCommenters[] = ['id' => $user->id, 'name' => $user->name];
        }
        
        return $fakeCommenters;
    }
    
    /**
     * Generate realistic health-focused comments
     */
    private function generateHealthComments(int $count): array
    {
        $healthComments = [
            "This is so helpful! Thank you for sharing 🙏",
            "Love this advice! Definitely trying this today 💪",
            "Great tips! I've been looking for something like this",
            "Thank you for the motivation! Just what I needed 🌟",
            "This really resonates with me. Thanks for posting!",
            "Such valuable information! Bookmarking this 📖",
            "You always share the best content! 👏",
            "This is exactly what I needed to hear today",
            "Amazing post! Can't wait to try this out",
            "So inspiring! Thank you for the reminder 💚",
            "Perfect timing for this post! Thank you 🙌",
            "This is gold! Sharing with my friends",
            "Love your approach to wellness! So practical",
            "Thank you for making health simple and accessible",
            "Your posts always brighten my day! ☀️",
            "This is why I follow you! Great content as always",
            "Such a good reminder. Thank you! 🤗",
            "Needed this motivation today. Thank you!",
            "Your content is always so thoughtful and helpful",
            "This speaks to me! Thank you for sharing your wisdom"
        ];
        
        // Randomly select comments
        $selectedComments = [];
        for ($i = 0; $i < $count; $i++) {
            $selectedComments[] = $healthComments[array_rand($healthComments)];
        }
        
        return $selectedComments;
    }
    
    /**
     * Add engagement to all recent bot posts that don't have it
     */
    public function addEngagementToRecentBotPosts(): int
    {
        $recentBotPosts = SocialContent::where('source', 'bot_generated')
            ->whereDate('created_at', '>=', now()->subDays(7))
            ->whereNull('engagement_metrics')
            ->get();
        
        $count = 0;
        foreach ($recentBotPosts as $post) {
            $this->addFakeEngagement($post);
            $count++;
        }
        
        Log::info('Added engagement to recent bot posts', ['count' => $count]);
        
        return $count;
    }
}

<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class ConfirmablePasswordController extends Controller
{
    /**
     * Show the confirm password page.
     */
    public function show(): Response
    {
        return Inertia::render('auth/ConfirmPassword');
    }

    /**
     * Confirm the user's password.
     */
    public function store(Request $request): RedirectResponse
    {
        if (! Auth::guard('web')->validate([
            'email' => $request->user()->email,
            'password' => $request->password,
        ])) {
            throw ValidationException::withMessages([
                'password' => __('auth.password'),
            ]);
        }

        $request->session()->put('auth.password_confirmed_at', time());

        // Role-based redirect after password confirmation
        $user = $request->user();
        $redirectRoute = $this->getRoleBasedRedirectRoute($user);
        return redirect()->intended(route($redirectRoute, absolute: false));
    }

    /**
     * Get the appropriate redirect route based on user role
     */
    private function getRoleBasedRedirectRoute($user): string
    {
        switch ($user->role) {
            case 'patient':
                return 'chat';
            case 'provider':
                return 'provider.schedule';
            case 'admin':
            case 'super_admin':
                return 'dashboard';
            default:
                return 'dashboard';
        }
    }
}

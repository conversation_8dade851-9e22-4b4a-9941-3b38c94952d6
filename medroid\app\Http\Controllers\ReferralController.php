<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Referral;
use App\Services\ReferralService;
use App\Services\UserActivityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ReferralController extends Controller
{
    protected $referralService;
    protected $userActivityService;

    public function __construct(ReferralService $referralService, UserActivityService $userActivityService)
    {
        $this->referralService = $referralService;
        $this->userActivityService = $userActivityService;
    }

    /**
     * Get the current user's referral code
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReferralCode(Request $request)
    {
        $user = Auth::user();

        // Generate a referral code if the user doesn't have one
        if (!$user->referral_code) {
            $referralCode = $this->referralService->generateReferralCode($user);
        } else {
            $referralCode = $user->referral_code;
        }

        return response()->json([
            'referral_code' => $referralCode,
            'referral_url' => config('app.url') . '/register?ref=' . $referralCode,
        ]);
    }

    /**
     * Create a new referral by sending an invitation
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createReferral(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $email = $request->input('email');

        // Check if the email is already registered
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            return response()->json([
                'message' => 'This email is already registered with Medroid.',
            ], 422);
        }

        try {
            // Ensure user has a referral code
            if (!$user->referral_code) {
                $this->referralService->generateReferralCode($user);
                $user->refresh(); // Refresh to get the updated referral code
            }

            $referral = $this->referralService->createReferral($user, $email);

            // Send invitation email to the referred email
            try {
                $this->referralService->sendReferralInvitation($user, $email, $user->referral_code);
                $emailStatus = 'sent';
                $message = 'Referral invitation sent successfully.';
            } catch (\Exception $emailError) {
                Log::warning('Referral created but email failed to send', [
                    'user_id' => $user->id,
                    'email' => $email,
                    'error' => $emailError->getMessage()
                ]);
                $emailStatus = 'failed';
                $message = 'Referral created but email failed to send. Please try again.';
            }

            return response()->json([
                'message' => $message,
                'referral' => $referral,
                'email_status' => $emailStatus,
            ]);
        } catch (\Exception $e) {
            Log::error("Error creating referral: " . $e->getMessage(), [
                'user_id' => $user->id,
                'email' => $email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'message' => 'Failed to create referral. Please try again.',
            ], 500);
        }
    }

    /**
     * Get all referrals for the current user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserReferrals(Request $request)
    {
        $user = Auth::user();
        $referrals = $this->referralService->getUserReferrals($user);

        return response()->json([
            'referrals' => $referrals,
            'total_count' => $referrals->count(),
            'completed_count' => $referrals->where('status', 'completed')->count(),
            'pending_count' => $referrals->where('status', 'pending')->count(),
        ]);
    }

    /**
     * Get all referrals (for admin dashboard)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view all referrals
        if (!Auth::user()->can('view referrals')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return $this->getAllReferrals($request);
    }

    /**
     * Get referral statistics (for admin dashboard)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        // Check if user has permission to view referral stats
        if (!Auth::user()->can('view referrals')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return $this->getReferralStats($request);
    }

    /**
     * Apply a referral code during registration
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function applyReferralCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'referral_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $referralCode = $request->input('referral_code');
        $referrer = User::where('referral_code', $referralCode)->first();

        if (!$referrer) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid referral code.',
            ]);
        }

        return response()->json([
            'valid' => true,
            'referrer_name' => $referrer->name,
            'message' => 'Valid referral code.',
        ]);
    }

    /**
     * Get all referrals (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllReferrals(Request $request)
    {
        // Check if user has permission to view all referrals
        if (!Auth::user()->can('view referrals')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = Referral::with(['referrer', 'referred']);

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [
                $request->input('start_date'),
                $request->input('end_date'),
            ]);
        }

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $referrals = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Add activity days for each referred user and ensure email is shown
        foreach ($referrals as $referral) {
            if ($referral->referred) {
                $activityDays = $this->userActivityService->getUserActivityStreak($referral->referred);
                $referral->referred->activity_days = $activityDays;
            }

            // Always set referred_email for display purposes
            $referral->referred_email = $referral->referred ? $referral->referred->email : $referral->email;
        }

        return response()->json($referrals);
    }

    /**
     * Get referral statistics (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReferralStats(Request $request)
    {
        // Check if user has permission to view referral stats
        if (!Auth::user()->can('view referrals')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get date range filters if provided
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Base query
        $query = Referral::query();

        // Apply date filters if provided
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Get total referrals count
        $totalReferrals = $query->count();

        // Get completed referrals count
        $completedReferrals = (clone $query)->where('status', 'completed')->count();

        // Get pending referrals count
        $pendingReferrals = (clone $query)->where('status', 'pending')->count();

        // Get total credits awarded
        $totalCreditsAwarded = (clone $query)
            ->where('status', 'completed')
            ->where('credit_awarded', true)
            ->sum('credit_amount') ?? 0;

        // Get top referrers
        $topReferrers = User::select('users.id', 'users.name', 'users.email')
            ->selectRaw('COUNT(referrals.id) as total_referrals')
            ->selectRaw('SUM(CASE WHEN referrals.status = "completed" THEN 1 ELSE 0 END) as completed_referrals')
            ->selectRaw('SUM(CASE WHEN referrals.credit_awarded = 1 THEN referrals.credit_amount ELSE 0 END) as total_credits')
            ->join('referrals', 'users.id', '=', 'referrals.referrer_id')
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderByRaw('COUNT(referrals.id) DESC')
            ->limit(10)
            ->get();

        // Get monthly stats for the last 12 months
        $monthlyStats = Referral::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month')
            ->selectRaw('COUNT(*) as total')
            ->selectRaw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed')
            ->selectRaw('SUM(CASE WHEN credit_awarded = 1 THEN credit_amount ELSE 0 END) as credits_awarded')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return response()->json([
            'total_referrals' => $totalReferrals,
            'completed_referrals' => $completedReferrals,
            'pending_referrals' => $pendingReferrals,
            'total_credits_awarded' => (float) $totalCreditsAwarded,
            'top_referrers' => $topReferrers,
            'monthly_stats' => $monthlyStats,
        ]);
    }
}

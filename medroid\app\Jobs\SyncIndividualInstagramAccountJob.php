<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\InstagramAccount;
use App\Services\InstagramService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SyncIndividualInstagramAccountJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * The Instagram account to sync.
     *
     * @var InstagramAccount
     */
    protected $instagramAccount;

    /**
     * The user ID for progress tracking.
     *
     * @var int
     */
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(InstagramAccount $instagramAccount, $userId = null)
    {
        $this->instagramAccount = $instagramAccount;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting individual Instagram account sync job', [
            'account_id' => $this->instagramAccount->id,
            'username' => $this->instagramAccount->username,
            'user_id' => $this->userId
        ]);

        $cacheKey = null;
        if ($this->userId) {
            $cacheKey = "instagram_progress_{$this->userId}";
            
            // Update progress to show background processing started
            Cache::put($cacheKey, [
                'status' => 'processing',
                'step' => 'starting_background_sync',
                'progress' => 60,
                'message' => 'Starting content import in background...',
                'imported_count' => 0
            ], 300);
        }

        try {
            $instagramService = new InstagramService();

            // Sync content for this specific account
            $importedCount = $instagramService->syncAccountContent($this->instagramAccount, $this->userId);

            // Update last sync time
            $this->instagramAccount->update(['last_sync_at' => now()]);

            // Set a flag for frontend to detect feed changes
            if ($this->userId) {
                Cache::put("user_feed_changed_{$this->userId}", true, 300); // 5 minutes
            }

            // Final progress update
            if ($cacheKey) {
                Cache::put($cacheKey, [
                    'status' => 'completed',
                    'step' => 'completed',
                    'progress' => 100,
                    'message' => "Instagram account connected successfully! Imported {$importedCount} health-related posts.",
                    'imported_count' => $importedCount
                ], 120); // Keep for 2 minutes
            }

            Log::info('Successfully synced individual Instagram account', [
                'account_id' => $this->instagramAccount->id,
                'username' => $this->instagramAccount->username,
                'imported_count' => $importedCount,
                'user_id' => $this->userId
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to sync individual Instagram account', [
                'account_id' => $this->instagramAccount->id,
                'username' => $this->instagramAccount->username,
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Update progress with error
            if ($cacheKey) {
                Cache::put($cacheKey, [
                    'status' => 'error',
                    'step' => 'sync_failed',
                    'progress' => 0,
                    'message' => 'Failed to import Instagram content: ' . $e->getMessage(),
                    'imported_count' => 0
                ], 60);
            }

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Individual Instagram account sync job failed permanently', [
            'account_id' => $this->instagramAccount->id,
            'username' => $this->instagramAccount->username,
            'user_id' => $this->userId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Update progress with permanent failure
        if ($this->userId) {
            $cacheKey = "instagram_progress_{$this->userId}";
            Cache::put($cacheKey, [
                'status' => 'failed',
                'step' => 'permanently_failed',
                'progress' => 0,
                'message' => 'Failed to import Instagram content after multiple attempts. Please try reconnecting your account.',
                'imported_count' => 0
            ], 300);
        }
    }
}
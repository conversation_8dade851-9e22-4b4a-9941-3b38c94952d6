<?php

namespace App\Console\Commands;

use App\Models\File;
use App\Services\FileAttachmentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupUnusedFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'files:cleanup
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--older-than=30 : Delete files older than X days}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up unused files from the file management system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $olderThanDays = (int) $this->option('older-than');

        $this->info('Starting file cleanup...');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will be actually deleted');
        }

        // Find unused files
        $unusedFiles = File::whereDoesntHave('usages')
                          ->where('created_at', '<', now()->subDays($olderThanDays))
                          ->get();

        if ($unusedFiles->isEmpty()) {
            $this->info('No unused files found.');
            return;
        }

        $this->info("Found {$unusedFiles->count()} unused files older than {$olderThanDays} days.");

        $totalSize = 0;
        $deletedCount = 0;

        foreach ($unusedFiles as $file) {
            $totalSize += $file->size;

            $this->line("- {$file->name} ({$file->size_formatted}) - {$file->created_at->diffForHumans()}");

            if (!$dryRun) {
                try {
                    // Delete physical file
                    if (Storage::disk($file->disk)->exists($file->path)) {
                        Storage::disk($file->disk)->delete($file->path);
                    }

                    // Delete database record
                    $file->delete();
                    $deletedCount++;
                } catch (\Exception $e) {
                    $this->error("Failed to delete {$file->name}: " . $e->getMessage());
                }
            }
        }

        if ($dryRun) {
            $this->info("Would delete {$unusedFiles->count()} files, freeing up " . $this->formatBytes($totalSize));
        } else {
            $this->info("Deleted {$deletedCount} files, freed up " . $this->formatBytes($totalSize));
        }

        // Clean up orphaned file usages
        $fileAttachmentService = app(FileAttachmentService::class);
        $orphanedCount = $fileAttachmentService->cleanupOrphanedUsages();

        if ($orphanedCount > 0) {
            $this->info("Cleaned up {$orphanedCount} orphaned file usage records.");
        }

        $this->info('File cleanup completed.');
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

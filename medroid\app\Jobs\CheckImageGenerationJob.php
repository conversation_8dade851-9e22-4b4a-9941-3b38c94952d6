<?php

namespace App\Jobs;

use App\Models\ImageGenerationLog;
use App\Services\MidjourneyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * DEPRECATED: This job is no longer needed since Google Imagen returns images synchronously.
 * Keeping for backward compatibility with existing queued jobs.
 */
class CheckImageGenerationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 10;
    public int $timeout = 60;

    private ImageGenerationLog $log;

    /**
     * Create a new job instance.
     */
    public function __construct(ImageGenerationLog $log)
    {
        $this->log = $log;
    }

    /**
     * Execute the job.
     */
    public function handle(MidjourneyService $midjourneyService): void
    {
        try {
            // Refresh the model to get latest data
            $this->log->refresh();

            // Skip if already completed or failed
            if (!$this->log->isInProgress()) {
                Log::info('Image generation no longer in progress', [
                    'log_id' => $this->log->id,
                    'status' => $this->log->status,
                ]);
                return;
            }

            Log::info('Checking image generation status', [
                'log_id' => $this->log->id,
                'task_id' => $this->log->midjourney_task_id,
                'attempt' => $this->attempts(),
            ]);

            // Check the status with Midjourney API
            $completed = $midjourneyService->processCompletedGeneration($this->log);

            if (!$completed && $this->log->isInProgress()) {
                // Still in progress, schedule another check
                $delay = $this->calculateDelay();
                
                Log::info('Image still generating, scheduling next check', [
                    'log_id' => $this->log->id,
                    'next_check_in_minutes' => $delay,
                ]);

                self::dispatch($this->log)->delay(now()->addMinutes($delay));
            } else {
                Log::info('Image generation check completed', [
                    'log_id' => $this->log->id,
                    'status' => $this->log->status,
                    'completed' => $completed,
                ]);

                // If image is ready, schedule the post for publishing
                if ($completed && $this->log->isComplete()) {
                    $post = $this->log->automatedPost;
                    if ($post && $post->status === 'ready') {
                        PublishBotPostJob::dispatch($post)->delay(now()->addMinutes(5));
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Error in CheckImageGenerationJob', [
                'log_id' => $this->log->id,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
            ]);

            // If we've tried too many times, mark as failed
            if ($this->attempts() >= $this->tries) {
                $this->log->markAsFailed('Max check attempts reached: ' . $e->getMessage());
                $this->log->automatedPost->markAsFailed('Image generation check failed after max attempts');
            } else {
                // Retry with exponential backoff
                $delay = min(30, pow(2, $this->attempts()));
                self::dispatch($this->log)->delay(now()->addMinutes($delay));
            }
        }
    }

    /**
     * Calculate delay for next check based on attempt number
     */
    private function calculateDelay(): int
    {
        $attempt = $this->attempts();
        
        // Progressive delays: 2, 3, 5, 8, 10, 15, 20, 25, 30 minutes
        $delays = [2, 3, 5, 8, 10, 15, 20, 25, 30];
        
        return $delays[min($attempt - 1, count($delays) - 1)];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('CheckImageGenerationJob failed permanently', [
            'log_id' => $this->log->id,
            'task_id' => $this->log->midjourney_task_id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Mark the generation as failed
        $this->log->markAsFailed('Job failed after max attempts: ' . $exception->getMessage());
        
        // Mark the associated post as failed
        if ($this->log->automatedPost) {
            $this->log->automatedPost->markAsFailed('Image generation failed: ' . $exception->getMessage());
        }
    }
}

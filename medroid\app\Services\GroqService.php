<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GroqService extends BaseChatService
{
    /**
     * Initialize Groq service configuration
     */
    protected function initializeService()
    {
        $this->serviceName = 'Groq';
        $this->apiKey = config('services.groq.api_key');
        $this->apiUrl = config('services.groq.api_url');
        $this->model = config('services.groq.model');
    }

    /**
     * Make API request to Groq
     */
    protected function makeApiRequest($payload, $timeout = 60)
    {
        return Http::withHeaders([
            'Authorization' => "Bearer {$this->apiKey}",
            'Content-Type' => 'application/json',
        ])->timeout($timeout)
        ->post($this->apiUrl, $payload);
    }
}
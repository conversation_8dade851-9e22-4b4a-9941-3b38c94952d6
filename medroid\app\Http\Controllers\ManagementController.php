<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use Inertia\Response;

class ManagementController extends Controller
{
    /**
     * Display the users management page.
     */
    public function users(): Response
    {
        return Inertia::render('Users');
    }

    /**
     * Display the patients management page.
     */
    public function patients(): Response
    {
        return Inertia::render('Patients');
    }

    /**
     * Display the appointments management page.
     */
    public function appointments(): Response
    {
        return Inertia::render('Appointments');
    }

    /**
     * Display the payments management page.
     */
    public function payments(): Response
    {
        return Inertia::render('Payments');
    }

    /**
     * Display the chats management page.
     */
    public function chats(): Response
    {
        return Inertia::render('Chats');
    }

    /**
     * Display the permissions management page.
     */
    public function permissions(): Response
    {
        return Inertia::render('Permissions');
    }

    /**
     * Display the email templates management page.
     */
    public function emailTemplates(): Response
    {
        return Inertia::render('EmailTemplates');
    }

    /**
     * Display the notifications management page.
     */
    public function notifications(): Response
    {
        return Inertia::render('Notifications');
    }

    /**
     * Display the services management page.
     */
    public function services(): Response
    {
        return Inertia::render('Services');
    }

    /**
     * Display the system verification page.
     */
    public function systemVerification(): Response
    {
        return Inertia::render('SystemVerification');
    }

    /**
     * Display the referrals management page.
     */
    public function referrals(): Response
    {
        return Inertia::render('Referrals');
    }

    /**
     * Display the credits management page.
     */
    public function credits(): Response
    {
        return Inertia::render('Credits');
    }

    /**
     * Display the clubs management page.
     */
    public function clubs(): Response
    {
        return Inertia::render('Clubs');
    }

    /**
     * Display the providers management page.
     */
    public function providers(): Response
    {
        return Inertia::render('Providers');
    }

    /**
     * Display the clinics management page.
     */
    public function clinics(): Response
    {
        return Inertia::render('Clinics');
    }
}

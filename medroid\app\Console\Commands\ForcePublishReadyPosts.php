<?php

namespace App\Console\Commands;

use App\Models\AutomatedPost;
use App\Jobs\PublishBotPostJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ForcePublishReadyPosts extends Command
{
    protected $signature = 'bot:force-publish {--limit=10 : Maximum number of posts to publish} {--all : Publish all ready posts}';
    protected $description = 'Force publish ready bot posts to accelerate high-frequency posting';

    public function handle()
    {
        $this->info('🚀 Force publishing ready bot posts...');
        
        $limit = $this->option('all') ? null : (int)$this->option('limit');
        
        $query = AutomatedPost::where('status', 'ready')
            ->with('botPersona')
            ->orderBy('created_at', 'asc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        $readyPosts = $query->get();
        
        if ($readyPosts->isEmpty()) {
            $this->info('No ready posts found to publish.');
            return 0;
        }
        
        $this->info("Found {$readyPosts->count()} ready posts to publish");
        
        $published = 0;
        $skipped = 0;
        
        foreach ($readyPosts as $post) {
            if ($this->shouldPublishPost($post)) {
                // Update scheduled time to now
                $post->update(['scheduled_at' => now()]);
                
                // Dispatch publish job
                PublishBotPostJob::dispatch($post);
                
                $this->line("✅ Queued for publishing: {$post->botPersona->full_name} (Post #{$post->id})");
                $published++;
                
                Log::info('Force published ready post', [
                    'post_id' => $post->id,
                    'persona_name' => $post->botPersona->full_name,
                ]);
            } else {
                $this->line("⏭️  Skipped: {$post->botPersona->full_name} (rate limited)");
                $skipped++;
            }
        }
        
        $this->newLine();
        $this->info("🎯 FORCE PUBLISH COMPLETE:");
        $this->info("   ✅ Queued for publishing: {$published} posts");
        $this->info("   ⏭️  Skipped (rate limited): {$skipped} posts");
        
        if ($published > 0) {
            $this->info("📋 Posts are now in the queue. Run 'php artisan queue:work' to process them.");
        }
        
        return 0;
    }
    
    /**
     * Check if a post should be published (respecting rate limits)
     */
    private function shouldPublishPost(AutomatedPost $post): bool
    {
        $botUser = $post->botPersona->botUser;
        
        if (!$botUser) {
            return false;
        }
        
        // Check daily limit (4 posts per day)
        $todayPosts = $botUser->automatedPosts()
            ->where('posted_at', '>=', now()->startOfDay())
            ->where('status', 'posted')
            ->count();
            
        if ($todayPosts >= 4) {
            return false;
        }
        
        // Check minimum time between posts (6 hours for 4 posts per day)
        if ($botUser->last_posted_at && $botUser->last_posted_at->gt(now()->subHours(6))) {
            return false;
        }
        
        return true;
    }
}

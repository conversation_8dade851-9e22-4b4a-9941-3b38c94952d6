<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\ImageGenerationLog;
use App\Models\AutomatedPost;

class MidjourneyService
{
    private ?string $apiKey;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = env('PIAPI_API_KEY');
        $this->baseUrl = env('PIAPI_API_URL', 'https://api.piapi.ai');
    }

    /**
     * Generate an image using PiAPI Midjourney API (Two-step process for single image)
     */
    public function generateImage(string $prompt, AutomatedPost $post): ?ImageGenerationLog
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('PiAPI API key not configured');
                return null;
            }

            // Create generation log
            $log = ImageGenerationLog::create([
                'automated_post_id' => $post->id,
                'midjourney_task_id' => '', // Will be updated after API call
                'prompt' => $prompt,
                'status' => 'pending',
            ]);

            // Step 1: Generate 4-image grid using /imagine
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post("{$this->baseUrl}/api/v1/task", [
                'model' => 'midjourney',
                'task_type' => 'imagine',
                'input' => [
                    'prompt' => $prompt,
                    'aspect_ratio' => '1:1', // Square format for social media
                    'process_mode' => 'fast', // fast, relax, or turbo
                    'skip_prompt_check' => false,
                ],
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // PiAPI returns task_id in data.task_id
                $taskId = $data['data']['task_id'] ?? null;

                if ($taskId) {
                    $log->update([
                        'midjourney_task_id' => $taskId,
                        'status' => 'processing',
                        'started_at' => now(),
                        'response_data' => $data,
                        'needs_upscale' => true, // Flag to indicate we need to upscale later
                    ]);

                    // Update the post with the task ID
                    $post->update(['midjourney_task_id' => $taskId]);

                    Log::info('PiAPI Midjourney image generation started (Step 1: Imagine)', [
                        'task_id' => $taskId,
                        'post_id' => $post->id,
                        'prompt' => $prompt,
                    ]);

                    return $log;
                } else {
                    $log->markAsFailed('No task ID returned from API', $data);
                    return null;
                }
            } else {
                $errorMessage = "API request failed: " . $response->status() . " - " . $response->body();
                $log->markAsFailed($errorMessage, $response->json() ?? []);
                Log::error('PiAPI Midjourney API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'prompt' => $prompt,
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('PiAPI Midjourney image generation failed', [
                'error' => $e->getMessage(),
                'prompt' => $prompt,
                'post_id' => $post->id ?? null,
            ]);

            if (isset($log)) {
                $log->markAsFailed($e->getMessage());
            }

            return null;
        }
    }

    /**
     * Check the status of an image generation task
     */
    public function checkTaskStatus(string $taskId): ?array
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('PiAPI API key not configured');
                return null;
            }

            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
            ])->timeout(30)->get("{$this->baseUrl}/api/v1/task/{$taskId}");

            if ($response->successful()) {
                return $response->json();
            } else {
                Log::error('Failed to check PiAPI task status', [
                    'task_id' => $taskId,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('Error checking PiAPI task status', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Upscale a specific image from the 4-image grid
     */
    public function upscaleImage(string $originTaskId, int $index = 1): ?array
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('PiAPI API key not configured');
                return null;
            }

            // Step 2: Upscale selected image (index 1-4)
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post("{$this->baseUrl}/api/v1/task", [
                'model' => 'midjourney',
                'task_type' => 'upscale',
                'input' => [
                    'origin_task_id' => $originTaskId,
                    'index' => (string)$index, // Convert to string as required by API
                ],
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $taskId = $data['data']['task_id'] ?? null;

                if ($taskId) {
                    Log::info('PiAPI Midjourney upscale started (Step 2: Upscale)', [
                        'upscale_task_id' => $taskId,
                        'origin_task_id' => $originTaskId,
                        'index' => $index,
                    ]);

                    return $data;
                } else {
                    Log::error('No task ID returned from upscale API', ['response' => $data]);
                    return null;
                }
            } else {
                Log::error('PiAPI Midjourney upscale API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'origin_task_id' => $originTaskId,
                    'index' => $index,
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('PiAPI Midjourney upscale failed', [
                'error' => $e->getMessage(),
                'origin_task_id' => $originTaskId,
                'index' => $index,
            ]);
            return null;
        }
    }

    /**
     * Process completed image generation (handles both imagine and upscale)
     */
    public function processCompletedGeneration(ImageGenerationLog $log): bool
    {
        try {
            $response = $this->checkTaskStatus($log->midjourney_task_id);

            if (!$response) {
                return false;
            }

            // PiAPI response structure: data.status and data.output.image_url
            $taskData = $response['data'] ?? [];
            $taskStatus = $taskData['status'] ?? 'unknown';
            $output = $taskData['output'] ?? [];
            $imageUrl = $output['image_url'] ?? null;

            switch (strtolower($taskStatus)) {
                case 'completed':
                    // Check if this is the initial imagine task (4-image grid) that needs upscaling
                    if ($log->needs_upscale ?? false) {
                        return $this->handleImageGridCompletion($log, $response);
                    }

                    // This is an upscaled single image - final result
                    if ($imageUrl) {
                        $log->markAsCompleted($imageUrl, $response);

                        // Update the automated post with the final single image URL
                        $log->automatedPost->update([
                            'image_url' => $imageUrl,
                            'status' => 'ready',
                        ]);

                        Log::info('PiAPI Midjourney single image completed', [
                            'task_id' => $log->midjourney_task_id,
                            'post_id' => $log->automated_post_id,
                            'image_url' => $imageUrl,
                        ]);

                        return true;
                    } else {
                        $log->markAsFailed('No image URL in completed response', $response);
                        return false;
                    }

                case 'failed':
                    $errorData = $taskData['error'] ?? [];
                    $errorMessage = $errorData['message'] ?? 'Generation failed';
                    $log->markAsFailed($errorMessage, $response);

                    // Mark the post as failed too
                    $log->automatedPost->markAsFailed("Image generation failed: {$errorMessage}");

                    Log::error('PiAPI Midjourney image generation failed', [
                        'task_id' => $log->midjourney_task_id,
                        'error' => $errorMessage,
                    ]);

                    return false;

                case 'processing':
                case 'pending':
                case 'staged':
                    // Still in progress, no action needed
                    return false;

                default:
                    Log::warning('Unknown PiAPI task status', [
                        'task_id' => $log->midjourney_task_id,
                        'status' => $taskStatus,
                    ]);
                    return false;
            }
        } catch (\Exception $e) {
            Log::error('Error processing PiAPI generation', [
                'task_id' => $log->midjourney_task_id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle completion of 4-image grid and automatically select best image for upscaling
     */
    private function handleImageGridCompletion(ImageGenerationLog $log, array $response): bool
    {
        try {
            $taskData = $response['data'] ?? [];
            $output = $taskData['output'] ?? [];
            $gridImageUrl = $output['image_url'] ?? null;

            if (!$gridImageUrl) {
                $log->markAsFailed('No grid image URL in completed response', $response);
                return false;
            }

            Log::info('4-image grid completed, selecting best image for upscaling', [
                'task_id' => $log->midjourney_task_id,
                'grid_image_url' => $gridImageUrl,
            ]);

            // Intelligently select the best image from the grid (1-4)
            $selectedIndex = $this->selectBestImageFromGrid($gridImageUrl, $log);

            // Start upscale process for the selected image
            $upscaleResponse = $this->upscaleImage($log->midjourney_task_id, $selectedIndex);

            if ($upscaleResponse) {
                $upscaleTaskId = $upscaleResponse['data']['task_id'] ?? null;

                if ($upscaleTaskId) {
                    // Update log with upscale task ID and mark as processing upscale
                    $log->update([
                        'midjourney_task_id' => $upscaleTaskId, // Switch to upscale task ID
                        'needs_upscale' => false, // No longer needs upscaling
                        'selected_image_index' => $selectedIndex,
                        'grid_image_url' => $gridImageUrl,
                        'response_data' => array_merge($log->response_data ?? [], [
                            'upscale_response' => $upscaleResponse,
                            'selected_index' => $selectedIndex,
                        ]),
                    ]);

                    Log::info('Upscale started for selected image', [
                        'original_task_id' => $log->midjourney_task_id,
                        'upscale_task_id' => $upscaleTaskId,
                        'selected_index' => $selectedIndex,
                    ]);

                    // The upscale is now processing, CheckImageGenerationJob will handle the rest
                    return false; // Return false to continue monitoring
                } else {
                    $log->markAsFailed('No upscale task ID returned', $upscaleResponse);
                    return false;
                }
            } else {
                $log->markAsFailed('Failed to start upscale process');
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Error handling image grid completion', [
                'log_id' => $log->id,
                'error' => $e->getMessage(),
            ]);
            $log->markAsFailed('Error processing grid completion: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Intelligently select the best image from the 4-image grid
     */
    private function selectBestImageFromGrid(string $gridImageUrl, ImageGenerationLog $log): int
    {
        // For now, we'll use a smart default selection strategy
        // In the future, this could be enhanced with AI image quality analysis

        $persona = $log->automatedPost->botPersona ?? null;
        $contentFocus = $persona->content_focus ?? '';

        // Selection strategy based on content type and general best practices
        $selectionStrategies = [
            'fitness' => 2, // Top-right often has better action shots
            'nutrition' => 1, // Top-left often has better food composition
            'mental_health' => 3, // Bottom-left often has better calm/serene images
            'wellness' => 4, // Bottom-right often has better lifestyle shots
            'sleep' => 3, // Bottom-left for peaceful/calm imagery
            'mindfulness' => 1, // Top-left for centered/balanced compositions
        ];

        // Try to match content focus with strategy
        foreach ($selectionStrategies as $focus => $index) {
            if (stripos($contentFocus, $focus) !== false) {
                Log::info('Selected image based on content focus', [
                    'content_focus' => $contentFocus,
                    'matched_focus' => $focus,
                    'selected_index' => $index,
                ]);
                return $index;
            }
        }

        // Default intelligent selection: prefer top-right (index 2) as it often has the best composition
        $defaultIndex = 2;

        Log::info('Selected image using default strategy', [
            'content_focus' => $contentFocus,
            'selected_index' => $defaultIndex,
            'reason' => 'Top-right typically has best composition',
        ]);

        return $defaultIndex;
    }

    /**
     * Enhance prompt with style and quality parameters for Midjourney
     */
    public function enhancePrompt(string $basePrompt, array $options = []): string
    {
        $enhancedPrompt = $basePrompt;

        // Add default style parameters for health/wellness content
        $defaultStyles = [
            'bright and airy',
            'natural lighting',
            'clean aesthetic',
            'professional photography',
            'high quality',
            'Instagram-worthy',
        ];

        $styles = $options['styles'] ?? $defaultStyles;

        if (!empty($styles)) {
            $enhancedPrompt .= ', ' . implode(', ', $styles);
        }

        // Add face reference if provided for character consistency
        if (isset($options['face_reference_url']) && !empty($options['face_reference_url'])) {
            $enhancedPrompt .= ' --cref ' . $options['face_reference_url'] . ' --cw 100';
        }

        // Add Midjourney technical parameters for single image generation
        // Fixed: Use single --no parameter compatible with v6.1
        $enhancedPrompt .= ' --ar 1:1 --v 6.1 --quality 1 --no "grid, collage, multiple images"';

        return $enhancedPrompt;
    }

    /**
     * Generate image with consistent character face
     */
    public function generateImageWithConsistentFace(string $prompt, AutomatedPost $post, ?string $faceReferenceUrl = null): ?ImageGenerationLog
    {
        try {
            // Get face reference from persona if not provided
            if (!$faceReferenceUrl && $post->botPersona && $post->botPersona->face_reference_url) {
                $faceReferenceUrl = $post->botPersona->face_reference_url;
            }

            // Enhance prompt with face consistency
            $enhancedPrompt = $this->enhancePrompt($prompt, [
                'face_reference_url' => $faceReferenceUrl,
                'styles' => [
                    'consistent character',
                    'same person',
                    'natural lighting',
                    'high quality',
                    'Instagram-worthy',
                    'professional photography'
                ]
            ]);

            Log::info('Generating image with consistent face', [
                'post_id' => $post->id,
                'persona_id' => $post->bot_persona_id,
                'face_reference_url' => $faceReferenceUrl ? 'provided' : 'none',
                'enhanced_prompt' => $enhancedPrompt,
            ]);

            return $this->generateImage($enhancedPrompt, $post);

        } catch (\Exception $e) {
            Log::error('Error generating image with consistent face', [
                'post_id' => $post->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get account information and usage from PiAPI
     */
    public function getAccountInfo(): ?array
    {
        try {
            if (empty($this->apiKey)) {
                return null;
            }

            // PiAPI account info endpoint
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
            ])->timeout(30)->get("{$this->baseUrl}/api/v1/account");

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error getting PiAPI account info', [
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get available process modes for optimization
     */
    public function getAvailableProcessModes(): array
    {
        return [
            'relax' => 'Slower but cheaper processing',
            'fast' => 'Standard processing speed',
            'turbo' => 'Fastest processing (premium)',
        ];
    }

    /**
     * Optimize process mode based on priority and time
     */
    public function getOptimalProcessMode(bool $isUrgent = false, bool $isPremium = false): string
    {
        if ($isUrgent && $isPremium) {
            return 'turbo';
        } elseif ($isUrgent) {
            return 'fast';
        } else {
            return 'relax'; // Most cost-effective for automated posting
        }
    }

    /**
     * Generate image directly and wait for completion (for avatar generation)
     */
    public function generateImageDirect(string $prompt, int $maxWaitSeconds = 300): ?array
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('PiAPI API key not configured');
                return null;
            }

            Log::info('Starting direct image generation', ['prompt' => $prompt]);

            // Step 1: Generate 4-image grid
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post("{$this->baseUrl}/api/v1/task", [
                'model' => 'midjourney',
                'task_type' => 'imagine',
                'input' => [
                    'prompt' => $prompt,
                    'aspect_ratio' => '1:1',
                    'process_mode' => 'fast',
                    'skip_prompt_check' => false,
                ],
            ]);

            if (!$response->successful()) {
                Log::error('Failed to start image generation', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $data = $response->json();
            $taskId = $data['data']['task_id'] ?? null;

            if (!$taskId) {
                Log::error('No task ID returned from API', ['response' => $data]);
                return null;
            }

            Log::info('Image generation started, waiting for completion', ['task_id' => $taskId]);

            // Step 2: Wait for grid completion
            $gridImageUrl = $this->waitForTaskCompletion($taskId, $maxWaitSeconds);

            if (!$gridImageUrl) {
                Log::error('Grid generation failed or timed out', ['task_id' => $taskId]);
                return null;
            }

            Log::info('Grid completed, starting upscale', ['grid_url' => $gridImageUrl]);

            // Step 3: Upscale first image (index 1)
            $upscaleResponse = $this->upscaleImage($taskId, 1);

            if (!$upscaleResponse) {
                Log::error('Failed to start upscale', ['origin_task_id' => $taskId]);
                return null;
            }

            $upscaleTaskId = $upscaleResponse['data']['task_id'] ?? null;

            if (!$upscaleTaskId) {
                Log::error('No upscale task ID returned', ['response' => $upscaleResponse]);
                return null;
            }

            // Step 4: Wait for upscale completion
            $finalImageUrl = $this->waitForTaskCompletion($upscaleTaskId, $maxWaitSeconds);

            if (!$finalImageUrl) {
                Log::error('Upscale failed or timed out', ['task_id' => $upscaleTaskId]);
                return null;
            }

            Log::info('Direct image generation completed successfully', [
                'final_image_url' => $finalImageUrl,
                'upscale_task_id' => $upscaleTaskId,
            ]);

            return [
                'image_url' => $finalImageUrl,
                'task_id' => $upscaleTaskId,
                'grid_url' => $gridImageUrl,
                'original_task_id' => $taskId,
            ];

        } catch (\Exception $e) {
            Log::error('Error in direct image generation', [
                'error' => $e->getMessage(),
                'prompt' => $prompt,
            ]);
            return null;
        }
    }

    /**
     * Wait for task completion and return image URL
     */
    private function waitForTaskCompletion(string $taskId, int $maxWaitSeconds): ?string
    {
        $startTime = time();
        $checkInterval = 10; // Check every 10 seconds

        while ((time() - $startTime) < $maxWaitSeconds) {
            $response = $this->checkTaskStatus($taskId);

            if (!$response) {
                sleep($checkInterval);
                continue;
            }

            $taskData = $response['data'] ?? [];
            $status = strtolower($taskData['status'] ?? 'unknown');

            if ($status === 'completed') {
                $output = $taskData['output'] ?? [];
                $imageUrl = $output['image_url'] ?? null;

                if ($imageUrl) {
                    return $imageUrl;
                }
            } elseif ($status === 'failed') {
                Log::error('Task failed', ['task_id' => $taskId, 'response' => $response]);
                return null;
            }

            sleep($checkInterval);
        }

        Log::warning('Task timed out', ['task_id' => $taskId, 'max_wait' => $maxWaitSeconds]);
        return null;
    }
}

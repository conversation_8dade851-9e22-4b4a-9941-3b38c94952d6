<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bot_personas', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('handle')->unique();
            $table->integer('age');
            $table->text('bio');
            $table->json('interests'); // Array of interests
            $table->json('instagram_posts'); // Array of post types
            $table->text('ai_post_prompt'); // AI prompt for generating posts
            $table->text('image_prompt'); // Midjourney image generation prompt
            $table->text('avatar_url')->nullable(); // URL to persona's avatar image
            $table->text('face_reference_url')->nullable(); // URL to reference image for consistent face generation
            $table->text('physical_description')->nullable(); // Detailed physical description for AI generation
            $table->boolean('is_active')->default(true);
            $table->integer('posts_per_week')->default(3); // How many posts per week
            $table->json('posting_schedule')->nullable(); // Preferred posting times
            $table->json('hashtag_preferences')->nullable(); // Preferred hashtags
            $table->string('tone')->default('friendly'); // Posting tone
            $table->string('content_focus')->nullable(); // Main content focus area
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bot_personas');
    }
};

<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NScaleService extends BaseChatService
{
    /**
     * Initialize NScale service configuration
     */
    protected function initializeService()
    {
        $this->serviceName = 'NScale';
        $this->apiKey = config('services.nscale.api_key');
        $this->apiUrl = config('services.nscale.api_url');
        $this->model = config('services.nscale.model');
    }

    /**
     * Make API request to NScale
     */
    protected function makeApiRequest($payload, $timeout = 60)
    {
        return Http::withHeaders([
            'Authorization' => "Bearer {$this->apiKey}",
            'Content-Type' => 'application/json',
        ])->timeout($timeout)
        ->post($this->apiUrl, $payload);
    }
}
<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Mail\OrderFollowUpMail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class SendOrderFollowUpEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:send-follow-up-emails';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send follow-up emails to customers 7 days after order shipment for product reviews';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to send order follow-up emails for product reviews...');

        // Get orders that were shipped exactly 7 days ago
        $sevenDaysAgo = Carbon::now()->subDays(7)->startOfDay();
        $sevenDaysAgoEnd = Carbon::now()->subDays(7)->endOfDay();

        $orders = Order::with(['user', 'items.product'])
            ->whereIn('status', ['shipped', 'delivered'])
            ->whereBetween('shipped_at', [$sevenDaysAgo, $sevenDaysAgoEnd])
            ->whereNotNull('shipped_at')
            ->whereNull('review_follow_up_sent_at') // Only orders that haven't had follow-up sent
            ->whereHas('user') // Ensure user exists
            ->get();

        $this->info("Found {$orders->count()} orders to send follow-up emails for.");

        $successCount = 0;
        $errorCount = 0;

        foreach ($orders as $order) {
            try {
                // Only send if order has physical products that can be reviewed
                $hasReviewableItems = $order->items->where('product_type', 'physical')->count() > 0;

                if (!$hasReviewableItems) {
                    $this->line("⏭ Skipping order #{$order->order_number} - no reviewable items");
                    continue;
                }

                Mail::to($order->user->email)->send(new OrderFollowUpMail($order));

                $this->line("✓ Sent review follow-up email for order #{$order->order_number} to {$order->user->email}");
                $successCount++;

                // Mark that follow-up email was sent (you may want to add this field to orders table)
                $order->update(['review_follow_up_sent_at' => now()]);

            } catch (\Exception $e) {
                $this->error("✗ Failed to send review follow-up email for order #{$order->order_number}: " . $e->getMessage());
                \Log::error("Failed to send review follow-up email for order {$order->id}: " . $e->getMessage());
                $errorCount++;
            }
        }

        $this->info("Follow-up email sending completed!");
        $this->info("Successfully sent: {$successCount}");
        if ($errorCount > 0) {
            $this->warn("Failed to send: {$errorCount}");
        }

        return Command::SUCCESS;
    }
}

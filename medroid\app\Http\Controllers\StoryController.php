<?php

namespace App\Http\Controllers;

use App\Models\Story;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Services\FileUploadService;

class StoryController extends Controller
{
    /**
     * Get all active stories.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            // Get all active stories grouped by user
            $stories = Story::with('user')
                ->active()
                ->orderBy('created_at', 'desc')
                ->get()
                ->groupBy('user_id')
                ->map(function ($userStories) use ($user) {
                    $firstStory = $userStories->first();
                    $hasUnviewed = $userStories->contains(function ($story) use ($user) {
                        return !$story->hasBeenViewedBy($user->id);
                    });

                    return [
                        'user_id' => $firstStory->user_id,
                        'username' => $firstStory->user->name ?? 'Unknown',
                        'user_avatar' => $firstStory->formatted_media_url, // Use the latest story image as thumbnail
                        'stories_count' => $userStories->count(),
                        'latest_story_time' => $firstStory->created_at,
                        'has_unviewed' => $hasUnviewed,
                        'is_current_user' => $firstStory->user_id === $user->id,
                        'stories' => $userStories->map(function ($story) use ($user) {
                            return [
                                'id' => $story->id,
                                'media_url' => $story->formatted_media_url,
                                'media_type' => $story->media_type,
                                'caption' => $story->caption,
                                'created_at' => $story->created_at,
                                'expires_at' => $story->expires_at,
                                'time_remaining' => $story->time_remaining,
                                'is_viewed' => $story->hasBeenViewedBy($user->id),
                                'viewers_count' => $story->viewers_count,
                            ];
                        })->values()
                    ];
                })
                ->values();

            return response()->json([
                'success' => true,
                'stories' => $stories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load stories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new story.
     */
    public function store(Request $request, FileUploadService $fileUploadService)
    {
        try {
            // Log the incoming request for debugging
            \Illuminate\Support\Facades\Log::info('Story upload request received', [
                'user_id' => $request->user() ? $request->user()->id : 'no user',
                'has_media' => $request->hasFile('media'),
                'caption' => $request->caption,
                'headers' => $request->headers->all()
            ]);
            \Illuminate\Support\Facades\Log::info('Starting validation...');

            $validator = Validator::make($request->all(), [
                'media' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,mov|max:20480', // 20MB max
                'caption' => 'nullable|string|max:500',
                'media_type' => 'nullable|in:image,video'
            ]);

            if ($validator->fails()) {
                \Illuminate\Support\Facades\Log::warning('Validation failed', ['errors' => $validator->errors()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            \Illuminate\Support\Facades\Log::info('Validation passed');

            $user = $request->user();
            $uploadedFile = $request->file('media');

            \Illuminate\Support\Facades\Log::info('File info', [
                'original_name' => $uploadedFile->getClientOriginalName(),
                'size' => $uploadedFile->getSize(),
                'mime_type' => $uploadedFile->getMimeType()
            ]);

            // Determine media type if not provided
            $mediaType = $request->media_type;
            if (!$mediaType) {
                $mimeType = $uploadedFile->getMimeType();
                $mediaType = str_starts_with($mimeType, 'video/') ? 'video' : 'image';
            }

            // Upload file using the file management system
            $file = $fileUploadService->uploadFile(
                $uploadedFile,
                'stories',
                'Story by ' . $user->name,
                'Story media uploaded by user',
                true // Make stories public
            );

            \Illuminate\Support\Facades\Log::info('Story file uploaded via file management system', [
                'file_id' => $file->id,
                'file_url' => $file->url,
                'media_type' => $mediaType
            ]);

            // Create the story
            $story = Story::create([
                'user_id' => $user->id,
                'media_url' => $file->url,
                'media_type' => $mediaType,
                'caption' => $request->caption,
                'is_active' => true,
            ]);

            // Track file usage for the story
            \App\Models\FileUsage::create([
                'file_id' => $file->id,
                'usable_type' => get_class($story),
                'usable_id' => $story->id,
                'usage_type' => 'story_media',
            ]);

            $story->load('user');

            return response()->json([
                'success' => true,
                'message' => 'Story created successfully',
                'story' => [
                    'id' => $story->id,
                    'media_url' => $story->formatted_media_url,
                    'media_type' => $story->media_type,
                    'caption' => $story->caption,
                    'created_at' => $story->created_at,
                    'expires_at' => $story->expires_at,
                    'time_remaining' => $story->time_remaining,
                    'viewers_count' => $story->viewers_count,
                    'user' => [
                        'id' => $story->user->id,
                        'name' => $story->user->name,
                        'avatar' => $story->user->profile_image,
                    ]
                ]
            ], 201);
        } catch (\Exception $e) {
            // Log the detailed error
            \Illuminate\Support\Facades\Log::error('Story creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user() ? $request->user()->id : 'no user',
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create story',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get stories for a specific user.
     */
    public function getUserStories(Request $request, $userId)
    {
        try {
            $currentUser = $request->user();

            $stories = Story::with('user')
                ->where('user_id', $userId)
                ->active()
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($story) use ($currentUser) {
                    return [
                        'id' => $story->id,
                        'media_url' => $story->formatted_media_url,
                        'media_type' => $story->media_type,
                        'caption' => $story->caption,
                        'created_at' => $story->created_at,
                        'expires_at' => $story->expires_at,
                        'time_remaining' => $story->time_remaining,
                        'is_viewed' => $story->hasBeenViewedBy($currentUser->id),
                        'viewers_count' => $story->viewers_count,
                    ];
                });

            return response()->json([
                'success' => true,
                'stories' => $stories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load user stories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark a story as viewed.
     */
    public function markAsViewed(Request $request, $storyId)
    {
        try {
            $story = Story::findOrFail($storyId);
            $user = $request->user();

            $story->markAsViewedBy($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Story marked as viewed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark story as viewed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a story.
     */
    public function destroy(Request $request, $storyId)
    {
        try {
            $story = Story::where('id', $storyId)
                ->where('user_id', $request->user()->id)
                ->firstOrFail();

            // Delete the media file
            if ($story->media_url) {
                $filePath = str_replace('/storage/', 'public/', $story->media_url);
                Storage::delete($filePath);
            }

            $story->delete();

            return response()->json([
                'success' => true,
                'message' => 'Story deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete story',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get story viewers.
     */
    public function getViewers(Request $request, $storyId)
    {
        try {
            $story = Story::where('id', $storyId)
                ->where('user_id', $request->user()->id)
                ->firstOrFail();

            $viewerIds = $story->viewers ?? [];
            $viewers = \App\Models\User::whereIn('id', $viewerIds)
                ->select('id', 'name', 'profile_image')
                ->get();

            return response()->json([
                'success' => true,
                'viewers' => $viewers,
                'viewers_count' => count($viewerIds)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load story viewers',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

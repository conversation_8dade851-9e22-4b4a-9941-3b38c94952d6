<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\SocialContent;
use App\Models\AutomatedPost;
use Illuminate\Console\Command;
use Carbon\Carbon;

class MonitorHighFrequencyPosting extends Command
{
    protected $signature = 'bot:monitor-posting {--watch : Watch mode with live updates} {--detailed : Show detailed persona breakdown}';
    protected $description = 'Monitor high-frequency bot posting progress (4 posts per persona per day)';

    public function handle()
    {
        $watch = $this->option('watch');
        $detailed = $this->option('detailed');
        
        if ($watch) {
            $this->watchMode($detailed);
        } else {
            $this->showStatus($detailed);
        }
        
        return 0;
    }
    
    private function watchMode(bool $detailed): void
    {
        $this->info('🔄 Starting watch mode (press Ctrl+C to exit)...');
        
        while (true) {
            system('clear');
            $this->showStatus($detailed);
            sleep(10); // Update every 10 seconds
        }
    }
    
    private function showStatus(bool $detailed): void
    {
        $this->info('🚀 HIGH-FREQUENCY BOT POSTING MONITOR');
        $this->info('=====================================');
        
        $totalPersonas = BotPersona::active()->count();
        $targetPerDay = $totalPersonas * 4;
        $targetPerWeek = $targetPerDay * 7;
        
        $this->info("📊 TARGETS:");
        $this->info("   Personas: {$totalPersonas}");
        $this->info("   Posts per persona per day: 4");
        $this->info("   Daily target: {$targetPerDay} posts");
        $this->info("   Weekly target: {$targetPerWeek} posts");
        $this->newLine();
        
        // Today's progress
        $todayPosts = SocialContent::where('source', 'bot_generated')
            ->whereDate('created_at', today())
            ->count();
            
        $personasPostedToday = BotPersona::whereHas('automatedPosts', function($q) {
            $q->where('status', 'posted')->whereDate('posted_at', today());
        })->count();
        
        $readyPosts = AutomatedPost::where('status', 'ready')->count();
        $pendingPosts = AutomatedPost::where('status', 'pending')->count();
        $failedPosts = AutomatedPost::where('status', 'failed')
            ->whereDate('created_at', today())
            ->count();
        
        $this->info("📈 TODAY'S PROGRESS:");
        $this->info("   ✅ Posts published: {$todayPosts}/{$targetPerDay} (" . round(($todayPosts / $targetPerDay) * 100, 1) . "%)");
        $this->info("   👥 Personas active: {$personasPostedToday}/{$totalPersonas}");
        $this->info("   ⏳ Posts ready: {$readyPosts}");
        $this->info("   🔄 Posts pending: {$pendingPosts}");
        if ($failedPosts > 0) {
            $this->warn("   ❌ Posts failed: {$failedPosts}");
        }
        $this->newLine();
        
        // Weekly progress
        $weekStart = now()->startOfWeek();
        $weekPosts = SocialContent::where('source', 'bot_generated')
            ->where('created_at', '>=', $weekStart)
            ->count();
            
        $this->info("📅 THIS WEEK'S PROGRESS:");
        $this->info("   ✅ Posts published: {$weekPosts}/{$targetPerWeek} (" . round(($weekPosts / $targetPerWeek) * 100, 1) . "%)");
        $this->newLine();
        
        // Engagement metrics
        $avgEngagement = SocialContent::where('source', 'bot_generated')
            ->whereDate('created_at', today())
            ->whereNotNull('engagement_metrics')
            ->get()
            ->avg(function($post) {
                $metrics = $post->engagement_metrics;
                return $metrics['likes'] ?? 0;
            });
            
        if ($avgEngagement > 0) {
            $this->info("💫 ENGAGEMENT:");
            $this->info("   📊 Avg likes per post: " . round($avgEngagement, 1));
        }
        
        if ($detailed) {
            $this->showDetailedBreakdown();
        }
        
        $this->info("🕒 Last updated: " . now()->format('H:i:s'));
    }
    
    private function showDetailedBreakdown(): void
    {
        $this->newLine();
        $this->info("👥 PERSONA BREAKDOWN:");
        $this->info("=====================");
        
        $personas = BotPersona::active()->with(['automatedPosts' => function($q) {
            $q->where('status', 'posted')->whereDate('posted_at', today());
        }])->get();
        
        foreach ($personas as $persona) {
            $todayPosts = $persona->automatedPosts->count();
            $status = $this->getPersonaStatus($todayPosts);
            
            $this->line("   {$status} {$persona->full_name}: {$todayPosts}/4 posts");
        }
    }
    
    private function getPersonaStatus(int $posts): string
    {
        if ($posts >= 4) {
            return '✅';
        } elseif ($posts >= 2) {
            return '🟡';
        } elseif ($posts >= 1) {
            return '🟠';
        } else {
            return '⭕';
        }
    }
}

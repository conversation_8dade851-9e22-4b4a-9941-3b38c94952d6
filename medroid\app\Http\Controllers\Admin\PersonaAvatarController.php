<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BotPersona;
use App\Services\AvatarGenerationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PersonaAvatarController extends Controller
{
    private AvatarGenerationService $avatarService;

    public function __construct(AvatarGenerationService $avatarService)
    {
        $this->avatarService = $avatarService;
    }

    /**
     * Display all personas with their avatars
     */
    public function index()
    {
        $personas = BotPersona::active()
            ->select([
                'id', 'first_name', 'last_name', 'handle', 'age', 'bio',
                'avatar_url', 'face_reference_url', 'physical_description',
                'content_focus', 'is_active'
            ])
            ->orderBy('first_name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $personas,
            'total' => $personas->count(),
        ]);
    }

    /**
     * Generate avatar for specific persona
     */
    public function generateAvatar(Request $request, int $personaId): JsonResponse
    {
        $persona = BotPersona::find($personaId);

        if (!$persona) {
            return response()->json([
                'success' => false,
                'message' => 'Persona not found'
            ], 404);
        }

        $force = $request->boolean('force', false);

        // Check if avatar already exists
        if (!$force && $persona->avatar_url && $persona->face_reference_url) {
            return response()->json([
                'success' => false,
                'message' => 'Avatar already exists. Use force=true to regenerate.',
                'data' => [
                    'avatar_url' => $persona->avatar_url,
                    'face_reference_url' => $persona->face_reference_url,
                ]
            ], 400);
        }

        $success = $this->avatarService->generateAvatarForPersona($persona);

        if ($success) {
            $persona->refresh();
            return response()->json([
                'success' => true,
                'message' => 'Avatar generated successfully',
                'data' => [
                    'persona_id' => $persona->id,
                    'persona_name' => $persona->full_name,
                    'avatar_url' => $persona->avatar_url,
                    'face_reference_url' => $persona->face_reference_url,
                    'physical_description' => $persona->physical_description,
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate avatar'
            ], 500);
        }
    }

    /**
     * Update persona's physical description
     */
    public function updateDescription(Request $request, int $personaId): JsonResponse
    {
        $request->validate([
            'physical_description' => 'required|string|max:1000',
        ]);

        $persona = BotPersona::find($personaId);

        if (!$persona) {
            return response()->json([
                'success' => false,
                'message' => 'Persona not found'
            ], 404);
        }

        $persona->update([
            'physical_description' => $request->physical_description
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Physical description updated successfully',
            'data' => [
                'persona_id' => $persona->id,
                'physical_description' => $persona->physical_description,
            ]
        ]);
    }

    /**
     * Get avatar generation status for all personas
     */
    public function status(): JsonResponse
    {
        $personas = BotPersona::active()->get();

        $stats = [
            'total_personas' => $personas->count(),
            'with_avatars' => $personas->whereNotNull('avatar_url')->count(),
            'with_face_references' => $personas->whereNotNull('face_reference_url')->count(),
            'with_descriptions' => $personas->whereNotNull('physical_description')->count(),
            'complete_profiles' => $personas->filter(function ($persona) {
                return $persona->avatar_url && $persona->face_reference_url && $persona->physical_description;
            })->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}

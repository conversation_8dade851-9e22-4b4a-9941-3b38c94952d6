<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserClub extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'club_type',
        'membership_level',
        'is_verified',
        'verification_method',
        'founder_code_used',
        'joined_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_verified' => 'boolean',
        'joined_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the club membership.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the founder referral code if applicable
     */
    public function founderCode()
    {
        return $this->belongsTo(FounderReferralCode::class, 'founder_code_used', 'code');
    }

    /**
     * Scope for verified memberships
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for founder members
     */
    public function scopeFounders($query)
    {
        return $query->where('club_type', 'founder');
    }

    /**
     * Get membership display name
     */
    public function getDisplayNameAttribute(): string
    {
        $clubNames = [
            'founder' => 'Medroid Founders Club',
            'premium' => 'Premium Club',
            'regular' => 'Regular Club',
        ];

        return $clubNames[$this->club_type] ?? ucfirst($this->club_type) . ' Club';
    }

    /**
     * Get membership level display
     */
    public function getLevelDisplayAttribute(): string
    {
        return ucfirst($this->membership_level);
    }
}

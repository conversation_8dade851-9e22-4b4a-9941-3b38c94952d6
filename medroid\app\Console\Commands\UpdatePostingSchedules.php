<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\PostingSchedule;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdatePostingSchedules extends Command
{
    protected $signature = 'bot:update-schedules {--reset : Reset all existing schedules}';
    protected $description = 'Update posting schedules to support 4 posts per day for each persona';

    public function handle()
    {
        $this->info('🔄 Updating posting schedules for high-frequency posting...');
        
        $reset = $this->option('reset');
        
        if ($reset) {
            $this->info('🗑️  Resetting all existing posting schedules...');
            PostingSchedule::truncate();
        }
        
        $personas = BotPersona::active()->get();
        
        if ($personas->isEmpty()) {
            $this->warn('No active personas found.');
            return 0;
        }
        
        $this->info("Found {$personas->count()} active personas");
        
        $updated = 0;
        $created = 0;
        
        foreach ($personas as $persona) {
            $result = $this->updatePersonaSchedules($persona);
            $updated += $result['updated'];
            $created += $result['created'];
        }
        
        $this->info("✅ Schedule update complete:");
        $this->info("   📝 Created: {$created} new schedules");
        $this->info("   🔄 Updated: {$updated} existing schedules");
        $this->info("   🎯 Total capacity: " . ($personas->count() * 28) . " posts per week");
        
        return 0;
    }
    
    /**
     * Update posting schedules for a specific persona
     */
    private function updatePersonaSchedules(BotPersona $persona): array
    {
        $updated = 0;
        $created = 0;
        
        // Define 4 optimal time slots per day for health content
        $timeSlots = [
            'morning' => ['07:00', '08:30', '09:15'],
            'midday' => ['12:00', '12:30', '13:00'],
            'afternoon' => ['15:30', '16:00', '16:30'],
            'evening' => ['18:30', '19:00', '19:30'],
        ];
        
        // Get content-specific preferences
        $preferredTimes = $this->getContentSpecificTimes($persona->content_focus);
        
        $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        foreach ($daysOfWeek as $day) {
            // Create 4 posting slots per day
            for ($slot = 1; $slot <= 4; $slot++) {
                $timeSlot = $this->selectTimeSlot($slot, $preferredTimes, $timeSlots);
                $time = $timeSlot['time'];
                $priority = ($slot - 1) * 7 + array_search($day, $daysOfWeek) + 1;
                
                // Check if schedule already exists
                $existingSchedule = PostingSchedule::where('bot_persona_id', $persona->id)
                    ->where('day_of_week', $day)
                    ->where('preferred_time', $time)
                    ->first();
                
                if ($existingSchedule) {
                    $existingSchedule->update([
                        'priority' => $priority,
                        'is_active' => true,
                    ]);
                    $updated++;
                } else {
                    PostingSchedule::create([
                        'bot_persona_id' => $persona->id,
                        'day_of_week' => $day,
                        'preferred_time' => $time,
                        'priority' => $priority,
                        'is_active' => true,
                    ]);
                    $created++;
                }
            }
        }
        
        $this->line("✓ {$persona->full_name}: 4 slots per day × 7 days = 28 weekly slots");
        
        Log::info('Updated posting schedules for persona', [
            'persona_id' => $persona->id,
            'persona_name' => $persona->full_name,
            'schedules_created' => $created,
            'schedules_updated' => $updated,
        ]);
        
        return ['created' => $created, 'updated' => $updated];
    }
    
    /**
     * Get content-specific optimal times
     */
    private function getContentSpecificTimes(string $contentFocus): array
    {
        $contentTimes = [
            'yoga' => [
                'morning' => '07:00',    // Morning practice
                'midday' => '12:30',     // Lunch break stretch
                'afternoon' => '16:00',  // Afternoon energy
                'evening' => '19:00',    // Evening wind-down
            ],
            'fitness' => [
                'morning' => '07:30',    // Pre-work workout
                'midday' => '12:00',     // Lunch workout
                'afternoon' => '15:30',  // Post-work prep
                'evening' => '18:30',    // Evening workout
            ],
            'nutrition' => [
                'morning' => '08:30',    // Breakfast tips
                'midday' => '12:00',     // Lunch ideas
                'afternoon' => '16:30',  // Snack time
                'evening' => '18:30',    // Dinner prep
            ],
            'mental health' => [
                'morning' => '09:00',    // Morning mindfulness
                'midday' => '13:00',     // Midday check-in
                'afternoon' => '15:30',  // Afternoon reflection
                'evening' => '19:30',    // Evening self-care
            ],
            'meditation' => [
                'morning' => '07:00',    // Morning meditation
                'midday' => '12:30',     // Midday pause
                'afternoon' => '16:00',  // Afternoon mindfulness
                'evening' => '19:30',    // Evening practice
            ],
        ];
        
        // Default times for other content types
        $defaultTimes = [
            'morning' => '08:30',
            'midday' => '12:30',
            'afternoon' => '16:00',
            'evening' => '19:00',
        ];
        
        return $contentTimes[strtolower($contentFocus)] ?? $defaultTimes;
    }
    
    /**
     * Select appropriate time slot based on slot number and preferences
     */
    private function selectTimeSlot(int $slot, array $preferredTimes, array $timeSlots): array
    {
        $slotMapping = [
            1 => 'morning',
            2 => 'midday', 
            3 => 'afternoon',
            4 => 'evening',
        ];
        
        $period = $slotMapping[$slot];
        $time = $preferredTimes[$period];
        
        return [
            'time' => $time,
            'period' => $period,
            'slot' => $slot,
        ];
    }
}

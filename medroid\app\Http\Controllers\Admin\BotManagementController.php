<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Models\SocialContent;
use App\Models\ImageGenerationLog;
use App\Models\BotSettings;
use App\Jobs\GenerateBotPostJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Inertia\Inertia;

class BotManagementController extends Controller
{
    // No constructor needed - middleware handled in routes

    /**
     * Show the bot management dashboard
     */
    public function dashboard(Request $request)
    {
        $stats = $this->getDashboardStats();
        $recentPosts = $this->getRecentPosts($request);
        $systemHealth = $this->getSystemHealth();
        $personas = BotPersona::with(['botUser.user'])->get();
        $settings = $this->getBotSettings();

        return Inertia::render('Admin/BotManagement/Dashboard', [
            'stats' => $stats,
            'recentPosts' => $recentPosts,
            'systemHealth' => $systemHealth,
            'personas' => $personas,
            'settings' => $settings,
        ]);
    }

    /**
     * Get dashboard statistics
     */
    protected function getDashboardStats(): array
    {
        $cacheKey = 'bot_dashboard_stats';
        
        return Cache::remember($cacheKey, 300, function () {
            return [
                'total_personas' => BotPersona::count(),
                'active_personas' => BotPersona::whereHas('botUser')->count(),
                'total_posts_generated' => AutomatedPost::count(),
                'posts_published_today' => SocialContent::where('source', 'bot_generated')
                    ->whereDate('published_at', today())
                    ->count(),
                'posts_this_week' => SocialContent::where('source', 'bot_generated')
                    ->whereBetween('published_at', [now()->startOfWeek(), now()->endOfWeek()])
                    ->count(),
                'average_quality_score' => AutomatedPost::whereNotNull('generation_prompts->quality_scores->quality_score')
                    ->avg(DB::raw("JSON_EXTRACT(generation_prompts, '$.quality_scores.quality_score')")),
                'pending_posts' => AutomatedPost::whereIn('status', ['draft', 'generating_image', 'ready'])->count(),
                'failed_posts' => AutomatedPost::where('status', 'failed')->count(),
                'image_generation_success_rate' => $this->getImageGenerationSuccessRate(),
            ];
        });
    }

    /**
     * Get recent posts for dashboard with pagination and filtering
     */
    public function getRecentPosts(Request $request): array
    {
        $perPage = $request->get('per_page', 10);
        $status = $request->get('status', 'all');
        $page = $request->get('page', 1);

        $query = AutomatedPost::with(['botPersona', 'imageGenerationLog']);

        // Apply status filter
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Get paginated results
        $posts = $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return [
            'data' => $posts->map(function ($post) {
                $imageLog = $post->imageGenerationLog;

                return [
                    'id' => $post->id,
                    'persona_name' => $post->botPersona->full_name,
                    'persona_focus' => $post->botPersona->content_focus,
                    'status' => $post->status,
                    'caption' => $post->caption,
                    'caption_length' => strlen($post->caption ?? ''),
                    'hashtags' => $post->hashtags,
                    'image_url' => $post->image_url,
                    'quality_score' => $post->generation_prompts['quality_scores']['quality_score'] ?? null,
                    'generation_prompts' => $post->generation_prompts,
                    'created_at' => $post->created_at,
                    'scheduled_at' => $post->scheduled_at,
                    'posted_at' => $post->posted_at,
                    'error_message' => $post->error_message,
                    // Intelligent Image Selection Details
                    'image_generation' => $imageLog ? [
                        'status' => $imageLog->status,
                        'grid_image_url' => $imageLog->grid_image_url,
                        'selected_image_index' => $imageLog->selected_image_index,
                        'final_image_url' => $imageLog->image_url,
                        'midjourney_task_id' => $imageLog->midjourney_task_id,
                        'prompt' => $imageLog->prompt,
                        'needs_upscale' => $imageLog->needs_upscale,
                        'started_at' => $imageLog->started_at,
                        'completed_at' => $imageLog->completed_at,
                        'selection_reason' => $this->getSelectionReason($post->botPersona->content_focus, $imageLog->selected_image_index),
                    ] : null,
                ];
            })->toArray(),
            'pagination' => [
                'current_page' => $posts->currentPage(),
                'last_page' => $posts->lastPage(),
                'per_page' => $posts->perPage(),
                'total' => $posts->total(),
                'from' => $posts->firstItem(),
                'to' => $posts->lastItem(),
            ],
            'filters' => [
                'status' => $status,
                'available_statuses' => ['all', 'draft', 'generating_image', 'ready', 'posted', 'failed'],
            ],
        ];
    }

    /**
     * Get explanation for intelligent image selection
     */
    protected function getSelectionReason(string $contentFocus, ?int $selectedIndex): ?string
    {
        if (!$selectedIndex) {
            return null;
        }

        $indexDescriptions = [
            1 => 'Top-left: Best for centered compositions and food photography',
            2 => 'Top-right: Optimal for action shots and fitness content',
            3 => 'Bottom-left: Ideal for calm, peaceful, and mental health imagery',
            4 => 'Bottom-right: Perfect for lifestyle and wellness shots',
        ];

        $contentStrategies = [
            'fitness' => 'Selected for action shots and dynamic movement',
            'nutrition' => 'Selected for food composition and visual appeal',
            'mental_health' => 'Selected for calm and serene imagery',
            'wellness' => 'Selected for lifestyle and wellness aesthetics',
            'sleep' => 'Selected for peaceful and restful imagery',
            'mindfulness' => 'Selected for balanced and centered compositions',
            'yoga' => 'Selected for action shots and pose demonstration',
            'cycling' => 'Selected for dynamic movement and action',
            'cooking' => 'Selected for food presentation and composition',
        ];

        $baseDescription = $indexDescriptions[$selectedIndex] ?? "Position {$selectedIndex}";

        // Find matching content strategy
        foreach ($contentStrategies as $focus => $reason) {
            if (stripos($contentFocus, $focus) !== false) {
                return "{$baseDescription}. {$reason}.";
            }
        }

        return "{$baseDescription}. Selected using default composition strategy.";
    }

    /**
     * Get system health status
     */
    protected function getSystemHealth(): array
    {
        try {
            // Simple health check based on database status
            $totalPersonas = BotPersona::count();
            $activePersonas = BotPersona::whereHas('botUser')->count();
            $recentPosts = AutomatedPost::where('created_at', '>=', now()->subDay())->count();

            $isHealthy = $totalPersonas > 0 && $activePersonas > 0;

            return [
                'overall_healthy' => $isHealthy,
                'total_personas' => $totalPersonas,
                'active_personas' => $activePersonas,
                'recent_posts' => $recentPosts,
                'last_checked' => now(),
            ];
        } catch (\Exception $e) {
            return [
                'overall_healthy' => false,
                'error' => $e->getMessage(),
                'last_checked' => now(),
            ];
        }
    }

    /**
     * Get image generation success rate
     */
    protected function getImageGenerationSuccessRate(): float
    {
        $total = ImageGenerationLog::count();
        if ($total === 0) return 100.0;
        
        $successful = ImageGenerationLog::where('status', 'completed')->count();
        return round(($successful / $total) * 100, 1);
    }

    /**
     * Get detailed post information including intelligent image selection details
     */
    public function getPostDetails(Request $request, $postId)
    {
        try {
            $post = AutomatedPost::with([
                'botPersona',
                'botUser.user',
                'imageGenerationLog'
            ])->findOrFail($postId);

            $imageLog = $post->imageGenerationLog;

            return response()->json([
                'success' => true,
                'post' => [
                    'id' => $post->id,
                    'persona' => [
                        'id' => $post->botPersona->id,
                        'name' => $post->botPersona->full_name,
                        'content_focus' => $post->botPersona->content_focus,
                        'image_prompt' => $post->botPersona->image_prompt,
                    ],
                    'content' => [
                        'caption' => $post->caption,
                        'hashtags' => $post->hashtags,
                        'full_caption' => $post->getFullCaptionAttribute(),
                    ],
                    'status' => $post->status,
                    'timestamps' => [
                        'created_at' => $post->created_at,
                        'scheduled_at' => $post->scheduled_at,
                        'posted_at' => $post->posted_at,
                    ],
                    'image_generation' => $imageLog ? [
                        'status' => $imageLog->status,
                        'midjourney_task_id' => $imageLog->midjourney_task_id,
                        'prompt' => $imageLog->prompt,
                        'grid_image_url' => $imageLog->grid_image_url,
                        'selected_image_index' => $imageLog->selected_image_index,
                        'final_image_url' => $imageLog->image_url,
                        'needs_upscale' => $imageLog->needs_upscale,
                        'selection_reason' => $this->getSelectionReason(
                            $post->botPersona->content_focus,
                            $imageLog->selected_image_index
                        ),
                        'timestamps' => [
                            'started_at' => $imageLog->started_at,
                            'completed_at' => $imageLog->completed_at,
                        ],
                        'response_data' => $imageLog->response_data,
                        'error_message' => $imageLog->error_message,
                    ] : null,
                    'generation_prompts' => $post->generation_prompts,
                    'error_message' => $post->error_message,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get post details: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate posts for all personas
     */
    public function generateAllPosts(Request $request)
    {
        try {
            $personas = BotPersona::whereHas('botUser')->get();
            $jobsDispatched = 0;

            foreach ($personas as $persona) {
                GenerateBotPostJob::dispatch($persona);
                $jobsDispatched++;
            }

            Log::info('Bulk post generation initiated', [
                'personas_count' => $jobsDispatched,
                'initiated_by' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => "Post generation initiated for {$jobsDispatched} personas",
                'jobs_dispatched' => $jobsDispatched,
            ]);

        } catch (\Exception $e) {
            Log::error('Bulk post generation failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate post generation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate post for specific persona
     */
    public function generatePersonaPost(Request $request, BotPersona $persona)
    {
        try {
            // Get the bot_user_id from the relationship since the column might be null
            $botUserId = $persona->botUser ? $persona->botUser->id : null;

            if (!$botUserId) {
                Log::error('No bot user found for persona', [
                    'persona_id' => $persona->id,
                    'persona_name' => $persona->full_name,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'No bot user associated with this persona',
                ], 400);
            }

            // Create the AutomatedPost record immediately so it shows in recent posts
            $automatedPost = AutomatedPost::create([
                'bot_persona_id' => $persona->id,
                'bot_user_id' => $botUserId,
                'status' => 'draft',
                'caption' => 'Generating content...', // Temporary caption until content is generated
                'hashtags' => json_encode([]), // Empty hashtags array
                'generation_prompts' => [
                    'initiated_by' => 'manual_admin_request',
                    'initiated_at' => now()->toISOString(),
                    'immediate_posting' => true, // Flag for immediate posting
                ],
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Dispatch the job to process immediately (not scheduled)
            GenerateBotPostJob::dispatch($persona, $automatedPost->id, true); // true = immediate processing

            Log::info('Immediate post generation initiated', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
                'automated_post_id' => $automatedPost->id,
                'initiated_by' => auth()->user()->id,
                'immediate_posting' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => "Post generation initiated for {$persona->full_name}",
                'persona' => [
                    'id' => $persona->id,
                    'name' => $persona->full_name,
                    'focus' => $persona->content_focus,
                ],
                'automated_post' => [
                    'id' => $automatedPost->id,
                    'status' => $automatedPost->status,
                    'created_at' => $automatedPost->created_at,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Immediate post generation failed', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate post: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get detailed analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '7d'); // 1d, 7d, 30d
        
        $analytics = [
            'posts_by_day' => $this->getPostsByDay($period),
            'posts_by_persona' => $this->getPostsByPersona($period),
            'quality_scores_distribution' => $this->getQualityScoresDistribution($period),
            'status_distribution' => $this->getStatusDistribution($period),
            'engagement_metrics' => $this->getEngagementMetrics($period),
        ];

        return response()->json($analytics);
    }

    /**
     * Get posts by day for charts
     */
    protected function getPostsByDay(string $period): array
    {
        $days = match($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            default => 7,
        };

        $posts = SocialContent::where('source', 'bot_generated')
            ->where('published_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(published_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return $posts->map(function ($post) {
            return [
                'date' => $post->date,
                'count' => $post->count,
            ];
        })->toArray();
    }

    /**
     * Get posts by persona
     */
    protected function getPostsByPersona(string $period): array
    {
        $days = match($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            default => 7,
        };

        $posts = AutomatedPost::with('botPersona')
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('bot_persona_id, COUNT(*) as count')
            ->groupBy('bot_persona_id')
            ->get();

        return $posts->map(function ($post) {
            return [
                'persona_name' => $post->botPersona->full_name,
                'persona_focus' => $post->botPersona->content_focus,
                'count' => $post->count,
            ];
        })->toArray();
    }

    /**
     * Get quality scores distribution
     */
    protected function getQualityScoresDistribution(string $period): array
    {
        $days = match($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            default => 7,
        };

        $posts = AutomatedPost::where('created_at', '>=', now()->subDays($days))
            ->whereNotNull('generation_prompts->quality_scores->quality_score')
            ->get();

        $distribution = [
            '90-100' => 0,
            '80-89' => 0,
            '70-79' => 0,
            '60-69' => 0,
            'Below 60' => 0,
        ];

        foreach ($posts as $post) {
            $score = $post->generation_prompts['quality_scores']['quality_score'] ?? 0;
            
            if ($score >= 90) $distribution['90-100']++;
            elseif ($score >= 80) $distribution['80-89']++;
            elseif ($score >= 70) $distribution['70-79']++;
            elseif ($score >= 60) $distribution['60-69']++;
            else $distribution['Below 60']++;
        }

        return $distribution;
    }

    /**
     * Get status distribution
     */
    protected function getStatusDistribution(string $period): array
    {
        $days = match($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            default => 7,
        };

        return AutomatedPost::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * Get engagement metrics
     */
    protected function getEngagementMetrics(string $period): array
    {
        $days = match($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            default => 7,
        };

        $posts = SocialContent::where('source', 'bot_generated')
            ->where('published_at', '>=', now()->subDays($days))
            ->get();

        $totalLikes = 0;
        $totalShares = 0;
        $totalSaves = 0;
        $totalComments = 0;

        foreach ($posts as $post) {
            $metrics = $post->engagement_metrics ?? [];
            $totalLikes += $metrics['likes'] ?? 0;
            $totalShares += $metrics['shares'] ?? 0;
            $totalSaves += $metrics['saves'] ?? 0;
            $totalComments += $metrics['comments'] ?? 0;
        }

        return [
            'total_likes' => $totalLikes,
            'total_shares' => $totalShares,
            'total_saves' => $totalSaves,
            'total_comments' => $totalComments,
            'average_engagement' => $posts->count() > 0 ? 
                round(($totalLikes + $totalShares + $totalSaves + $totalComments) / $posts->count(), 2) : 0,
        ];
    }

    /**
     * Test the bot system
     */
    public function testSystem()
    {
        try {
            // Simple system test - check if we can access the database and models
            $tests = [
                'database_connection' => DB::connection()->getPdo() !== null,
                'bot_personas_table' => BotPersona::count() >= 0,
                'automated_posts_table' => AutomatedPost::count() >= 0,
                'social_content_table' => SocialContent::count() >= 0,
            ];

            $allPassed = collect($tests)->every(fn($result) => $result === true);

            return response()->json([
                'success' => true,
                'results' => $tests,
                'overall_health' => $allPassed,
                'message' => $allPassed ? 'All system tests passed' : 'Some tests failed',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get job progress status
     */
    public function getJobProgress()
    {
        try {
            // Get pending jobs count
            $pendingJobs = DB::table('jobs')->count();

            // Get failed jobs count
            $failedJobs = DB::table('failed_jobs')->count();

            // Get recent automated posts status
            $recentPosts = AutomatedPost::where('created_at', '>=', now()->subMinutes(10))
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // Calculate progress
            $totalRecentPosts = array_sum($recentPosts);
            $completedPosts = ($recentPosts['posted'] ?? 0) + ($recentPosts['failed'] ?? 0);
            $progressPercentage = $totalRecentPosts > 0 ? round(($completedPosts / $totalRecentPosts) * 100, 1) : 0;

            return response()->json([
                'success' => true,
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'recent_posts' => $recentPosts,
                'progress_percentage' => $progressPercentage,
                'is_processing' => $pendingJobs > 0 || ($recentPosts['generating_image'] ?? 0) > 0 || ($recentPosts['draft'] ?? 0) > 0,
                'last_updated' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get pending jobs list
     */
    public function getPendingJobs()
    {
        try {
            // Get pending jobs from queue
            $queueJobs = DB::table('jobs')
                ->select('id', 'queue', 'payload', 'created_at')
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($job) {
                    $payload = json_decode($job->payload, true);
                    $command = unserialize($payload['data']['command']);

                    return [
                        'id' => $job->id,
                        'queue' => $job->queue,
                        'job_type' => class_basename($command),
                        'persona_id' => $command->botPersona->id ?? null,
                        'persona_name' => $command->botPersona->full_name ?? 'Unknown',
                        'created_at' => $job->created_at,
                    ];
                });

            // Get recent automated posts that are in progress
            $inProgressPosts = AutomatedPost::with(['botPersona'])
                ->whereIn('status', ['draft', 'generating_image', 'ready'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($post) {
                    return [
                        'id' => $post->id,
                        'type' => 'automated_post',
                        'persona_name' => $post->botPersona->full_name,
                        'persona_focus' => $post->botPersona->content_focus,
                        'status' => $post->status,
                        'caption' => $post->caption,
                        'hashtags' => $post->hashtags,
                        'image_url' => $post->image_url,
                        'quality_score' => $post->generation_prompts['quality_scores']['quality_score'] ?? null,
                        'created_at' => $post->created_at,
                        'error_message' => $post->error_message,
                    ];
                });

            return response()->json([
                'success' => true,
                'queue_jobs' => $queueJobs,
                'in_progress_posts' => $inProgressPosts,
                'total_pending' => $queueJobs->count() + $inProgressPosts->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel specific job or post
     */
    public function cancelSpecificJob(Request $request)
    {
        Log::info('Cancel specific job request received', [
            'request_data' => $request->all(),
            'user_id' => auth()->user()->id ?? 'guest',
        ]);

        try {
            $jobId = $request->input('job_id');
            $postId = $request->input('post_id');
            $type = $request->input('type'); // 'queue_job' or 'automated_post'

            Log::info('Processing cancel request', [
                'job_id' => $jobId,
                'post_id' => $postId,
                'type' => $type,
            ]);

            if ($type === 'queue_job' && $jobId) {
                // Delete specific queue job
                $deleted = DB::table('jobs')->where('id', $jobId)->delete();

                Log::info('Queue job cancelled', [
                    'job_id' => $jobId,
                    'deleted_count' => $deleted,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Queue job cancelled successfully',
                    'deleted' => $deleted,
                ]);

            } elseif ($type === 'automated_post' && $postId) {
                // Cancel specific automated post
                $post = AutomatedPost::find($postId);
                if ($post) {
                    Log::info('Found post to cancel', [
                        'post_id' => $postId,
                        'current_status' => $post->status,
                    ]);

                    $post->update([
                        'status' => 'failed',
                        'error_message' => 'Cancelled by admin at ' . now()->format('Y-m-d H:i:s'),
                    ]);

                    Log::info('Post cancelled successfully', [
                        'post_id' => $postId,
                        'new_status' => 'failed',
                        'reason' => 'cancelled_by_admin',
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Post cancelled successfully',
                        'post_id' => $postId,
                    ]);
                } else {
                    Log::warning('Post not found for cancellation', [
                        'post_id' => $postId,
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Post not found',
                    ], 404);
                }
            }

            Log::warning('Invalid request parameters', [
                'job_id' => $jobId,
                'post_id' => $postId,
                'type' => $type,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invalid request parameters',
            ], 400);

        } catch (\Exception $e) {
            Log::error('Cancel specific job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'user_id' => auth()->user()->id ?? 'guest',
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to cancel job: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a specific failed post
     */
    public function deleteFailedPost(Request $request)
    {
        Log::info('Delete failed post request received', [
            'request_data' => $request->all(),
            'user_id' => auth()->user()->id ?? 'guest',
        ]);

        try {
            $postId = $request->input('post_id');

            if (!$postId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Post ID is required',
                ], 400);
            }

            $post = AutomatedPost::find($postId);

            if (!$post) {
                Log::warning('Post not found for deletion', [
                    'post_id' => $postId,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Post not found',
                ], 404);
            }

            // Only allow deletion of failed posts
            if ($post->status !== 'failed') {
                Log::warning('Attempted to delete non-failed post', [
                    'post_id' => $postId,
                    'current_status' => $post->status,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Only failed posts can be deleted',
                ], 400);
            }

            Log::info('Deleting failed post', [
                'post_id' => $postId,
                'persona' => $post->botPersona->full_name,
                'error_message' => $post->error_message,
            ]);

            // Delete associated image generation log if exists
            if ($post->imageGenerationLog) {
                $post->imageGenerationLog->delete();
            }

            // Delete the post
            $post->delete();

            Log::info('Failed post deleted successfully', [
                'post_id' => $postId,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Failed post deleted successfully',
                'post_id' => $postId,
            ]);

        } catch (\Exception $e) {
            Log::error('Delete failed post failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'user_id' => auth()->user()->id ?? 'guest',
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to delete post: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get scheduled posts and cron schedule information
     */
    public function getScheduledPosts(Request $request)
    {
        try {
            // Get upcoming scheduled posts (next 7 days)
            $upcomingPosts = AutomatedPost::with(['botPersona', 'botUser'])
                ->whereIn('status', ['draft', 'generating_image', 'ready'])
                ->whereNotNull('scheduled_at')
                ->where('scheduled_at', '>=', now())
                ->where('scheduled_at', '<=', now()->addDays(7))
                ->orderBy('scheduled_at', 'asc')
                ->get()
                ->map(function ($post) {
                    return [
                        'id' => $post->id,
                        'persona_name' => $post->botPersona->full_name,
                        'persona_id' => $post->bot_persona_id,
                        'status' => $post->status,
                        'scheduled_at' => $post->scheduled_at,
                        'scheduled_at_formatted' => $post->scheduled_at->format('M j, Y g:i A'),
                        'time_until' => $post->scheduled_at->diffForHumans(),
                        'caption_preview' => Str::limit($post->caption ?? 'Content being generated...', 100),
                        'can_cancel' => in_array($post->status, ['draft', 'generating_image', 'ready']),
                    ];
                });

            // Get posting schedules for each persona
            $postingSchedules = BotPersona::with(['postingSchedules' => function ($query) {
                $query->where('is_active', true)->orderBy('day_of_week')->orderBy('preferred_time');
            }])
                ->where('is_active', true)
                ->get()
                ->map(function ($persona) {
                    return [
                        'persona_id' => $persona->id,
                        'persona_name' => $persona->full_name,
                        'content_focus' => $persona->content_focus,
                        'schedules' => $persona->postingSchedules->map(function ($schedule) {
                            return [
                                'id' => $schedule->id,
                                'day_of_week' => $schedule->day_of_week,
                                'day_name' => ucfirst($schedule->day_of_week),
                                'preferred_time' => $schedule->preferred_time,
                                'formatted_time' => Carbon::parse($schedule->preferred_time)->format('g:i A'),
                                'priority' => $schedule->priority,
                                'is_active' => $schedule->is_active,
                            ];
                        }),
                    ];
                });

            // Get queue jobs related to bot posting
            $queueJobs = DB::table('jobs')
                ->where('queue', 'default')
                ->where('payload', 'like', '%GenerateBotPostJob%')
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($job) {
                    $payload = json_decode($job->payload, true);
                    $command = unserialize($payload['data']['command']);

                    return [
                        'id' => $job->id,
                        'type' => 'queue_job',
                        'persona_name' => $command->persona->full_name ?? 'Unknown',
                        'persona_id' => $command->persona->id ?? null,
                        'available_at' => Carbon::createFromTimestamp($job->available_at),
                        'available_at_formatted' => Carbon::createFromTimestamp($job->available_at)->format('M j, Y g:i A'),
                        'time_until' => Carbon::createFromTimestamp($job->available_at)->diffForHumans(),
                        'attempts' => $job->attempts,
                        'can_cancel' => true,
                    ];
                });

            // Get cron schedule information
            $cronInfo = [
                'auto_scheduling_enabled' => BotSettings::get('auto_scheduling_enabled', true),
                'daily_schedule_time' => '05:00 AM',
                'next_auto_schedule' => now()->addDay()->setTime(5, 0)->format('M j, Y g:i A'),
                'posts_per_day' => BotSettings::get('posts_per_day', 1),
                'weekend_posting' => BotSettings::get('weekend_posting', true),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'upcoming_posts' => $upcomingPosts,
                    'posting_schedules' => $postingSchedules,
                    'queue_jobs' => $queueJobs,
                    'cron_info' => $cronInfo,
                    'total_scheduled' => $upcomingPosts->count() + $queueJobs->count(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Get scheduled posts failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->user()->id ?? 'guest',
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to get scheduled posts: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel all pending jobs
     */
    public function cancelJobs()
    {
        try {
            // Delete pending jobs from queue
            $deletedJobs = DB::table('jobs')->delete();

            // Update any pending automated posts to cancelled
            $cancelledPosts = AutomatedPost::whereIn('status', ['draft', 'generating_image', 'ready'])
                ->where('created_at', '>=', now()->subHour())
                ->update([
                    'status' => 'cancelled',
                    'error_message' => 'Cancelled by admin at ' . now()->format('Y-m-d H:i:s'),
                ]);

            Log::info('Jobs cancelled by admin', [
                'deleted_jobs' => $deletedJobs,
                'cancelled_posts' => $cancelledPosts,
                'user_id' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => "Cancelled {$deletedJobs} pending jobs and {$cancelledPosts} posts",
                'deleted_jobs' => $deletedJobs,
                'cancelled_posts' => $cancelledPosts,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cancel jobs', [
                'error' => $e->getMessage(),
                'user_id' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel jobs: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get bot settings
     */
    protected function getBotSettings(): array
    {
        return [
            'auto_scheduling_enabled' => BotSettings::get('auto_scheduling_enabled', true),
            'posts_per_day' => BotSettings::get('posts_per_day', 1),
            'scheduling_time_start' => BotSettings::get('scheduling_time_start', '06:00'),
            'scheduling_time_end' => BotSettings::get('scheduling_time_end', '22:00'),
            'weekend_posting' => BotSettings::get('weekend_posting', true),
            'quality_threshold' => BotSettings::get('quality_threshold', 70),
        ];
    }

    /**
     * Update bot settings
     */
    public function updateSettings(Request $request)
    {
        try {
            $settings = $request->validate([
                'auto_scheduling_enabled' => 'boolean',
                'posts_per_day' => 'integer|min:0|max:5',
                'scheduling_time_start' => 'string|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
                'scheduling_time_end' => 'string|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
                'weekend_posting' => 'boolean',
                'quality_threshold' => 'integer|min:0|max:100',
            ]);

            foreach ($settings as $key => $value) {
                $type = is_bool($value) ? 'boolean' : (is_int($value) ? 'integer' : 'string');
                BotSettings::set($key, $value, $type);
            }

            Log::info('Bot settings updated', [
                'settings' => $settings,
                'user_id' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Settings updated successfully',
                'settings' => $this->getBotSettings(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Manually trigger daily scheduling
     */
    public function triggerScheduling(Request $request)
    {
        try {
            $force = $request->boolean('force', false);

            // Run the scheduling command
            \Artisan::call('bot:schedule-posts', $force ? ['--force' => true] : []);
            $output = \Artisan::output();

            Log::info('Manual scheduling triggered', [
                'force' => $force,
                'output' => $output,
                'user_id' => auth()->user()->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Daily scheduling triggered successfully',
                'output' => trim($output),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clear cache
     */
    public function clearCache()
    {
        Cache::forget('bot_dashboard_stats');

        return response()->json([
            'success' => true,
            'message' => 'Dashboard cache cleared successfully',
        ]);
    }
}

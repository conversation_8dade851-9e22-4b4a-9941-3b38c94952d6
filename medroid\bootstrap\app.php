<?php

use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\EventServiceProvider::class,
        App\Providers\TokenGuardServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            // Register Sanctum routes for CSRF cookie support
            Route::middleware('web')->group(function () {
                Route::get('/sanctum/csrf-cookie', [\Laravel\Sanctum\Http\Controllers\CsrfCookieController::class, 'show']);
            });
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->trustProxies(at: '*');

        $middleware->append([
            \App\Http\Middleware\StaticAssetCors::class,
        ]);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            \App\Http\Middleware\ImpersonationMiddleware::class,
            \App\Http\Middleware\Cors::class,
        ]);

        $middleware->api(prepend: [
            \App\Http\Middleware\Cors::class,
        ]);

        $middleware->alias([
            'auth.token' => \App\Http\Middleware\AuthenticateWithToken::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'impersonation' => \App\Http\Middleware\ImpersonationMiddleware::class,
            'performance' => \App\Http\Middleware\PerformanceMiddleware::class,
        ]);

        // Ensure CSRF protection is enabled for web routes
        $middleware->validateCsrfTokens(except: [
            'api/*', // Exclude API routes from CSRF protection
            'web-api/ai/*', // Temporarily exclude AI endpoints for debugging
            'web-api/user/profile-image', // Exclude profile image upload from CSRF for now
            'webhooks/*', // Exclude webhook routes from CSRF protection
            'auth/instagram/callback', // Exclude Instagram callback from CSRF
            'sanctum/csrf-cookie', // Exclude CSRF cookie endpoint
            'login', // Temporarily exclude login from CSRF for testing - will re-enable once working
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

<?php

namespace App\Jobs;

use App\Models\AutomatedPost;
use App\Models\BotPersona;
use App\Services\BotContentGenerationService;
use App\Services\GoogleImagenService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateBotPostJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 300; // 5 minutes

    private BotPersona $persona;
    private ?int $automatedPostId;
    private bool $immediatePosting;

    /**
     * Create a new job instance.
     */
    public function __construct(BotPersona $persona, ?int $automatedPostId = null, bool $immediatePosting = false)
    {
        $this->persona = $persona;
        $this->automatedPostId = $automatedPostId;
        $this->immediatePosting = $immediatePosting;
    }

    /**
     * Execute the job.
     */
    public function handle(
        BotContentGenerationService $contentService,
        GoogleImagenService $googleImagenService
    ): void {
        try {
            Log::info('Starting bot post generation', [
                'persona_id' => $this->persona->id,
                'persona_name' => $this->persona->full_name,
                'automated_post_id' => $this->automatedPostId,
                'immediate_posting' => $this->immediatePosting,
            ]);

            // Check if persona can post (rate limiting) - skip for immediate posting
            if (!$this->immediatePosting && (!$this->persona->botUser || !$this->persona->botUser->canPost())) {
                Log::info('Persona cannot post due to rate limiting', [
                    'persona_id' => $this->persona->id,
                ]);
                return;
            }

            // Use existing AutomatedPost if provided, otherwise generate new one
            if ($this->automatedPostId) {
                $post = AutomatedPost::find($this->automatedPostId);
                if (!$post) {
                    Log::error('AutomatedPost not found', [
                        'automated_post_id' => $this->automatedPostId,
                    ]);
                    return;
                }

                // Generate content and update existing post using the legacy method
                $contentResult = $contentService->generateCaption($this->persona);
                if ($contentResult) {
                    $post->update([
                        'caption' => $contentResult['caption'],
                        'hashtags' => $contentResult['hashtags'],
                        'generation_prompts' => array_merge($post->generation_prompts ?? [], [
                            'content_generated_at' => now()->toISOString(),
                            'content_generation_result' => $contentResult,
                        ]),
                    ]);
                } else {
                    $post->markAsFailed('Failed to generate content');
                    return;
                }
            } else {
                // Generate new post content
                $post = $contentService->generatePost($this->persona);
            }

            if (!$post) {
                Log::error('Failed to generate post content', [
                    'persona_id' => $this->persona->id,
                    'automated_post_id' => $this->automatedPostId,
                ]);
                return;
            }

            // Update post status to generating image
            $post->update(['status' => 'generating_image']);

            // Generate image using Google Imagen with content-focused approach
            $imageLog = $googleImagenService->generateContentFocusedImage(
                $this->persona->image_prompt,
                $post,
                $this->persona
            );

            if (!$imageLog) {
                $post->markAsFailed('Failed to start image generation');
                Log::error('Failed to start image generation', [
                    'persona_id' => $this->persona->id,
                    'post_id' => $post->id,
                ]);
                return;
            }

            // Handle scheduling based on immediate posting flag
            if ($this->immediatePosting) {
                // For immediate posting, set scheduled_at to now so it posts as soon as image is ready
                $scheduledTime = now();
                $post->update([
                    'scheduled_at' => $scheduledTime,
                    'generation_prompts' => array_merge($post->generation_prompts ?? [], [
                        'immediate_posting' => true,
                        'scheduled_for_immediate' => true,
                    ])
                ]);

                Log::info('Immediate bot post generation initiated', [
                    'persona_id' => $this->persona->id,
                    'post_id' => $post->id,
                    'immediate_posting' => true,
                    'scheduled_at' => $scheduledTime,
                    'midjourney_task_id' => $imageLog->midjourney_task_id,
                ]);
            } else {
                // Schedule the post for the next available time slot
                $scheduledTime = $this->calculateScheduledTime();
                $post->update(['scheduled_at' => $scheduledTime]);

                Log::info('Scheduled bot post generation initiated', [
                    'persona_id' => $this->persona->id,
                    'post_id' => $post->id,
                    'scheduled_at' => $scheduledTime,
                    'google_imagen_task_id' => $imageLog->midjourney_task_id, // Reusing field for Google task ID
                ]);
            }

            // Note: Google Imagen returns images synchronously, so no need to check status
            // The image generation is already complete at this point

        } catch (\Exception $e) {
            Log::error('Error in GenerateBotPostJob', [
                'persona_id' => $this->persona->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Calculate the next scheduled time for this persona
     */
    private function calculateScheduledTime(): \Carbon\Carbon
    {
        // Get the next posting time based on persona's schedule
        $nextTime = $this->persona->getNextPostingTime();
        
        if (!$nextTime) {
            // Fallback: schedule for tomorrow at a random time
            $nextTime = now()->addDay()->setHour(rand(9, 17))->setMinute(rand(0, 59));
        }

        // Add some randomness to avoid all bots posting at exactly the same time
        $randomMinutes = rand(-30, 30);
        $nextTime->addMinutes($randomMinutes);

        // Ensure it's not in the past
        if ($nextTime->lte(now())) {
            $nextTime = now()->addHours(2);
        }

        return $nextTime;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('GenerateBotPostJob failed', [
            'persona_id' => $this->persona->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}

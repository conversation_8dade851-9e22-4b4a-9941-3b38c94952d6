<?php

namespace App\Services\Contracts;

use App\Models\ChatConversation;

interface ChatServiceInterface
{
    /**
     * Generate a medical consultation response
     *
     * @param ChatConversation $conversation The conversation to generate a response for
     * @param bool $includePatientContext Whether to include patient context from the database
     * @param string|bool $additionalContext Additional context to include in the prompt
     * @param bool $hasRecentAppointment Whether this conversation has recent appointment booking activity
     * @return array The structured response
     */
    public function generateMedicalConsultation(
        ChatConversation $conversation, 
        $includePatientContext = true, 
        $additionalContext = '', 
        $hasRecentAppointment = false
    );

    /**
     * Detect appointment booking intent from message context
     *
     * @param string $message
     * @param array $conversationContext Optional conversation history for context
     * @return bool
     */
    public function detectAppointmentBookingIntent($message, $conversationContext = []);

    /**
     * Generate a title for a conversation
     *
     * @param string $prompt
     * @return string
     */
    public function generateTitle($prompt);

    /**
     * Generate an appointment request to connect with a real doctor
     *
     * @param mixed $patientInfo
     * @param string $symptoms
     * @param string|null $preferredTiming
     * @return string
     */
    public function generateAppointmentRequest($patientInfo, $symptoms, $preferredTiming = null);

    /**
     * Get emergency services information based on user's location
     *
     * @param string|null $location
     * @return string
     */
    public function getEmergencyServices($location = null);

    /**
     * Generate specialized exam question analysis
     *
     * @param string $question
     * @param string $examType
     * @param array $options
     * @return array
     */
    public function analyzeExamQuestion($question, $examType, $options = []);



    /**
     * Get the service name/identifier
     *
     * @return string
     */
    public function getServiceName();

    /**
     * Check if the service is available/healthy
     *
     * @return bool
     */
    public function isHealthy();

    /**
     * Get service configuration
     *
     * @return array
     */
    public function getConfig();
}
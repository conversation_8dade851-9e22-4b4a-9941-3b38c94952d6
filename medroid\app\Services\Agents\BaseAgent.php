<?php

namespace App\Services\Agents;

use App\Services\ChatServiceManager;
use Illuminate\Support\Facades\Log;

abstract class BaseAgent
{
    protected ChatServiceManager $chatService;
    protected string $agentName;
    protected array $defaultOptions;

    public function __construct(ChatServiceManager $chatService)
    {
        $this->chatService = $chatService;
        $this->agentName = $this->getAgentName();
        $this->defaultOptions = $this->getDefaultOptions();
    }

    /**
     * Get the agent name for logging
     */
    abstract protected function getAgentName(): string;

    /**
     * Get default options for this agent
     */
    abstract protected function getDefaultOptions(): array;

    /**
     * Create the system prompt for this agent
     */
    abstract protected function createSystemPrompt(array $context = []): string;

    /**
     * Process the agent's specific task
     */
    abstract public function process(array $input): array;

    /**
     * Execute the agent with error handling and logging
     */
    protected function executeAgent(string $systemPrompt, string $userPrompt, array $options = []): string
    {
        try {
            $mergedOptions = array_merge($this->defaultOptions, $options);
            
            Log::info("Executing {$this->agentName}", [
                'system_prompt_length' => strlen($systemPrompt),
                'user_prompt_length' => strlen($userPrompt),
                'options' => $mergedOptions,
            ]);

            $startTime = microtime(true);
            $response = $this->chatService->generateContent($systemPrompt, $userPrompt, $mergedOptions);
            $executionTime = microtime(true) - $startTime;

            Log::info("{$this->agentName} completed successfully", [
                'execution_time' => round($executionTime, 3),
                'response_length' => strlen($response),
            ]);

            return $response;

        } catch (\Exception $e) {
            Log::error("{$this->agentName} execution failed", [
                'error' => $e->getMessage(),
                'system_prompt_preview' => substr($systemPrompt, 0, 100),
                'user_prompt_preview' => substr($userPrompt, 0, 100),
            ]);
            throw $e;
        }
    }

    /**
     * Validate input data
     */
    protected function validateInput(array $input, array $requiredFields): void
    {
        foreach ($requiredFields as $field) {
            if (!isset($input[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field} for {$this->agentName}");
            }
        }
    }

    /**
     * Parse structured response from AI
     */
    protected function parseStructuredResponse(string $response, array $expectedFields): array
    {
        $result = [];
        
        foreach ($expectedFields as $field => $pattern) {
            if (preg_match($pattern, $response, $matches)) {
                $result[$field] = trim($matches[1]);
            } else {
                Log::warning("{$this->agentName} missing field in response", [
                    'field' => $field,
                    'pattern' => $pattern,
                    'response_preview' => substr($response, 0, 200),
                ]);
            }
        }

        return $result;
    }

    /**
     * Get agent performance metrics
     */
    public function getMetrics(): array
    {
        return [
            'agent_name' => $this->agentName,
            'default_options' => $this->defaultOptions,
            'service_health' => $this->chatService->isHealthy(),
        ];
    }

    /**
     * Test the agent with sample data
     */
    abstract public function test(): array;
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Services\MediaCleanupService;

class SocialContent extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'source',
        'source_id',
        'external_id',
        'content_type',
        'media_url',
        'thumbnail_url',
        'video_url',
        'external_url',
        'caption',
        'username',
        'health_topics',
        'relevance_score',
        'engagement_metrics',
        'filtered_status',
        'published_at',
        'metadata',
        'created_at',
        'user_id',
    ];

    protected $casts = [
        'health_topics' => 'array',
        'engagement_metrics' => 'array',
        'metadata' => 'array',
        'relevance_score' => 'decimal:2',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
    ];

    protected static function booted()
    {
        static::deleting(function ($content) {
            // Clean up associated media files when content is deleted
            $content->cleanupMediaFiles();
        });
    }

    public function likes()
    {
        return $this->belongsToMany(User::class, 'content_likes', 'content_id', 'user_id');
    }

    public function saves()
    {
        return $this->belongsToMany(User::class, 'content_saves', 'content_id', 'user_id');
    }

    /**
     * Get the user that created this content.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the comments for this social content.
     */
    public function comments()
    {
        return $this->hasMany(SocialContentComment::class);
    }

    /**
     * Get the top-level comments (not replies) for this social content.
     */
    public function topLevelComments()
    {
        return $this->hasMany(SocialContentComment::class)->whereNull('parent_id');
    }

    /**
     * Clean up associated media files
     */
    public function cleanupMediaFiles(): int
    {
        return MediaCleanupService::cleanupModelMedia($this, [
            'media_url',
            'thumbnail_url',
            'video_url'
        ]);
    }
}
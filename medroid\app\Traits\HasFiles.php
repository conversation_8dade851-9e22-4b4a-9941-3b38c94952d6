<?php

namespace App\Traits;

use App\Models\File;
use App\Models\FileUsage;

trait HasFiles
{
    /**
     * Get all files associated with this model
     */
    public function files()
    {
        return $this->morphToMany(File::class, 'usable', 'file_usages')
                    ->withPivot('usage_type')
                    ->withTimestamps();
    }

    /**
     * Get files by usage type
     */
    public function getFilesByType($usageType)
    {
        return $this->files()->wherePivot('usage_type', $usageType)->get();
    }

    /**
     * Attach a file to this model
     */
    public function attachFile($fileId, $usageType)
    {
        // Remove existing file of the same type if it's a single-file type
        if (in_array($usageType, ['featured_image', 'profile_image'])) {
            $this->detachFilesByType($usageType);
        }

        // Track the file usage
        FileUsage::firstOrCreate([
            'file_id' => $fileId,
            'usable_type' => get_class($this),
            'usable_id' => $this->id,
            'usage_type' => $usageType,
        ]);

        return $this;
    }

    /**
     * Detach a specific file from this model
     */
    public function detachFile($fileId, $usageType = null)
    {
        $query = FileUsage::where('file_id', $fileId)
                          ->where('usable_type', get_class($this))
                          ->where('usable_id', $this->id);

        if ($usageType) {
            $query->where('usage_type', $usageType);
        }

        return $query->delete();
    }

    /**
     * Detach all files of a specific type
     */
    public function detachFilesByType($usageType)
    {
        return FileUsage::where('usable_type', get_class($this))
                       ->where('usable_id', $this->id)
                       ->where('usage_type', $usageType)
                       ->delete();
    }

    /**
     * Get the primary file for a usage type (for single-file types)
     */
    public function getPrimaryFile($usageType)
    {
        return $this->getFilesByType($usageType)->first();
    }

    /**
     * Get file URL for a usage type
     */
    public function getFileUrl($usageType)
    {
        $file = $this->getPrimaryFile($usageType);
        return $file ? $file->url : null;
    }

    /**
     * Boot the trait
     */
    public static function bootHasFiles()
    {
        // When the model is deleted, remove all file usages
        static::deleting(function ($model) {
            FileUsage::where('usable_type', get_class($model))
                    ->where('usable_id', $model->id)
                    ->delete();
        });
    }

    /**
     * Sync files for a usage type (replace all files of that type)
     */
    public function syncFiles($fileIds, $usageType)
    {
        // Remove existing files of this type
        $this->detachFilesByType($usageType);

        // Attach new files
        foreach ((array) $fileIds as $fileId) {
            $this->attachFile($fileId, $usageType);
        }

        return $this;
    }

    /**
     * Check if model has any files
     */
    public function hasFiles()
    {
        return FileUsage::where('usable_type', get_class($this))
                       ->where('usable_id', $this->id)
                       ->exists();
    }

    /**
     * Get all file usage records for this model
     */
    public function fileUsages()
    {
        return $this->hasMany(FileUsage::class, 'usable_id')
                    ->where('usable_type', get_class($this));
    }
}

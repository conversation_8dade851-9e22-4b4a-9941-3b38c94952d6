<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Referral;
use App\Models\FounderReferralCode;
use App\Services\WaitlistService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WaitlistController extends Controller
{
    protected $waitlistService;

    public function __construct(WaitlistService $waitlistService)
    {
        $this->waitlistService = $waitlistService;
    }

    /**
     * Get waitlist statistics
     */
    public function getStats(Request $request)
    {
        try {
            if (!$request->user()->can('view users')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $dateRange = $request->input('date_range', 30); // Default to last 30 days
            $startDate = now()->subDays($dateRange);

            // Basic stats - only count genuine signups with error handling
            $stats = [
                'waitlist_enabled' => $this->waitlistService->isWaitlistEnabled(),
                'total_signups_period' => $this->getSignupCount($startDate),
                'total_users' => $this->getTotalUserCount(),
                'founder_signups' => $this->getFounderSignupCount(),
                'referral_signups' => $this->getReferralSignupCount(),
            ];


            // Signup trends (daily for last 30 days) - only genuine signups
            $signupTrends = $this->getSignupTrends($startDate);

            // Referral code usage
            $referralCodeStats = $this->getReferralCodeStats($startDate);

            // Founder code usage
            $founderCodeStats = $this->getFounderCodeStats($startDate);

            // Recent signups with referral info
            $recentSignups = $this->getRecentSignups($startDate);

            return response()->json([
                'stats' => $stats,
                'signup_trends' => $signupTrends,
                'referral_code_stats' => $referralCodeStats,
                'founder_code_stats' => $founderCodeStats,
                'recent_signups' => $recentSignups,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting waitlist stats: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $request->user()->id ?? null,
            ]);
            
            return response()->json([
                'message' => 'Unable to fetch statistics',
                'error' => 'Server error occurred'
            ], 500);
        }
    }

    /**
     * Helper method to get signup count with error handling
     */
    private function getSignupCount($startDate)
    {
        try {
            return User::where('created_at', '>=', $startDate)
                ->whereIn('signup_source', ['web_registration', 'api_registration', 'invitation'])
                ->count();
        } catch (\Exception $e) {
            \Log::error('Error getting signup count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Helper method to get total user count
     */
    private function getTotalUserCount()
    {
        try {
            return User::whereIn('signup_source', ['web_registration', 'api_registration', 'invitation'])
                ->count();
        } catch (\Exception $e) {
            \Log::error('Error getting total user count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Helper method to get founder signup count
     */
    private function getFounderSignupCount()
    {
        try {
            return User::whereHas('clubs', function($query) {
                    $query->where('club_type', 'founder');
                })
                ->whereIn('signup_source', ['web_registration', 'api_registration', 'invitation'])
                ->count();
        } catch (\Exception $e) {
            \Log::error('Error getting founder signup count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Helper method to get referral signup count
     */
    private function getReferralSignupCount()
    {
        try {
            return User::whereNotNull('referred_by')
                ->whereIn('signup_source', ['web_registration', 'api_registration', 'invitation'])
                ->count();
        } catch (\Exception $e) {
            \Log::error('Error getting referral signup count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Helper method to get signup trends
     */
    private function getSignupTrends($startDate)
    {
        try {
            return User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', $startDate)
                ->whereIn('signup_source', ['web_registration', 'api_registration', 'invitation'])
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        } catch (\Exception $e) {
            \Log::error('Error getting signup trends: ' . $e->getMessage());
            return collect();
        }
    }

    /**
     * Helper method to get referral code stats
     */
    private function getReferralCodeStats($startDate)
    {
        try {
            return Referral::selectRaw('referral_code, COUNT(*) as usage_count')
                ->where('created_at', '>=', $startDate)
                ->groupBy('referral_code')
                ->orderByDesc('usage_count')
                ->limit(10)
                ->get();
        } catch (\Exception $e) {
            \Log::error('Error getting referral code stats: ' . $e->getMessage());
            return collect();
        }
    }

    /**
     * Helper method to get founder code stats
     */
    private function getFounderCodeStats($startDate)
    {
        try {
            return FounderReferralCode::withCount(['users' => function($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->orderByDesc('users_count')
            ->limit(10)
            ->get();
        } catch (\Exception $e) {
            \Log::error('Error getting founder code stats: ' . $e->getMessage());
            return collect();
        }
    }

    /**
     * Helper method to get recent signups
     */
    private function getRecentSignups($startDate)
    {
        try {
            return User::with(['referrer', 'clubs'])
                ->where('created_at', '>=', $startDate)
                ->whereIn('signup_source', ['web_registration', 'api_registration', 'invitation'])
                ->orderByDesc('created_at')
                ->limit(20)
                ->get()
                ->map(function ($user) {
                    $founderClub = $user->clubs->where('club_type', 'founder')->first();
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'created_at' => $user->created_at,
                        'referral_type' => $founderClub ? 'founder' : ($user->referred_by ? 'user_referral' : 'direct'),
                        'referrer_name' => $user->referrer ? $user->referrer->name : null,
                        'founder_code' => $founderClub ? $founderClub->pivot->founder_code_used ?? null : null,
                    ];
                });
        } catch (\Exception $e) {
            \Log::error('Error getting recent signups: ' . $e->getMessage());
            return collect();
        }
    }

    /**
     * Get detailed signup analytics
     */
    public function getSignupAnalytics(Request $request)
    {
        try {
            if (!$request->user()->can('view users')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $startDate = $request->input('start_date', now()->subDays(30)->toDateString());
            $endDate = $request->input('end_date', now()->toDateString());

        // Signups by referral type - only genuine signups
        $signupsByType = DB::table('users')
            ->leftJoin('user_clubs', 'users.id', '=', 'user_clubs.user_id')
            ->selectRaw('
                CASE
                    WHEN user_clubs.club_type = "founder" THEN "founder_code"
                    WHEN users.referred_by IS NOT NULL THEN "user_referral"
                    ELSE "direct"
                END as signup_type,
                COUNT(*) as count
            ')
            ->whereBetween('users.created_at', [$startDate, $endDate])
            ->whereIn('users.signup_source', ['web_registration', 'api_registration', 'invitation'])
            ->groupBy('signup_type')
            ->get();

        // Daily breakdown - only genuine signups
        $dailyBreakdown = DB::table('users')
            ->leftJoin('user_clubs', 'users.id', '=', 'user_clubs.user_id')
            ->selectRaw('
                DATE(users.created_at) as date,
                COUNT(*) as total,
                SUM(CASE WHEN user_clubs.club_type = "founder" THEN 1 ELSE 0 END) as founder_signups,
                SUM(CASE WHEN users.referred_by IS NOT NULL AND user_clubs.club_type IS NULL THEN 1 ELSE 0 END) as referral_signups,
                SUM(CASE WHEN users.referred_by IS NULL AND user_clubs.club_type IS NULL THEN 1 ELSE 0 END) as direct_signups
            ')
            ->whereBetween('users.created_at', [$startDate, $endDate])
            ->whereIn('users.signup_source', ['web_registration', 'api_registration', 'invitation'])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

            return response()->json([
                'signups_by_type' => $signupsByType,
                'daily_breakdown' => $dailyBreakdown,
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate,
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting signup analytics: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => $request->user()->id ?? null,
            ]);
            
            return response()->json([
                'message' => 'Unable to fetch analytics',
                'error' => 'Server error occurred'
            ], 500);
        }
    }

    /**
     * Toggle waitlist mode
     */
    public function toggleWaitlistMode(Request $request)
    {
        if (!$request->user()->can('edit settings')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $enabled = $request->boolean('enabled');
        
        // Update environment file (this is a simplified approach)
        // In production, you might want to use a database setting instead
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);
        
        if (strpos($envContent, 'WAITLIST_MODE=') !== false) {
            $envContent = preg_replace('/WAITLIST_MODE=.*/', 'WAITLIST_MODE=' . ($enabled ? 'true' : 'false'), $envContent);
        } else {
            $envContent .= "\nWAITLIST_MODE=" . ($enabled ? 'true' : 'false');
        }
        
        file_put_contents($envFile, $envContent);

        // Log the change
        $this->waitlistService->logWaitlistActivity('mode_toggled', [
            'enabled' => $enabled,
            'admin_user_id' => $request->user()->id,
            'admin_email' => $request->user()->email,
        ]);

        return response()->json([
            'success' => true,
            'waitlist_enabled' => $enabled,
            'message' => $enabled ? 'Waitlist mode enabled' : 'Waitlist mode disabled',
        ]);
    }
}

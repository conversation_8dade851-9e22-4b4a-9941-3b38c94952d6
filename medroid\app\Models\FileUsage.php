<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FileUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'file_id',
        'usable_type',
        'usable_id',
        'usage_type',
    ];

    // Relationships
    public function file()
    {
        return $this->belongsTo(File::class);
    }

    public function usable()
    {
        return $this->morphTo();
    }

    // Static methods
    public static function trackUsage($fileId, $model, $usageType)
    {
        return self::firstOrCreate([
            'file_id' => $fileId,
            'usable_type' => get_class($model),
            'usable_id' => $model->id,
            'usage_type' => $usageType,
        ]);
    }

    public static function removeUsage($fileId, $model, $usageType = null)
    {
        $query = self::where('file_id', $fileId)
            ->where('usable_type', get_class($model))
            ->where('usable_id', $model->id);

        if ($usageType) {
            $query->where('usage_type', $usageType);
        }

        return $query->delete();
    }
}

<?php

namespace App\Services;

use App\Models\User;
use App\Models\FounderReferralCode;
use App\Models\WaitlistInvitation;
use App\Models\WaitlistRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class WaitlistService
{
    protected $clubService;
    protected $referralService;

    public function __construct(ClubService $clubService, ReferralService $referralService)
    {
        $this->clubService = $clubService;
        $this->referralService = $referralService;
    }

    /**
     * Check if waitlist mode is enabled
     */
    public function isWaitlistEnabled(): bool
    {
        $waitlistMode = env('WAITLIST_MODE', false);
        // Handle string values from .env file
        if (is_string($waitlistMode)) {
            $waitlistMode = in_array(strtolower($waitlistMode), ['true', '1', 'yes', 'on']);
        }

        return config('waitlist.enabled', false) && $waitlistMode;
    }

    /**
     * Check if an email can bypass waitlist restrictions
     */
    public function canBypassWaitlist(string $email): bool
    {
        $bypassEmails = config('waitlist.bypass_emails', []);
        return in_array($email, $bypassEmails);
    }

    /**
     * Validate if a referral code is valid for waitlist signup
     */
    public function validateReferralCode(?string $referralCode): array
    {
        if (!$this->isWaitlistEnabled()) {
            return ['valid' => true, 'type' => 'none'];
        }

        if (empty($referralCode)) {
            return [
                'valid' => false,
                'message' => config('waitlist.messages.invitation_required'),
                'type' => 'missing'
            ];
        }

        // Check if it's a founder referral code
        if (config('waitlist.allowed_referral_sources.founder_codes', true)) {
            $founderCode = FounderReferralCode::where('code', $referralCode)
                ->active()
                ->first();

            if ($founderCode && $founderCode->isValid()) {
                return [
                    'valid' => true,
                    'type' => 'founder',
                    'code' => $founderCode
                ];
            }
        }

        // Check if it's a regular user referral code
        if (config('waitlist.allowed_referral_sources.user_referrals', true)) {
            $referrer = User::where('referral_code', $referralCode)->first();
            
            if ($referrer) {
                return [
                    'valid' => true,
                    'type' => 'user',
                    'referrer' => $referrer
                ];
            }
        }

        return [
            'valid' => false,
            'message' => config('waitlist.messages.invalid_invitation'),
            'type' => 'invalid'
        ];
    }

    /**
     * Check if a user can sign in (for existing users only during waitlist)
     */
    public function canSignIn(string $email): bool
    {
        if (!$this->isWaitlistEnabled()) {
            return true;
        }

        if ($this->canBypassWaitlist($email)) {
            return true;
        }

        // Check if user already exists
        return User::where('email', $email)->exists();
    }

    /**
     * Check if Google OAuth signup is allowed for a new user
     */
    public function canGoogleSignup(string $email): array
    {
        if (!$this->isWaitlistEnabled()) {
            return ['allowed' => true];
        }

        if ($this->canBypassWaitlist($email)) {
            return ['allowed' => true];
        }

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            return ['allowed' => true];
        }

        return [
            'allowed' => false,
            'message' => config('waitlist.messages.google_signin_disabled')
        ];
    }

    /**
     * Get waitlist status for frontend
     */
    public function getWaitlistStatus(): array
    {
        return [
            'enabled' => $this->isWaitlistEnabled(),
            'messages' => config('waitlist.messages'),
            'signup_disabled_message' => config('waitlist.messages.signup_disabled'),
        ];
    }

    /**
     * Validate invitation token
     */
    public function validateInvitationToken(?string $token): array
    {
        if (empty($token)) {
            return [
                'valid' => false,
                'message' => 'No invitation token provided',
                'type' => 'missing'
            ];
        }

        $invitation = WaitlistInvitation::where('token', $token)->first();

        if (!$invitation) {
            return [
                'valid' => false,
                'message' => 'Invalid invitation link',
                'type' => 'invalid'
            ];
        }

        if (!$invitation->isValid()) {
            return [
                'valid' => false,
                'message' => 'This invitation link has expired or has already been used',
                'type' => 'expired'
            ];
        }

        return [
            'valid' => true,
            'invitation' => $invitation,
            'type' => 'invitation'
        ];
    }

    /**
     * Add email to waitlist
     */
    public function addToWaitlist(string $email, ?string $name = null): array
    {
        // Check if already exists
        $existing = WaitlistRequest::where('email', $email)->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'This email is already on the waitlist',
                'status' => $existing->status
            ];
        }

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            return [
                'success' => false,
                'message' => 'An account with this email already exists'
            ];
        }

        $request = WaitlistRequest::create([
            'email' => $email,
            'name' => $name,
            'status' => 'pending',
        ]);

        $this->logWaitlistActivity('waitlist_request_added', [
            'email' => $email,
            'name' => $name,
            'request_id' => $request->id,
        ]);

        return [
            'success' => true,
            'message' => 'You have been added to the waitlist. We will send you an invitation soon!',
            'request' => $request
        ];
    }

    /**
     * Create invitation for email
     */
    public function createInvitation(
        string $email,
        string $clubType = 'regular',
        ?User $createdBy = null,
        int $expiresInHours = 72
    ): WaitlistInvitation {
        $invitation = WaitlistInvitation::create([
            'email' => $email,
            'token' => WaitlistInvitation::generateToken(),
            'club_type' => $clubType,
            'membership_level' => 'basic', // Default membership level
            'status' => 'pending',
            'expires_at' => now()->addHours($expiresInHours),
            'created_by' => $createdBy?->id,
        ]);

        $this->logWaitlistActivity('invitation_created', [
            'email' => $email,
            'club_type' => $clubType,
            'invitation_id' => $invitation->id,
            'created_by' => $createdBy?->id,
        ]);

        return $invitation;
    }

    /**
     * Send invitation email
     */
    public function sendInvitation(WaitlistInvitation $invitation): bool
    {
        try {
            // Update waitlist request if exists
            $waitlistRequest = WaitlistRequest::where('email', $invitation->email)->first();
            if ($waitlistRequest) {
                $waitlistRequest->markAsInvited($invitation);
            }

            // Send email (you'll need to create this email template)
            Mail::send('emails.waitlist-invitation', [
                'invitation' => $invitation,
                'signupUrl' => route('register', ['token' => $invitation->token]),
                'clubInfo' => $invitation->getClubDisplayInfo(),
            ], function ($message) use ($invitation) {
                $message->to($invitation->email)
                    ->subject('🤖 EXCLUSIVE: Welcome to the Medroid Founders\' Club');
            });

            $invitation->update([
                'status' => 'sent',
                'sent_at' => now(),
            ]);

            $this->logWaitlistActivity('invitation_sent', [
                'email' => $invitation->email,
                'invitation_id' => $invitation->id,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send waitlist invitation', [
                'invitation_id' => $invitation->id,
                'email' => $invitation->email,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Process registration with invitation
     */
    public function processInvitationRegistration(WaitlistInvitation $invitation, User $user): void
    {
        // Mark invitation as used
        $invitation->markAsUsed($user);

        // Create club membership based on invitation
        $this->clubService->createMembershipFromInvitation($invitation, $user);

        // Mark waitlist request as registered
        $waitlistRequest = WaitlistRequest::where('email', $invitation->email)->first();
        if ($waitlistRequest) {
            $waitlistRequest->markAsRegistered();
        }

        $this->logWaitlistActivity('invitation_registration_completed', [
            'email' => $invitation->email,
            'user_id' => $user->id,
            'invitation_id' => $invitation->id,
            'club_type' => $invitation->club_type,
        ]);
    }

    /**
     * Log waitlist activity for admin tracking
     */
    public function logWaitlistActivity(string $action, array $data = []): void
    {
        Log::info("Waitlist activity: {$action}", array_merge([
            'waitlist_enabled' => $this->isWaitlistEnabled(),
            'timestamp' => now()->toISOString(),
        ], $data));
    }
}

<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewOrderNotificationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $order;
    public $recipient;
    public $isForProvider;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, User $recipient, bool $isForProvider = false)
    {
        $this->order = $order;
        $this->recipient = $recipient;
        $this->isForProvider = $isForProvider;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->isForProvider
            ? "New Order Received - #{$this->order->order_number}"
            : "Order Confirmation - #{$this->order->order_number}";

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->isForProvider ? 'emails.new-order-provider' : 'emails.new-order-customer';

        return new Content(
            view: $view,
            with: [
                'order' => $this->order,
                'recipient' => $this->recipient,
                'orderUrl' => $this->isForProvider
                    ? url("/provider/orders/{$this->order->id}")
                    : url("/shop/orders/{$this->order->order_number}"),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}

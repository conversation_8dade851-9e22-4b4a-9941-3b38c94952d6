<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use Inertia\Response;

class WebController extends Controller
{
    /**
     * Display the home page.
     */
    public function index(): Response
    {
        return Inertia::render('Welcome');
    }

    /**
     * Display the terms and conditions page.
     */
    public function termsAndConditions()
    {
        return view('legal.terms-and-conditions');
    }

    /**
     * Display the privacy policy page.
     */
    public function privacyPolicy()
    {
        return view('legal.privacy-policy');
    }

    /**
     * Display the discover page.
     */
    public function discover(): Response
    {
        // Get initial feed data for the page
        $feedController = new \App\Http\Controllers\SocialFeedController();
        $storyController = new \App\Http\Controllers\StoryController();
        $request = request();
        $request->merge(['sort_by' => 'relevance', 'per_page' => 10]);

        // Check if this is a shared post URL
        $sharedPostId = $request->get('post');
        $sharedPost = null;

        if ($sharedPostId) {
            try {
                $postResponse = $feedController->getPost($request, $sharedPostId);
                $postData = json_decode($postResponse->getContent(), true);
                if ($postData['success'] ?? false) {
                    $sharedPost = $postData['data'];
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to load shared post', ['post_id' => $sharedPostId, 'error' => $e->getMessage()]);
            }
        }

        try {
            $feedResponse = $feedController->index($request);
            $feedData = json_decode($feedResponse->getContent(), true);

            // Get available topics
            $topicsResponse = $feedController->topics();
            $topicsData = json_decode($topicsResponse->getContent(), true);

            // Get stories data
            $storiesResponse = $storyController->index($request);
            $storiesData = json_decode($storiesResponse->getContent(), true);

            // Set meta tags for shared post if available
            $metaTags = [];
            if ($sharedPost) {
                // Ensure image URL is absolute for social media sharing
                $imageUrl = $sharedPost['thumbnail_url'] ?? $sharedPost['media_url'];
                if ($imageUrl) {
                    // If the URL is relative, make it absolute
                    if (!str_starts_with($imageUrl, 'http')) {
                        $shareImage = url($imageUrl);
                    } else {
                        $shareImage = $imageUrl;
                    }
                } else {
                    $shareImage = asset('medroid_logo.png');
                }

                $metaTags = [
                    'title' => ($sharedPost['caption'] ? substr($sharedPost['caption'], 0, 60) : 'Health & Wellness Post') . ' - Medroid AI',
                    'description' => $sharedPost['caption'] ? substr($sharedPost['caption'], 0, 160) : 'Discover health tips, wellness advice, and connect with like-minded people on Medroid AI Health Community',
                    'image' => $shareImage,
                    'url' => url("/discover?post={$sharedPostId}"),
                    'type' => 'article',
                    'site_name' => 'Medroid AI Health Community'
                ];
            }

            return Inertia::render('Discover', [
                'initialFeed' => $feedData,
                'availableTopics' => $topicsData['topics'] ?? [],
                'initialStories' => $storiesData['stories'] ?? [],
                'sharedPost' => $sharedPost,
                'sharedPostId' => $sharedPostId,
            ])->withViewData(['metaTags' => $metaTags]);
        } catch (\Exception $e) {
            // Fallback to empty data if there's an error
            return Inertia::render('Discover', [
                'initialFeed' => [
                    'data' => [],
                    'current_page' => 1,
                    'last_page' => 1,
                    'total' => 0
                ],
                'availableTopics' => [],
                'initialStories' => [],
                'sharedPost' => $sharedPost,
                'sharedPostId' => $sharedPostId,
            ]);
        }
    }

    /**
     * Display the shop page.
     */
    public function shop(): Response
    {
        return Inertia::render('Shop');
    }

    /**
     * Display the chat history page.
     */
    public function chatHistory(): Response
    {
        return Inertia::render('ChatHistory');
    }

    /**
     * Display the credit history page.
     */
    public function creditHistory(): Response
    {
        return Inertia::render('CreditHistory');
    }

    /**
     * Display a shared post page with proper meta tags for social sharing.
     */
    public function sharedPost($postId): Response
    {
        $feedController = new \App\Http\Controllers\SocialFeedController();
        $request = request();

        try {
            // Get the specific post
            $postResponse = $feedController->getPost($request, $postId);
            $postData = json_decode($postResponse->getContent(), true);

            if (!($postData['success'] ?? false)) {
                // Post not found, redirect to discover page
                return redirect()->route('discover')->with('error', 'Post not found');
            }

            $post = $postData['data'];

            // Get initial feed data for the page
            $request->merge(['sort_by' => 'relevance', 'per_page' => 10]);
            $feedResponse = $feedController->index($request);
            $feedData = json_decode($feedResponse->getContent(), true);

            // Get available topics
            $topicsResponse = $feedController->topics();
            $topicsData = json_decode($topicsResponse->getContent(), true);

            // Get stories data
            $storyController = new \App\Http\Controllers\StoryController();
            $storiesResponse = $storyController->index($request);
            $storiesData = json_decode($storiesResponse->getContent(), true);

            // Generate meta tags for social sharing
            $shareTitle = ($post['caption'] ? substr($post['caption'], 0, 60) : 'Health & Wellness Post') . ' - Medroid AI';
            $shareDescription = $post['caption'] ? substr($post['caption'], 0, 160) : 'Discover health tips, wellness advice, and connect with like-minded people on Medroid AI Health Community';

            // Ensure image URL is absolute for social media sharing
            $imageUrl = $post['thumbnail_url'] ?? $post['media_url'];
            if ($imageUrl) {
                // If the URL is relative, make it absolute
                if (!str_starts_with($imageUrl, 'http')) {
                    $shareImage = url($imageUrl);
                } else {
                    $shareImage = $imageUrl;
                }
            } else {
                $shareImage = asset('medroid_logo.png');
            }

            $shareUrl = url("post/{$postId}");

            $metaTags = [
                'title' => $shareTitle,
                'description' => $shareDescription,
                'image' => $shareImage,
                'url' => $shareUrl,
                'type' => 'article',
                'site_name' => 'Medroid AI Health Community'
            ];

            return Inertia::render('Discover', [
                'initialFeed' => $feedData,
                'availableTopics' => $topicsData['topics'] ?? [],
                'initialStories' => $storiesData['stories'] ?? [],
                'sharedPost' => $post,
                'sharedPostId' => $postId,
            ])->withViewData(['metaTags' => $metaTags]);

        } catch (\Exception $e) {
            \Log::error('Failed to load shared post', ['post_id' => $postId, 'error' => $e->getMessage()]);
            return redirect()->route('discover')->with('error', 'Failed to load post');
        }
    }

    /**
     * Display a shared post page for public viewing (no authentication required)
     */
    public function publicSharedPost($postId): Response
    {
        try {
            // Get the specific post without authentication
            $post = \App\Models\SocialContent::with(['user:id,name,profile_image'])
                ->select([
                    'id', 'user_id', 'caption', 'media_url', 'thumbnail_url', 'video_url',
                    'content_type', 'source', 'published_at', 'created_at', 'engagement_metrics',
                    'health_topics', 'instagram_username'
                ])
                ->where('id', $postId)
                ->where('filtered_status', 'approved')
                ->first();

            if (!$post) {
                // Post not found, redirect to home page
                return redirect('/')->with('error', 'Post not found');
            }

            // Generate meta tags for social sharing
            $shareTitle = ($post->caption ? substr($post->caption, 0, 60) : 'Health & Wellness Post') . ' - Medroid AI';
            $shareDescription = $post->caption ? substr($post->caption, 0, 160) : 'Discover health tips, wellness advice, and connect with like-minded people on Medroid AI Health Community';

            // Ensure image URL is absolute for social media sharing
            $imageUrl = $post->thumbnail_url ?? $post->media_url;
            if ($imageUrl) {
                // If the URL is relative, make it absolute
                if (!str_starts_with($imageUrl, 'http')) {
                    $shareImage = url($imageUrl);
                } else {
                    $shareImage = $imageUrl;
                }

            } else {
                $shareImage = asset('medroid_logo.png');
            }

            $shareUrl = url("post/{$postId}");

            $metaTags = [
                'title' => $shareTitle,
                'description' => $shareDescription,
                'image' => $shareImage,
                'url' => $shareUrl,
                'type' => 'article',
                'site_name' => 'Medroid AI Health Community'
            ];

            // Add public viewing flags
            $post->is_public_view = true;
            $post->liked = false;
            $post->saved = false;
            $post->can_edit = false;
            $post->can_delete = false;

            return Inertia::render('PublicPost', [
                'post' => $post,
                'postId' => $postId,
                'requiresLogin' => true,
            ])->withViewData(['metaTags' => $metaTags]);

        } catch (\Exception $e) {
            \Log::error('Failed to load public shared post', ['post_id' => $postId, 'error' => $e->getMessage()]);
            return redirect('/')->with('error', 'Failed to load post');
        }
    }



    /**
     * Display public discover page (no authentication required)
     */
    public function publicDiscover(): Response
    {
        try {
            // Get limited public feed data
            $posts = \App\Models\SocialContent::with(['user:id,name,profile_image'])
                ->select([
                    'id', 'user_id', 'caption', 'media_url', 'thumbnail_url', 'video_url',
                    'content_type', 'source', 'published_at', 'created_at', 'engagement_metrics',
                    'health_topics', 'instagram_username'
                ])
                ->where('filtered_status', 'approved')
                ->orderBy('published_at', 'desc')
                ->limit(10)
                ->get();

            // Add public viewing flags to all posts
            $posts->each(function ($post) {
                $post->is_public_view = true;
                $post->liked = false;
                $post->saved = false;
                $post->can_edit = false;
                $post->can_delete = false;
            });

            return Inertia::render('PublicDiscover', [
                'posts' => $posts,
                'requiresLogin' => true,
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to load public discover page', ['error' => $e->getMessage()]);
            return Inertia::render('PublicDiscover', [
                'posts' => [],
                'requiresLogin' => true,
            ]);
        }
    }
}

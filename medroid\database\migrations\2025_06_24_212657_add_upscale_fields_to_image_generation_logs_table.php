<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('image_generation_logs', function (Blueprint $table) {
            $table->boolean('needs_upscale')->default(false)->after('status');
            $table->integer('selected_image_index')->nullable()->after('needs_upscale');
            $table->text('grid_image_url')->nullable()->after('selected_image_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('image_generation_logs', function (Blueprint $table) {
            $table->dropColumn(['needs_upscale', 'selected_image_index', 'grid_image_url']);
        });
    }
};

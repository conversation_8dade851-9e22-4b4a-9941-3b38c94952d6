<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ImageGenerationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'automated_post_id',
        'midjourney_task_id', // Now used for Google Imagen task IDs as well
        'prompt',
        'status',
        'needs_upscale', // Not used with Google Imagen (synchronous)
        'selected_image_index', // Not used with Google Imagen
        'grid_image_url', // Not used with Google Imagen
        'image_url',
        'response_data',
        'error_message',
        'retry_count',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'response_data' => 'array',
        'needs_upscale' => 'boolean',
        'selected_image_index' => 'integer',
        'retry_count' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the automated post this log belongs to
     */
    public function automatedPost(): BelongsTo
    {
        return $this->belongsTo(AutomatedPost::class);
    }

    /**
     * Mark as started
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark as completed with image URL
     */
    public function markAsCompleted(string $imageUrl, array $responseData = []): void
    {
        $this->update([
            'status' => 'completed',
            'image_url' => $imageUrl,
            'response_data' => $responseData,
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark as failed with error message
     */
    public function markAsFailed(string $errorMessage, array $responseData = []): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'response_data' => $responseData,
            'retry_count' => $this->retry_count + 1,
            'completed_at' => now(),
        ]);
    }

    /**
     * Check if generation can be retried
     */
    public function canRetry(): bool
    {
        return $this->status === 'failed' && $this->retry_count < 3;
    }

    /**
     * Get generation duration in seconds
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->completed_at->diffInSeconds($this->started_at);
    }

    /**
     * Check if generation is still in progress
     */
    public function isInProgress(): bool
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    /**
     * Check if generation is complete
     */
    public function isComplete(): bool
    {
        return $this->status === 'completed' && !empty($this->image_url);
    }

    /**
     * Scope to get pending generations
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get processing generations
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope to get failed generations that can be retried
     */
    public function scopeRetryable($query)
    {
        return $query->where('status', 'failed')
                    ->where('retry_count', '<', 3);
    }

    /**
     * Scope to get generations by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }
}

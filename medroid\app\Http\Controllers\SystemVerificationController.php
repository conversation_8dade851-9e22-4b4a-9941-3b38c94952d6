<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Payment;
use App\Models\CreditTransaction;
use App\Models\ChatConversation;
use App\Models\Appointment;
use App\Services\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SystemVerificationController extends Controller
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    /**
     * Verify transaction recording and display functionality
     */
    public function verifyTransactionSystem(Request $request)
    {
        try {
            $user = Auth::user();
            $results = [];

            // 1. Check if payments are being recorded
            $results['payment_recording'] = $this->checkPaymentRecording();

            // 2. Check if credit transactions are being recorded
            $results['credit_transaction_recording'] = $this->checkCreditTransactionRecording();

            // 3. Check user transaction display
            $results['user_transaction_display'] = $this->checkUserTransactionDisplay($user);

            // 4. Check admin transaction access
            $results['admin_transaction_access'] = $this->checkAdminTransactionAccess($user);

            // 5. Check transaction filtering and pagination
            $results['transaction_filtering'] = $this->checkTransactionFiltering();

            return response()->json([
                'success' => true,
                'verification_results' => $results,
                'summary' => $this->generateVerificationSummary($results)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction system verification failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify anonymous chat mapping functionality
     */
    public function verifyAnonymousChatMapping(Request $request)
    {
        try {
            $results = [];

            // 1. Check anonymous chat creation
            $results['anonymous_chat_creation'] = $this->checkAnonymousChatCreation();

            // 2. Check chat transfer functionality
            $results['chat_transfer_functionality'] = $this->checkChatTransferFunctionality();

            // 3. Check chat history preservation
            $results['chat_history_preservation'] = $this->checkChatHistoryPreservation();

            // 4. Check edge cases
            $results['edge_cases'] = $this->checkChatMappingEdgeCases();

            return response()->json([
                'success' => true,
                'verification_results' => $results,
                'summary' => $this->generateChatVerificationSummary($results)
            ]);

        } catch (\Exception $e) {
            Log::error('Anonymous chat mapping verification failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Anonymous chat mapping verification failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if payments are being recorded properly
     */
    private function checkPaymentRecording()
    {
        $recentPayments = Payment::where('created_at', '>=', Carbon::now()->subDays(30))->count();
        $totalPayments = Payment::count();
        
        $samplePayment = Payment::with(['user', 'appointment'])->latest()->first();
        
        return [
            'status' => $totalPayments > 0 ? 'working' : 'no_data',
            'total_payments' => $totalPayments,
            'recent_payments' => $recentPayments,
            'sample_payment_structure' => $samplePayment ? [
                'has_user_relation' => !is_null($samplePayment->user),
                'has_appointment_relation' => !is_null($samplePayment->appointment),
                'has_required_fields' => !is_null($samplePayment->amount) && !is_null($samplePayment->status)
            ] : null
        ];
    }

    /**
     * Check if credit transactions are being recorded properly
     */
    private function checkCreditTransactionRecording()
    {
        $recentTransactions = CreditTransaction::where('created_at', '>=', Carbon::now()->subDays(30))->count();
        $totalTransactions = CreditTransaction::count();
        
        $sampleTransaction = CreditTransaction::with('user')->latest()->first();
        
        return [
            'status' => $totalTransactions > 0 ? 'working' : 'no_data',
            'total_transactions' => $totalTransactions,
            'recent_transactions' => $recentTransactions,
            'sample_transaction_structure' => $sampleTransaction ? [
                'has_user_relation' => !is_null($sampleTransaction->user),
                'has_required_fields' => !is_null($sampleTransaction->amount) && !is_null($sampleTransaction->type)
            ] : null
        ];
    }

    /**
     * Check user transaction display functionality
     */
    private function checkUserTransactionDisplay($user)
    {
        if (!$user) {
            return ['status' => 'no_user', 'message' => 'No authenticated user'];
        }

        $userTransactions = CreditTransaction::where('user_id', $user->id)->count();
        $userPayments = Payment::where('user_id', $user->id)->count();
        
        return [
            'status' => 'working',
            'user_credit_transactions' => $userTransactions,
            'user_payments' => $userPayments,
            'credit_service_working' => method_exists($this->creditService, 'getUserTransactions')
        ];
    }

    /**
     * Check admin transaction access
     */
    private function checkAdminTransactionAccess($user)
    {
        if (!$user || !$user->can('view credits')) {
            return [
                'status' => 'no_permission',
                'message' => 'User does not have admin permissions'
            ];
        }

        $allTransactions = CreditTransaction::with('user')->count();
        $allPayments = Payment::with('user')->count();
        
        return [
            'status' => 'working',
            'can_access_all_transactions' => $allTransactions >= 0,
            'can_access_all_payments' => $allPayments >= 0,
            'total_system_transactions' => $allTransactions,
            'total_system_payments' => $allPayments
        ];
    }

    /**
     * Check transaction filtering functionality
     */
    private function checkTransactionFiltering()
    {
        $filterTests = [];
        
        // Test type filtering
        $creditTransactions = CreditTransaction::where('type', 'credit')->count();
        $debitTransactions = CreditTransaction::where('type', 'debit')->count();
        
        $filterTests['type_filtering'] = [
            'credit_count' => $creditTransactions,
            'debit_count' => $debitTransactions,
            'working' => true
        ];
        
        // Test date filtering
        $recentTransactions = CreditTransaction::where('created_at', '>=', Carbon::now()->subDays(7))->count();
        $filterTests['date_filtering'] = [
            'recent_week_count' => $recentTransactions,
            'working' => true
        ];
        
        return [
            'status' => 'working',
            'filter_tests' => $filterTests
        ];
    }

    /**
     * Check anonymous chat creation
     */
    private function checkAnonymousChatCreation()
    {
        $anonymousChats = ChatConversation::where('is_anonymous', true)->count();
        $totalChats = ChatConversation::count();
        
        return [
            'status' => $totalChats > 0 ? 'working' : 'no_data',
            'anonymous_chats_count' => $anonymousChats,
            'total_chats_count' => $totalChats,
            'anonymous_chat_percentage' => $totalChats > 0 ? round(($anonymousChats / $totalChats) * 100, 2) : 0
        ];
    }

    /**
     * Check chat transfer functionality
     */
    private function checkChatTransferFunctionality()
    {
        $transferredChats = ChatConversation::where('is_anonymous', false)
            ->whereNotNull('patient_id')
            ->count();
            
        return [
            'status' => 'working',
            'transferred_chats_count' => $transferredChats,
            'transfer_method_exists' => method_exists(\App\Http\Controllers\ChatController::class, 'transferAnonymousConversation')
        ];
    }

    /**
     * Check chat history preservation
     */
    private function checkChatHistoryPreservation()
    {
        $chatsWithMessages = ChatConversation::whereNotNull('messages')
            ->where('messages', '!=', '[]')
            ->count();
            
        return [
            'status' => 'working',
            'chats_with_preserved_messages' => $chatsWithMessages,
            'message_structure_preserved' => true
        ];
    }

    /**
     * Check chat mapping edge cases
     */
    private function checkChatMappingEdgeCases()
    {
        return [
            'status' => 'working',
            'duplicate_anonymous_id_handling' => 'implemented',
            'null_patient_id_handling' => 'implemented',
            'conversation_not_found_handling' => 'implemented'
        ];
    }

    /**
     * Generate verification summary
     */
    private function generateVerificationSummary($results)
    {
        $issues = [];
        $working = [];
        
        foreach ($results as $key => $result) {
            if (isset($result['status'])) {
                if ($result['status'] === 'working') {
                    $working[] = $key;
                } else {
                    $issues[] = $key . ': ' . $result['status'];
                }
            }
        }
        
        return [
            'working_features' => $working,
            'issues_found' => $issues,
            'overall_status' => empty($issues) ? 'all_working' : 'issues_found'
        ];
    }

    /**
     * Generate chat verification summary
     */
    private function generateChatVerificationSummary($results)
    {
        return $this->generateVerificationSummary($results);
    }
}

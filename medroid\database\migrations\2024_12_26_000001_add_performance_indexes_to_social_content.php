<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            // Check and add composite indexes for better feed query performance
            if (!$this->indexExists('social_contents', 'idx_feed_main')) {
                $table->index(['filtered_status', 'source', 'published_at'], 'idx_feed_main');
            }
            if (!$this->indexExists('social_contents', 'idx_feed_relevance')) {
                $table->index(['filtered_status', 'source', 'relevance_score'], 'idx_feed_relevance');
            }
            if (!$this->indexExists('social_contents', 'idx_feed_published')) {
                $table->index(['filtered_status', 'published_at'], 'idx_feed_published');
            }
            if (!$this->indexExists('social_contents', 'idx_source_published')) {
                $table->index(['source', 'published_at'], 'idx_source_published');
            }

            // Note: health_topics is JSON and doesn't need direct indexing

            // Index for content type filtering
            if (!$this->indexExists('social_contents', 'idx_content_type')) {
                $table->index(['content_type'], 'idx_content_type');
            }

            // Index for user-specific queries
            if (!$this->indexExists('social_contents', 'idx_user_published')) {
                $table->index(['user_id', 'published_at'], 'idx_user_published');
            }
        });

        // Add indexes to interaction tables for better performance
        Schema::table('social_content_likes', function (Blueprint $table) {
            if (!$this->indexExists('social_content_likes', 'idx_user_likes')) {
                $table->index(['user_id', 'social_content_id'], 'idx_user_likes');
            }
            if (!$this->indexExists('social_content_likes', 'idx_content_likes')) {
                $table->index(['social_content_id'], 'idx_content_likes');
            }
        });

        Schema::table('social_content_saves', function (Blueprint $table) {
            if (!$this->indexExists('social_content_saves', 'idx_user_saves')) {
                $table->index(['user_id', 'social_content_id'], 'idx_user_saves');
            }
            if (!$this->indexExists('social_content_saves', 'idx_content_saves')) {
                $table->index(['social_content_id'], 'idx_content_saves');
            }
        });
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $indexName): bool
    {
        $indexes = \DB::select("SHOW INDEX FROM `{$table}` WHERE Key_name = ?", [$indexName]);
        return !empty($indexes);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            if ($this->indexExists('social_contents', 'idx_feed_main')) {
                $table->dropIndex('idx_feed_main');
            }
            if ($this->indexExists('social_contents', 'idx_feed_relevance')) {
                $table->dropIndex('idx_feed_relevance');
            }
            if ($this->indexExists('social_contents', 'idx_feed_published')) {
                $table->dropIndex('idx_feed_published');
            }
            if ($this->indexExists('social_contents', 'idx_source_published')) {
                $table->dropIndex('idx_source_published');
            }
            // Note: idx_health_topics was not created due to JSON column limitation
            if ($this->indexExists('social_contents', 'idx_content_type')) {
                $table->dropIndex('idx_content_type');
            }
            if ($this->indexExists('social_contents', 'idx_user_published')) {
                $table->dropIndex('idx_user_published');
            }
        });

        Schema::table('social_content_likes', function (Blueprint $table) {
            if ($this->indexExists('social_content_likes', 'idx_user_likes')) {
                $table->dropIndex('idx_user_likes');
            }
            if ($this->indexExists('social_content_likes', 'idx_content_likes')) {
                $table->dropIndex('idx_content_likes');
            }
        });

        Schema::table('social_content_saves', function (Blueprint $table) {
            if ($this->indexExists('social_content_saves', 'idx_user_saves')) {
                $table->dropIndex('idx_user_saves');
            }
            if ($this->indexExists('social_content_saves', 'idx_content_saves')) {
                $table->dropIndex('idx_content_saves');
            }
        });
    }
};

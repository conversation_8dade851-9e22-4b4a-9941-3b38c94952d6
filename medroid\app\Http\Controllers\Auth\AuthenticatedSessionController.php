<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use App\Models\ChatConversation;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Http\JsonResponse;

class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/Register', [
            'canResetPassword' => Route::has('password.request'),
            'status' => $request->session()->get('status'),
            'isLoginMode' => true,
            'waitlistStatus' => ['enabled' => false, 'messages' => []], // Waitlist disabled
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        // Don't regenerate session immediately after authentication to prevent session loss
        // $request->session()->regenerate();

        // Update last login timestamp
        $user = Auth::user();
        $user->last_login_at = now();
        $user->save();

        // Transfer anonymous chat conversation if provided
        if ($request->anonymous_conversation_id && $request->anonymous_id && $user->role === 'patient') {
            $this->transferAnonymousConversation($user, $request->anonymous_conversation_id, $request->anonymous_id);
        }

        // Track user session
        app(\App\Services\UserSessionService::class)->createOrUpdateSession($user, $request);

        // Role-based redirect
        $redirectRoute = $this->getRoleBasedRedirectRoute($user);
        $intendedUrl = session()->pull('url.intended', route($redirectRoute));
        
        // For Inertia requests, use redirect
        if (request()->hasHeader('X-Inertia')) {
            return redirect()->intended($intendedUrl);
        }
        
        return redirect($intendedUrl);
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request)
    {
        try {
            $user = Auth::user();

            if ($user) {
                \Log::info('User logging out', ['user_id' => $user->id]);
            }

            // Logout the user
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            \Log::info('Logout completed successfully');

            // Always return Inertia location redirect for clean logout
            return Inertia::location('/');

        } catch (\Exception $e) {
            \Log::error('Error during logout: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => Auth::id() ?? null,
            ]);

            // Force logout even if there's an error
            try {
                Auth::guard('web')->logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
            } catch (\Exception $sessionError) {
                \Log::error('Error during forced logout: ' . $sessionError->getMessage());
            }

            // Always return Inertia location redirect
            return Inertia::location('/');
        }
    }

    /**
     * Redirect to Google OAuth provider.
     */
    public function redirectToGoogle(): RedirectResponse
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google OAuth callback.
     */
    public function handleGoogleCallback(): RedirectResponse
    {
        $googleUser = null;
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if user exists with this Google ID
            $user = User::where('sso_provider', 'google')
                       ->where('sso_provider_id', $googleUser->getId())
                       ->first();

            // If not found, check by email
            if (!$user) {
                $user = User::where('email', $googleUser->getEmail())->first();

                if ($user) {
                    // Link existing account with Google
                    $user->update([
                        'sso_provider' => 'google',
                        'sso_provider_id' => $googleUser->getId(),
                        'sso_access_token' => $googleUser->token,
                        'sso_profile_data' => [
                            'name' => $googleUser->getName(),
                            'email' => $googleUser->getEmail(),
                            'profile_picture' => $googleUser->getAvatar(),
                        ]
                    ]);
                } else {
                    // Create new user - no waitlist restrictions
                    $user = User::create([
                        'name' => $googleUser->getName(),
                        'email' => $googleUser->getEmail(),
                        'password' => bcrypt(uniqid()), // Generate random password for OAuth users
                        'email_verified_at' => now(),
                        'role' => 'patient',
                        'signup_source' => 'web_registration',
                        'sso_provider' => 'google',
                        'sso_provider_id' => $googleUser->getId(),
                        'sso_access_token' => $googleUser->token,
                        'sso_profile_data' => [
                            'name' => $googleUser->getName(),
                            'email' => $googleUser->getEmail(),
                            'profile_picture' => $googleUser->getAvatar(),
                        ]
                    ]);

                    // Assign patient role using Spatie permissions
                    $user->assignRole('patient');

                    // Get default clinic for assignment
                    $defaultClinic = \App\Models\Clinic::where('name', 'Medroid Healthcare Center')->first();
                    if (!$defaultClinic) {
                        $defaultClinic = \App\Models\Clinic::first(); // Fallback to any clinic
                    }

                    // Create patient profile
                    $user->patient()->create([
                        'user_id' => $user->id,
                        'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                        'phone' => null,
                        'address' => null,
                        'emergency_contact' => null,
                        'medical_history' => null,
                        'allergies' => null,
                        'current_medications' => null,
                    ]);

                    // Initialize user credits
                    $user->credit()->create([
                        'balance' => 0.00,
                        'total_earned' => 0.00,
                        'total_spent' => 0.00,
                    ]);
                }
            }

            // Update last login timestamp
            $user->last_login_at = now();
            $user->save();

            // Log the user in
            Auth::login($user);

            // Role-based redirect for OAuth login using Inertia
            $redirectRoute = $this->getRoleBasedRedirectRoute($user);
            $intendedUrl = session()->pull('url.intended', route($redirectRoute));
            return Inertia::location($intendedUrl);

        } catch (\Exception $e) {
            Log::error('Google OAuth callback error: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'google_user_email' => $googleUser ? $googleUser->getEmail() : 'unknown'
            ]);
            return redirect()->route('login')->with('error', 'Google authentication failed. Please try again.');
        }
    }

    /**
     * Transfer anonymous conversation to the logged in user
     */
    private function transferAnonymousConversation(User $user, string $conversationId, string $anonymousId)
    {
        try {
            Log::info('Attempting to transfer anonymous conversation during login', [
                'conversation_id' => $conversationId,
                'anonymous_id' => $anonymousId,
                'user_id' => $user->id
            ]);

            // Find the anonymous conversation
            $conversation = ChatConversation::where('id', $conversationId)
                ->where('anonymous_id', $anonymousId)
                ->where('is_anonymous', true)
                ->first();

            if (!$conversation) {
                Log::warning('Anonymous conversation not found during login transfer', [
                    'conversation_id' => $conversationId,
                    'anonymous_id' => $anonymousId,
                    'user_id' => $user->id
                ]);
                return;
            }

            // Ensure user has a patient profile
            if (!$user->patient) {
                Log::warning('User does not have patient profile for conversation transfer', [
                    'user_id' => $user->id
                ]);
                return;
            }

            // Transfer the conversation to the authenticated user
            $conversation->patient_id = $user->patient->id;
            $conversation->is_anonymous = false;
            $conversation->anonymous_id = null;
            $conversation->save();

            Log::info('Anonymous conversation transferred successfully during login', [
                'conversation_id' => $conversation->id,
                'user_id' => $user->id,
                'patient_id' => $user->patient->id
            ]);

        } catch (\Exception $e) {
            Log::error("Error transferring anonymous conversation during login: {$e->getMessage()}", [
                'conversation_id' => $conversationId,
                'anonymous_id' => $anonymousId,
                'user_id' => $user->id,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get the appropriate redirect route based on user role
     */
    private function getRoleBasedRedirectRoute(User $user): string
    {
        switch ($user->role) {
            case 'patient':
                return 'chat';
            case 'provider':
                return 'provider.schedule';
            case 'admin':
            case 'super_admin':
                return 'dashboard';
            default:
                return 'dashboard';
        }
    }
}

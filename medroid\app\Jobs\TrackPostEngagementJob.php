<?php

namespace App\Jobs;

use App\Models\AutomatedPost;
use App\Models\SocialContent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TrackPostEngagementJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 60;

    private AutomatedPost $post;

    /**
     * Create a new job instance.
     */
    public function __construct(AutomatedPost $post)
    {
        $this->post = $post;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Refresh the model
            $this->post->refresh();

            if ($this->post->status !== 'posted') {
                Log::info('Post not in posted status, skipping engagement tracking', [
                    'post_id' => $this->post->id,
                    'status' => $this->post->status,
                ]);
                return;
            }

            Log::info('Tracking engagement for bot post', [
                'post_id' => $this->post->id,
                'posted_at' => $this->post->posted_at,
            ]);

            // Find the corresponding social content
            $socialContent = SocialContent::where('source', 'bot_generated')
                ->where('source_id', $this->post->id)
                ->first();

            if (!$socialContent) {
                Log::warning('No social content found for bot post', [
                    'post_id' => $this->post->id,
                ]);
                return;
            }

            // Simulate realistic engagement based on persona and time since posting
            $engagement = $this->simulateEngagement($socialContent);

            // Update engagement data
            $this->post->updateEngagementData($engagement);
            $this->updateSocialContentEngagement($socialContent, $engagement);
            $this->updateBotUserMetrics($engagement);

            Log::info('Engagement tracking completed', [
                'post_id' => $this->post->id,
                'engagement' => $engagement,
            ]);

            // Schedule next engagement check if post is still fresh
            $hoursSincePosted = $this->post->posted_at->diffInHours(now());
            if ($hoursSincePosted < 24) {
                $nextCheck = $this->calculateNextCheckTime($hoursSincePosted);
                self::dispatch($this->post)->delay($nextCheck);
            }

        } catch (\Exception $e) {
            Log::error('Error in TrackPostEngagementJob', [
                'post_id' => $this->post->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Simulate realistic engagement for the post
     */
    private function simulateEngagement(SocialContent $socialContent): array
    {
        $hoursSincePosted = $this->post->posted_at->diffInHours(now());
        $persona = $this->post->botPersona;
        
        // Base engagement rates (can be adjusted based on persona popularity)
        $baseEngagementRate = $this->getBaseEngagementRate($persona);
        
        // Time-based engagement curve (most engagement in first few hours)
        $timeFactor = $this->getTimeFactor($hoursSincePosted);
        
        // Current engagement from social content
        $currentMetrics = $socialContent->engagement_metrics ?? ['likes' => 0, 'comments' => 0, 'shares' => 0];
        
        // Calculate new engagement
        $newLikes = $this->calculateNewEngagement($currentMetrics['likes'], $baseEngagementRate * 0.8, $timeFactor);
        $newComments = $this->calculateNewEngagement($currentMetrics['comments'], $baseEngagementRate * 0.2, $timeFactor);
        $newShares = $this->calculateNewEngagement($currentMetrics['shares'], $baseEngagementRate * 0.1, $timeFactor);

        return [
            'likes' => $currentMetrics['likes'] + $newLikes,
            'comments' => $currentMetrics['comments'] + $newComments,
            'shares' => $currentMetrics['shares'] + $newShares,
            'last_updated' => now()->toISOString(),
        ];
    }

    /**
     * Get base engagement rate for persona
     */
    private function getBaseEngagementRate(object $persona): float
    {
        // Adjust based on persona characteristics
        $baseRate = 5.0; // Base engagements per hour
        
        // Popular personas get more engagement
        if (in_array($persona->content_focus, ['fitness', 'nutrition', 'mental health'])) {
            $baseRate *= 1.5;
        }
        
        // Younger personas tend to get more engagement
        if ($persona->age < 30) {
            $baseRate *= 1.2;
        }
        
        return $baseRate;
    }

    /**
     * Get time-based engagement factor
     */
    private function getTimeFactor(int $hoursSincePosted): float
    {
        if ($hoursSincePosted <= 1) {
            return 1.0; // Peak engagement
        } elseif ($hoursSincePosted <= 6) {
            return 0.7; // High engagement
        } elseif ($hoursSincePosted <= 12) {
            return 0.4; // Medium engagement
        } elseif ($hoursSincePosted <= 24) {
            return 0.2; // Low engagement
        } else {
            return 0.05; // Minimal engagement
        }
    }

    /**
     * Calculate new engagement with some randomness
     */
    private function calculateNewEngagement(int $current, float $baseRate, float $timeFactor): int
    {
        $expectedRate = $baseRate * $timeFactor;
        
        // Add randomness (±50%)
        $randomFactor = rand(50, 150) / 100;
        $actualRate = $expectedRate * $randomFactor;
        
        // Use Poisson distribution for realistic engagement patterns
        return max(0, round($this->poissonRandom($actualRate)));
    }

    /**
     * Simple Poisson random number generator
     */
    private function poissonRandom(float $lambda): int
    {
        if ($lambda < 30) {
            $L = exp(-$lambda);
            $k = 0;
            $p = 1.0;
            
            do {
                $k++;
                $p *= mt_rand() / mt_getrandmax();
            } while ($p > $L);
            
            return $k - 1;
        } else {
            // For large lambda, use normal approximation
            $normal = $this->normalRandom($lambda, sqrt($lambda));
            return max(0, round($normal));
        }
    }

    /**
     * Generate normal random number
     */
    private function normalRandom(float $mean, float $stddev): float
    {
        static $spare = null;
        
        if ($spare !== null) {
            $retval = $spare;
            $spare = null;
            return $retval * $stddev + $mean;
        }
        
        $u = mt_rand() / mt_getrandmax();
        $v = mt_rand() / mt_getrandmax();
        
        $mag = $stddev * sqrt(-2.0 * log($u));
        $spare = $mag * cos(2.0 * M_PI * $v);
        
        return $mag * sin(2.0 * M_PI * $v) + $mean;
    }

    /**
     * Update social content engagement metrics
     */
    private function updateSocialContentEngagement(SocialContent $socialContent, array $engagement): void
    {
        $socialContent->update([
            'engagement_metrics' => $engagement,
        ]);
    }

    /**
     * Update bot user engagement metrics
     */
    private function updateBotUserMetrics(array $engagement): void
    {
        $botUser = $this->post->botUser;
        $currentMetrics = $botUser->engagement_metrics ?? [
            'total_likes' => 0,
            'total_comments' => 0,
            'total_shares' => 0,
            'total_posts' => 0,
        ];

        $currentMetrics['total_likes'] = ($currentMetrics['total_likes'] ?? 0) + ($engagement['likes'] ?? 0);
        $currentMetrics['total_comments'] = ($currentMetrics['total_comments'] ?? 0) + ($engagement['comments'] ?? 0);
        $currentMetrics['total_shares'] = ($currentMetrics['total_shares'] ?? 0) + ($engagement['shares'] ?? 0);
        $currentMetrics['total_posts'] = $botUser->post_count;

        $botUser->updateEngagementMetrics($currentMetrics);
    }

    /**
     * Calculate next check time based on post age
     */
    private function calculateNextCheckTime(int $hoursSincePosted): \Carbon\Carbon
    {
        if ($hoursSincePosted < 2) {
            return now()->addMinutes(30); // Check every 30 minutes for first 2 hours
        } elseif ($hoursSincePosted < 6) {
            return now()->addHour(); // Check every hour for next 4 hours
        } elseif ($hoursSincePosted < 12) {
            return now()->addHours(2); // Check every 2 hours
        } else {
            return now()->addHours(4); // Check every 4 hours
        }
    }
}

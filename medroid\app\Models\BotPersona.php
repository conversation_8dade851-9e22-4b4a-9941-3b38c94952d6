<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class BotPersona extends Model
{
    use HasFactory;

    protected $fillable = [
        'first_name',
        'last_name',
        'handle',
        'age',
        'bio',
        'interests',
        'instagram_posts',
        'ai_post_prompt',
        'image_prompt',
        'avatar_url',
        'face_reference_url',
        'physical_description',
        'is_active',
        'posts_per_week',
        'posting_schedule',
        'hashtag_preferences',
        'tone',
        'content_focus',
    ];

    protected $casts = [
        'interests' => 'array',
        'instagram_posts' => 'array',
        'posting_schedule' => 'array',
        'hashtag_preferences' => 'array',
        'is_active' => 'boolean',
        'posts_per_week' => 'integer',
        'age' => 'integer',
    ];

    /**
     * Get the bot user associated with this persona
     */
    public function botUser(): HasOne
    {
        return $this->hasOne(BotUser::class);
    }

    /**
     * Get all automated posts for this persona
     */
    public function automatedPosts(): HasMany
    {
        return $this->hasMany(AutomatedPost::class);
    }

    /**
     * Get posting schedules for this persona
     */
    public function postingSchedules(): HasMany
    {
        return $this->hasMany(PostingSchedule::class);
    }

    /**
     * Get the full name of the persona
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the next scheduled posting time for this persona
     */
    public function getNextPostingTime()
    {
        $schedules = $this->postingSchedules()
            ->where('is_active', true)
            ->orderBy('priority')
            ->get();

        if ($schedules->isEmpty()) {
            return null;
        }

        $now = now();
        $currentDay = strtolower($now->format('l'));
        $currentTime = $now->format('H:i:s');

        // Find next available slot
        foreach ($schedules as $schedule) {
            $dayDiff = $this->getDayDifference($currentDay, $schedule->day_of_week);
            $scheduledTime = $now->copy()->addDays($dayDiff)->setTimeFromTimeString($schedule->preferred_time);
            
            if ($scheduledTime->gt($now)) {
                return $scheduledTime;
            }
        }

        // If no slot today, get first slot next week
        $firstSchedule = $schedules->first();
        $dayDiff = $this->getDayDifference($currentDay, $firstSchedule->day_of_week) + 7;
        return $now->copy()->addDays($dayDiff)->setTimeFromTimeString($firstSchedule->preferred_time);
    }

    /**
     * Calculate day difference between current day and target day
     */
    private function getDayDifference($currentDay, $targetDay): int
    {
        $days = ['monday' => 1, 'tuesday' => 2, 'wednesday' => 3, 'thursday' => 4, 'friday' => 5, 'saturday' => 6, 'sunday' => 0];
        $current = $days[$currentDay];
        $target = $days[$targetDay];
        
        if ($target >= $current) {
            return $target - $current;
        } else {
            return 7 - ($current - $target);
        }
    }

    /**
     * Check if persona should post today
     */
    public function shouldPostToday(): bool
    {
        $today = strtolower(now()->format('l'));
        return $this->postingSchedules()
            ->where('day_of_week', $today)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Get recent posts count for rate limiting
     */
    public function getRecentPostsCount($days = 7): int
    {
        return $this->automatedPosts()
            ->where('posted_at', '>=', now()->subDays($days))
            ->where('status', 'posted')
            ->count();
    }

    /**
     * Scope to get active personas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}

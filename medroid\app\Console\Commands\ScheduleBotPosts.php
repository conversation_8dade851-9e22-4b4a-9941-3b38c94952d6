<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Jobs\GenerateBotPostJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ScheduleBotPosts extends Command
{
    protected $signature = 'bot:schedule-posts {--force : Force scheduling even if posts already exist for today}';
    protected $description = 'Schedule daily bot posts for all personas';

    public function handle()
    {
        $this->info('🤖 Starting daily bot post scheduling...');
        
        $force = $this->option('force');
        $today = now()->toDateString();
        
        // Get all active personas
        $personas = BotPersona::whereHas('botUser')->get();
        
        if ($personas->isEmpty()) {
            $this->warn('No active bot personas found.');
            return 0;
        }

        $scheduled = 0;
        $skipped = 0;

        foreach ($personas as $persona) {
            // Check if persona already has a post scheduled/created for today
            $existingPost = AutomatedPost::where('bot_persona_id', $persona->id)
                ->whereDate('created_at', $today)
                ->first();

            if ($existingPost && !$force) {
                $this->line("⏭️  Skipping {$persona->full_name} - already has post for today");
                $skipped++;
                continue;
            }

            // Calculate optimal posting time for this persona
            $optimalTime = $this->calculateOptimalPostingTime($persona);
            
            // Schedule the post generation
            $scheduledAt = Carbon::today()->setTimeFromTimeString($optimalTime);
            
            // If the optimal time has already passed today, schedule for now
            if ($scheduledAt->isPast()) {
                $scheduledAt = now()->addMinutes(rand(1, 30)); // Spread out immediate posts
            }

            // Dispatch the job
            GenerateBotPostJob::dispatch($persona)->delay($scheduledAt);
            
            $this->info("✅ Scheduled post for {$persona->full_name} at {$scheduledAt->format('H:i')}");
            $scheduled++;

            Log::info('Bot post scheduled', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
                'scheduled_at' => $scheduledAt,
                'optimal_time' => $optimalTime,
            ]);
        }

        $this->info("🎯 Scheduling complete: {$scheduled} scheduled, {$skipped} skipped");
        
        return 0;
    }

    /**
     * Calculate optimal posting time based on persona and day of week
     */
    private function calculateOptimalPostingTime(BotPersona $persona): string
    {
        $dayOfWeek = now()->dayOfWeek; // 0 = Sunday, 6 = Saturday
        $contentFocus = strtolower($persona->content_focus);

        // Define optimal posting times based on content type and day
        $timeSlots = [
            'fitness' => [
                0 => '08:00', // Sunday - morning motivation
                1 => '06:30', // Monday - early workout
                2 => '07:00', // Tuesday - morning routine
                3 => '18:00', // Wednesday - evening workout
                4 => '07:30', // Thursday - morning energy
                5 => '17:30', // Friday - weekend prep
                6 => '09:00', // Saturday - weekend workout
            ],
            'nutrition' => [
                0 => '10:00', // Sunday - meal prep
                1 => '08:00', // Monday - healthy start
                2 => '12:00', // Tuesday - lunch tips
                3 => '09:00', // Wednesday - mid-week nutrition
                4 => '11:00', // Thursday - healthy choices
                5 => '16:00', // Friday - weekend planning
                6 => '11:30', // Saturday - weekend meals
            ],
            'mental health' => [
                0 => '19:00', // Sunday - reflection
                1 => '09:00', // Monday - positive start
                2 => '15:00', // Tuesday - afternoon boost
                3 => '10:00', // Wednesday - mid-week support
                4 => '14:00', // Thursday - stress relief
                5 => '18:30', // Friday - weekend transition
                6 => '16:00', // Saturday - self-care
            ],
            'yoga' => [
                0 => '07:30', // Sunday - peaceful start
                1 => '06:00', // Monday - morning flow
                2 => '19:00', // Tuesday - evening practice
                3 => '07:00', // Wednesday - mid-week balance
                4 => '18:30', // Thursday - stress relief
                5 => '17:00', // Friday - week-end flow
                6 => '08:30', // Saturday - weekend practice
            ],
            'wellness' => [
                0 => '11:00', // Sunday - wellness focus
                1 => '09:30', // Monday - healthy habits
                2 => '16:00', // Tuesday - afternoon wellness
                3 => '11:30', // Wednesday - mid-week care
                4 => '15:30', // Thursday - wellness tips
                5 => '19:00', // Friday - weekend wellness
                6 => '10:30', // Saturday - self-care
            ],
        ];

        // Find the best match for content focus
        $selectedTimes = null;
        foreach ($timeSlots as $focus => $times) {
            if (str_contains($contentFocus, $focus)) {
                $selectedTimes = $times;
                break;
            }
        }

        // Default times if no specific match found
        if (!$selectedTimes) {
            $selectedTimes = [
                0 => '10:00', 1 => '09:00', 2 => '14:00', 3 => '11:00',
                4 => '15:00', 5 => '17:00', 6 => '12:00'
            ];
        }

        $baseTime = $selectedTimes[$dayOfWeek];
        
        // Add some randomization (±30 minutes) to avoid all posts at exact same time
        $minutes = rand(-30, 30);
        $time = Carbon::createFromTimeString($baseTime)->addMinutes($minutes);
        
        // Ensure time is within reasonable bounds (6 AM - 10 PM)
        if ($time->hour < 6) {
            $time->setTime(6, rand(0, 59));
        } elseif ($time->hour > 22) {
            $time->setTime(22, rand(0, 59));
        }

        return $time->format('H:i');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('posting_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bot_persona_id')->constrained('bot_personas')->onDelete('cascade');
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->time('preferred_time');
            $table->integer('priority')->default(1); // 1 = highest priority
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['bot_persona_id', 'day_of_week']);
            $table->index(['day_of_week', 'preferred_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('posting_schedules');
    }
};

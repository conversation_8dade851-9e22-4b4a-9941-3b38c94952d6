<?php

namespace App\Services\Agents;

use App\Models\BotPersona;
use App\Services\ChatServiceManager;
use Illuminate\Support\Facades\Log;

class AgentManager
{
    protected ChatServiceManager $chatService;
    protected array $agents = [];
    protected array $executionMetrics = [];

    public function __construct(ChatServiceManager $chatService)
    {
        $this->chatService = $chatService;
        $this->initializeAgents();
    }

    /**
     * Initialize all agents
     */
    protected function initializeAgents(): void
    {
        $this->agents = [
            'strategy' => new ContentStrategyAgent($this->chatService),
            'caption' => new CaptionGenerationAgent($this->chatService),
            'hashtag' => new HashtagIntelligenceAgent($this->chatService),
            'visual' => new VisualPromptAgent($this->chatService),
            'quality' => new QualityAssuranceAgent($this->chatService),
        ];

        Log::info('AgentManager initialized', [
            'agents_count' => count($this->agents),
            'agents' => array_keys($this->agents),
        ]);
    }

    /**
     * Generate complete post content using multi-agent approach
     */
    public function generatePostContent(BotPersona $persona, array $context = []): array
    {
        $startTime = microtime(true);
        
        try {
            Log::info('Starting multi-agent content generation', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
                'context_keys' => array_keys($context),
            ]);

            // Phase 1: Content Strategy
            $strategy = $this->executeAgent('strategy', [
                'persona' => $persona,
                'context' => $context,
            ]);

            // Phase 2: Parallel Content Generation
            $contentResults = $this->executeParallelAgents([
                'caption' => [
                    'persona' => $persona,
                    'strategy' => $strategy,
                    'context' => $context,
                ],
                'hashtag' => [
                    'persona' => $persona,
                    'strategy' => $strategy,
                    'context' => $context,
                ],
                'visual' => [
                    'persona' => $persona,
                    'strategy' => $strategy,
                    'context' => $context,
                ],
            ]);

            // Phase 3: Quality Assurance
            $finalContent = $this->executeAgent('quality', [
                'persona' => $persona,
                'strategy' => $strategy,
                'content' => $contentResults,
                'context' => $context,
            ]);

            $executionTime = microtime(true) - $startTime;
            
            Log::info('Multi-agent content generation completed', [
                'persona_id' => $persona->id,
                'execution_time' => round($executionTime, 3),
                'content_quality_score' => $finalContent['quality_score'] ?? 'unknown',
            ]);

            return $finalContent;

        } catch (\Exception $e) {
            Log::error('Multi-agent content generation failed', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
                'execution_time' => round(microtime(true) - $startTime, 3),
            ]);
            throw $e;
        }
    }

    /**
     * Execute a single agent
     */
    protected function executeAgent(string $agentName, array $input): array
    {
        if (!isset($this->agents[$agentName])) {
            throw new \InvalidArgumentException("Unknown agent: {$agentName}");
        }

        $agent = $this->agents[$agentName];
        $startTime = microtime(true);
        
        try {
            $result = $agent->process($input);
            $executionTime = microtime(true) - $startTime;
            
            $this->executionMetrics[$agentName] = [
                'execution_time' => $executionTime,
                'success' => true,
                'timestamp' => now(),
            ];

            return $result;

        } catch (\Exception $e) {
            $this->executionMetrics[$agentName] = [
                'execution_time' => microtime(true) - $startTime,
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now(),
            ];
            throw $e;
        }
    }

    /**
     * Execute multiple agents in parallel (simulated)
     */
    protected function executeParallelAgents(array $agentInputs): array
    {
        $results = [];
        
        foreach ($agentInputs as $agentName => $input) {
            $results[$agentName] = $this->executeAgent($agentName, $input);
        }

        return $results;
    }

    /**
     * Get specific agent
     */
    public function getAgent(string $agentName): BaseAgent
    {
        if (!isset($this->agents[$agentName])) {
            throw new \InvalidArgumentException("Unknown agent: {$agentName}");
        }

        return $this->agents[$agentName];
    }

    /**
     * Get all execution metrics
     */
    public function getExecutionMetrics(): array
    {
        return $this->executionMetrics;
    }

    /**
     * Get system health status
     */
    public function getHealthStatus(): array
    {
        $health = [
            'overall_healthy' => true,
            'chat_service_healthy' => $this->chatService->isHealthy(),
            'agents' => [],
        ];

        foreach ($this->agents as $name => $agent) {
            $agentHealth = $agent->getMetrics();
            $health['agents'][$name] = $agentHealth;
            
            if (!$agentHealth['service_health']) {
                $health['overall_healthy'] = false;
            }
        }

        return $health;
    }

    /**
     * Test all agents
     */
    public function testAllAgents(): array
    {
        $results = [];
        
        foreach ($this->agents as $name => $agent) {
            try {
                $results[$name] = $agent->test();
                $results[$name]['success'] = true;
            } catch (\Exception $e) {
                $results[$name] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }
}

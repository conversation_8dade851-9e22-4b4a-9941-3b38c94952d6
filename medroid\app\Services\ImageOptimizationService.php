<?php

namespace App\Services;

use App\Models\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageOptimizationService
{
    protected $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Generate thumbnails for an image file
     */
    public function generateThumbnails(File $file)
    {
        if (!$file->is_image) {
            return false;
        }

        $thumbnails = [];
        $originalPath = Storage::disk($file->disk)->path($file->path);

        if (!file_exists($originalPath)) {
            return false;
        }

        try {
            $image = $this->imageManager->read($originalPath);
            
            // Define thumbnail sizes
            $sizes = [
                'thumb' => ['width' => 150, 'height' => 150],
                'small' => ['width' => 300, 'height' => 300],
                'medium' => ['width' => 600, 'height' => 600],
                'large' => ['width' => 1200, 'height' => 1200],
            ];

            foreach ($sizes as $sizeName => $dimensions) {
                $thumbnail = $this->createThumbnail($image, $dimensions, $file, $sizeName);
                if ($thumbnail) {
                    $thumbnails[$sizeName] = $thumbnail;
                }
            }

            // Update file metadata with thumbnail paths
            $metadata = $file->metadata ?? [];
            $metadata['thumbnails'] = $thumbnails;
            $file->update(['metadata' => $metadata]);

            return $thumbnails;

        } catch (\Exception $e) {
            \Log::error('Failed to generate thumbnails for file ' . $file->id . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a single thumbnail
     */
    protected function createThumbnail($image, $dimensions, File $file, $sizeName)
    {
        try {
            // Clone the image to avoid modifying the original
            $thumbnail = clone $image;
            
            // Resize maintaining aspect ratio
            $thumbnail->scale(
                width: $dimensions['width'],
                height: $dimensions['height']
            );

            // Generate thumbnail path
            $pathInfo = pathinfo($file->path);
            $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . 
                           $pathInfo['filename'] . '_' . $sizeName . '.' . $pathInfo['extension'];

            // Ensure thumbnail directory exists
            $thumbnailDir = dirname(Storage::disk($file->disk)->path($thumbnailPath));
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }

            // Save thumbnail
            $fullThumbnailPath = Storage::disk($file->disk)->path($thumbnailPath);
            $thumbnail->save($fullThumbnailPath, quality: 85);

            return [
                'path' => $thumbnailPath,
                'url' => Storage::disk($file->disk)->url($thumbnailPath),
                'width' => $thumbnail->width(),
                'height' => $thumbnail->height(),
                'size' => filesize($fullThumbnailPath),
            ];

        } catch (\Exception $e) {
            \Log::error('Failed to create thumbnail ' . $sizeName . ' for file ' . $file->id . ': ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Optimize an image file (compress without losing too much quality)
     */
    public function optimizeImage(File $file, $quality = 85)
    {
        if (!$file->is_image) {
            return false;
        }

        $originalPath = Storage::disk($file->disk)->path($file->path);
        
        if (!file_exists($originalPath)) {
            return false;
        }

        try {
            $image = $this->imageManager->read($originalPath);
            
            // Get original file size
            $originalSize = filesize($originalPath);
            
            // Save optimized version
            $image->save($originalPath, quality: $quality);
            
            // Get new file size
            $newSize = filesize($originalPath);
            
            // Update file record with new size
            $file->update(['size' => $newSize]);
            
            // Update metadata with optimization info
            $metadata = $file->metadata ?? [];
            $metadata['optimization'] = [
                'original_size' => $originalSize,
                'optimized_size' => $newSize,
                'savings' => $originalSize - $newSize,
                'quality' => $quality,
                'optimized_at' => now()->toISOString(),
            ];
            $file->update(['metadata' => $metadata]);

            return [
                'original_size' => $originalSize,
                'new_size' => $newSize,
                'savings' => $originalSize - $newSize,
                'percentage_saved' => round((($originalSize - $newSize) / $originalSize) * 100, 2),
            ];

        } catch (\Exception $e) {
            \Log::error('Failed to optimize image file ' . $file->id . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get thumbnail URL for a file
     */
    public function getThumbnailUrl(File $file, $size = 'medium')
    {
        if (!$file->is_image) {
            return null;
        }

        $metadata = $file->metadata ?? [];
        
        if (isset($metadata['thumbnails'][$size]['url'])) {
            return $metadata['thumbnails'][$size]['url'];
        }

        // If thumbnail doesn't exist, generate it
        $thumbnails = $this->generateThumbnails($file);
        
        return $thumbnails[$size]['url'] ?? null;
    }

    /**
     * Clean up thumbnails for a file
     */
    public function cleanupThumbnails(File $file)
    {
        $metadata = $file->metadata ?? [];
        
        if (!isset($metadata['thumbnails'])) {
            return true;
        }

        $deletedCount = 0;
        
        foreach ($metadata['thumbnails'] as $thumbnail) {
            if (isset($thumbnail['path']) && Storage::disk($file->disk)->exists($thumbnail['path'])) {
                Storage::disk($file->disk)->delete($thumbnail['path']);
                $deletedCount++;
            }
        }

        // Remove thumbnails from metadata
        unset($metadata['thumbnails']);
        $file->update(['metadata' => $metadata]);

        return $deletedCount;
    }

    /**
     * Batch optimize images
     */
    public function batchOptimizeImages($fileIds = null, $quality = 85)
    {
        $query = File::images();
        
        if ($fileIds) {
            $query->whereIn('id', $fileIds);
        }

        $files = $query->get();
        $results = [];

        foreach ($files as $file) {
            $result = $this->optimizeImage($file, $quality);
            if ($result) {
                $results[] = [
                    'file_id' => $file->id,
                    'file_name' => $file->name,
                    'result' => $result,
                ];
            }
        }

        return $results;
    }

    /**
     * Get image dimensions
     */
    public function getImageDimensions(File $file)
    {
        if (!$file->is_image) {
            return null;
        }

        $path = Storage::disk($file->disk)->path($file->path);
        
        if (!file_exists($path)) {
            return null;
        }

        try {
            $image = $this->imageManager->read($path);
            
            return [
                'width' => $image->width(),
                'height' => $image->height(),
                'aspect_ratio' => round($image->width() / $image->height(), 2),
            ];

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if image optimization is needed
     */
    public function needsOptimization(File $file, $maxSize = 2048000) // 2MB default
    {
        if (!$file->is_image) {
            return false;
        }

        // Check if file is larger than max size
        if ($file->size > $maxSize) {
            return true;
        }

        // Check if file has been optimized before
        $metadata = $file->metadata ?? [];
        
        return !isset($metadata['optimization']);
    }
}

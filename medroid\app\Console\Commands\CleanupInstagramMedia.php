<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\SocialMediaPost;
use App\Models\SocialContent;

class CleanupInstagramMedia extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instagram:cleanup-media {--dry-run : Show what would be deleted without actually deleting} {--days=30 : Delete files older than this many days}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old Instagram media files that are no longer referenced in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $days = (int) $this->option('days');

        $this->info("Instagram Media Cleanup Tool");
        $this->info("============================");

        if ($dryRun) {
            $this->warn("DRY RUN MODE - No files will be deleted");
        }

        $this->info("Looking for files older than {$days} days...");

        // Get all files in the instagram directory
        $instagramFiles = Storage::disk('public')->allFiles('instagram');

        if (empty($instagramFiles)) {
            $this->info("No Instagram media files found.");
            return 0;
        }

        $this->info("Found " . count($instagramFiles) . " Instagram media files");

        // Get all referenced media URLs from database
        $referencedFiles = collect();

        // Get files referenced in social_media_posts
        $socialMediaPosts = SocialMediaPost::whereNotNull('media_url')
            ->orWhereNotNull('thumbnail_url')
            ->orWhereNotNull('display_media_url')
            ->get(['media_url', 'thumbnail_url', 'display_media_url']);

        foreach ($socialMediaPosts as $post) {
            if ($post->media_url) {
                $referencedFiles->push($this->extractFilePath($post->media_url));
            }
            if ($post->thumbnail_url) {
                $referencedFiles->push($this->extractFilePath($post->thumbnail_url));
            }
            if ($post->display_media_url) {
                $referencedFiles->push($this->extractFilePath($post->display_media_url));
            }
        }

        // Get files referenced in social_contents
        $socialContents = SocialContent::where('source', 'instagram')
            ->whereNotNull('media_url')
            ->orWhereNotNull('thumbnail_url')
            ->get(['media_url', 'thumbnail_url']);

        foreach ($socialContents as $content) {
            if ($content->media_url) {
                $referencedFiles->push($this->extractFilePath($content->media_url));
            }
            if ($content->thumbnail_url) {
                $referencedFiles->push($this->extractFilePath($content->thumbnail_url));
            }
        }

        $referencedFiles = $referencedFiles->filter()->unique();

        $this->info("Found " . $referencedFiles->count() . " referenced files in database");

        // Find orphaned files
        $orphanedFiles = collect($instagramFiles)->filter(function ($file) use ($referencedFiles) {
            return !$referencedFiles->contains($file);
        });

        // Find old files
        $cutoffDate = now()->subDays($days);
        $oldFiles = collect($instagramFiles)->filter(function ($file) use ($cutoffDate) {
            $lastModified = Storage::disk('public')->lastModified($file);
            return $lastModified < $cutoffDate->timestamp;
        });

        $filesToDelete = $orphanedFiles->merge($oldFiles)->unique();

        if ($filesToDelete->isEmpty()) {
            $this->info("No files to clean up!");
            return 0;
        }

        $this->warn("Files to be deleted:");
        $totalSize = 0;

        foreach ($filesToDelete as $file) {
            $size = Storage::disk('public')->size($file);
            $totalSize += $size;
            $sizeFormatted = $this->formatBytes($size);
            $lastModified = date('Y-m-d H:i:s', Storage::disk('public')->lastModified($file));

            $this->line("  - {$file} ({$sizeFormatted}) - Modified: {$lastModified}");
        }

        $this->info("Total files: " . $filesToDelete->count());
        $this->info("Total size: " . $this->formatBytes($totalSize));

        if ($dryRun) {
            $this->info("DRY RUN COMPLETE - No files were deleted");
            return 0;
        }

        if (!$this->confirm('Do you want to delete these files?')) {
            $this->info("Cleanup cancelled");
            return 0;
        }

        // Delete the files
        $deletedCount = 0;
        $deletedSize = 0;

        foreach ($filesToDelete as $file) {
            try {
                $size = Storage::disk('public')->size($file);
                Storage::disk('public')->delete($file);
                $deletedCount++;
                $deletedSize += $size;

                Log::info("Deleted Instagram media file: {$file}");
            } catch (\Exception $e) {
                $this->error("Failed to delete {$file}: " . $e->getMessage());
                Log::error("Failed to delete Instagram media file: {$file}", ['error' => $e->getMessage()]);
            }
        }

        $this->info("Cleanup complete!");
        $this->info("Deleted {$deletedCount} files");
        $this->info("Freed up " . $this->formatBytes($deletedSize) . " of storage");

        return 0;
    }

    /**
     * Extract file path from URL
     */
    private function extractFilePath($url)
    {
        if (empty($url) || $url === 'null') {
            return null;
        }

        // Extract path from storage URL
        if (strpos($url, '/storage/') !== false) {
            $parts = explode('/storage/', $url);
            if (count($parts) > 1) {
                return $parts[1];
            }
        }

        return null;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

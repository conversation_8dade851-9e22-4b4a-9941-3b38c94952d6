<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WaitlistRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'name',
        'status',
        'invited_at',
        'registered_at',
        'invitation_id',
        'metadata',
    ];

    protected $casts = [
        'invited_at' => 'datetime',
        'registered_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the invitation associated with this request
     */
    public function invitation()
    {
        return $this->belongsTo(WaitlistInvitation::class, 'invitation_id');
    }

    /**
     * Mark as invited
     */
    public function markAsInvited(WaitlistInvitation $invitation): void
    {
        $this->update([
            'status' => 'invited',
            'invited_at' => now(),
            'invitation_id' => $invitation->id,
        ]);
    }

    /**
     * Mark as registered
     */
    public function markAsRegistered(): void
    {
        $this->update([
            'status' => 'registered',
            'registered_at' => now(),
        ]);
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for invited requests
     */
    public function scopeInvited($query)
    {
        return $query->where('status', 'invited');
    }

    /**
     * Scope for registered requests
     */
    public function scopeRegistered($query)
    {
        return $query->where('status', 'registered');
    }

    /**
     * Check if request can be invited
     */
    public function canBeInvited(): bool
    {
        return $this->status === 'pending';
    }
}

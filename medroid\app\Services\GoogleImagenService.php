<?php

namespace App\Services;

use App\Models\AutomatedPost;
use App\Models\BotPersona;
use App\Models\ImageGenerationLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class GoogleImagenService
{
    private ?string $apiKey;
    private ?string $baseUrl;
    private ?string $imageModel;
    private ?string $predictEndpoint;

    public function __construct()
    {
        $this->apiKey = config('services.google_ai_studio.api_key');
        $this->baseUrl = config('services.google_ai_studio.api_url');
        $this->imageModel = config('services.google_ai_studio.image_model');
        $this->predictEndpoint = config('services.google_ai_studio.predict_endpoint');
    }

    /**
     * Generate an image using Google Imagen 2 (Gemini 2.0 Flash Preview Image Generation)
     */
    public function generateImage(string $prompt, AutomatedPost $post): ?ImageGenerationLog
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('Google AI Studio API key not configured');
                return null;
            }

            // Create generation log with unique task ID
            $taskId = 'imagen_' . uniqid() . '_' . $post->id;
            $log = ImageGenerationLog::create([
                'automated_post_id' => $post->id,
                'midjourney_task_id' => $taskId,
                'prompt' => $prompt,
                'status' => 'pending',
            ]);

            Log::info('Starting Google Imagen image generation', [
                'prompt' => $prompt,
                'post_id' => $post->id,
                'log_id' => $log->id,
            ]);

            // Generate image using Google AI Studio predict endpoint
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->timeout(60)->post("{$this->predictEndpoint}/{$this->imageModel}:predict?key={$this->apiKey}", [
                'instances' => [
                    [
                        'prompt' => $prompt
                    ]
                ],
                'parameters' => [
                    'sampleCount' => 1,
                    'aspectRatio' => '1:1',
                    'safetySetting' => 'block_low_and_above',
                    'personGeneration' => 'allow_adult'
                ]
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                Log::info('Google Imagen API response received', [
                    'log_id' => $log->id,
                    'response_keys' => array_keys($data),
                ]);

                // Extract image data from response (Imagen predict format)
                $imageUrl = $this->extractImageFromImagenResponse($data, $log);
                
                if ($imageUrl) {
                    $log->update([
                        'status' => 'completed',
                        'image_url' => $imageUrl,
                        'response_data' => $data,
                        'completed_at' => now(),
                    ]);

                    // Update the post with success
                    $post->update([
                        'status' => 'ready',
                        'image_url' => $imageUrl,
                    ]);

                    Log::info('Google Imagen image generation completed', [
                        'log_id' => $log->id,
                        'post_id' => $post->id,
                        'image_url' => $imageUrl,
                    ]);

                    return $log;
                } else {
                    $log->markAsFailed('No image data found in response', $data);
                    return null;
                }
            } else {
                $errorData = $response->json();
                $errorMessage = $errorData['error']['message'] ?? 'Unknown API error';
                
                Log::error('Google Imagen API error', [
                    'status' => $response->status(),
                    'error' => $errorMessage,
                    'response' => $response->body(),
                    'log_id' => $log->id,
                ]);

                $log->markAsFailed("API Error: {$errorMessage}", $errorData);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('Exception in Google Imagen image generation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'prompt' => $prompt,
                'post_id' => $post->id,
            ]);

            if (isset($log)) {
                $log->markAsFailed('Exception: ' . $e->getMessage());
            }

            return null;
        }
    }

    /**
     * Generate image with content-focused approach (no face consistency)
     */
    public function generateContentFocusedImage(string $prompt, AutomatedPost $post, BotPersona $persona): ?ImageGenerationLog
    {
        // Clean the prompt to ensure no text appears on images
        $cleanedPrompt = $this->cleanPromptForNoText($prompt);

        // Enhance the prompt with content and style focus rather than face consistency
        $enhancedPrompt = $this->enhancePromptForContent($cleanedPrompt, $persona);

        return $this->generateImage($enhancedPrompt, $post);
    }

    /**
     * Clean prompt to ensure no text appears on generated images
     */
    private function cleanPromptForNoText(string $prompt): string
    {
        // Add explicit instructions to avoid text in images
        $noTextInstructions = [
            'no text',
            'no words',
            'no letters',
            'no writing',
            'no captions',
            'no labels',
            'clean image without any text overlay'
        ];

        // Remove any existing text-related instructions that might encourage text
        $prompt = preg_replace('/\b(with text|text overlay|caption|label|sign|writing|words)\b/i', '', $prompt);

        // Add no-text instructions
        $prompt = $prompt . ', ' . implode(', ', $noTextInstructions);

        return $prompt;
    }

    /**
     * Enhance prompt for content-focused images with uniqueness
     */
    public function enhancePromptForContent(string $prompt, BotPersona $persona): string
    {
        // Focus on content, style, and activity with uniqueness factors
        $enhancements = [];

        // Add content focus styling
        if ($persona->content_focus) {
            $contentStyle = $this->getContentStyleFromFocus($persona->content_focus);
            $enhancements[] = $contentStyle;
        }

        // Add lifestyle context
        $lifestyleContext = $this->getLifestyleContext($persona->content_focus);
        if ($lifestyleContext) {
            $enhancements[] = $lifestyleContext;
        }

        // Add uniqueness factors to prevent duplicate images
        $uniquenessFactors = $this->getUniquenessFactors();
        $enhancements[] = $uniquenessFactors;

        // Add timestamp-based variation for additional uniqueness
        $timeVariation = $this->getTimeBasedVariation();
        $enhancements[] = $timeVariation;

        // Combine enhancements
        $enhancementText = implode(', ', array_filter($enhancements));

        if ($enhancementText) {
            return $enhancementText . ', ' . $prompt;
        }

        return $prompt;
    }

    /**
     * Get general age context (broad demographic, not specific features)
     */
    private function getGeneralAgeContext(int $age): string
    {
        if ($age < 25) return 'young adult energy';
        if ($age < 35) return 'professional millennial vibe';
        if ($age < 45) return 'experienced professional presence';
        return 'mature and confident demeanor';
    }

    /**
     * Get content style based on focus area
     */
    private function getContentStyleFromFocus(string $focus): string
    {
        $styleMap = [
            'fitness' => 'athletic setting, gym or outdoor workout environment, sporty aesthetic',
            'yoga' => 'peaceful studio setting, natural lighting, wellness atmosphere',
            'nutrition' => 'clean kitchen or cafe setting, fresh ingredients, healthy lifestyle',
            'mental_health' => 'calm and supportive environment, professional yet warm setting',
            'wellness' => 'balanced lifestyle setting, natural and serene atmosphere',
            'meditation' => 'tranquil space, mindful environment, peaceful setting',
        ];

        return $styleMap[$focus] ?? 'professional and welcoming environment';
    }

    /**
     * Get lifestyle context for content
     */
    private function getLifestyleContext(string $focus): string
    {
        $lifestyleMap = [
            'fitness' => 'active lifestyle, health-conscious choices',
            'yoga' => 'mindful living, balance-focused approach',
            'nutrition' => 'healthy eating habits, wellness-oriented lifestyle',
            'mental health' => 'self-care focused, emotionally aware approach',
            'wellness' => 'holistic health approach, balanced living',
            'meditation' => 'mindfulness practice, inner peace focus',
            'cycling' => 'outdoor adventure, endurance sports focus',
            'cooking' => 'culinary wellness, healthy meal preparation',
            'gut health' => 'digestive wellness, microbiome focus',
            'home wellness' => 'creating healthy living spaces',
            'health tech' => 'technology-enhanced wellness',
            'physical therapy' => 'recovery and rehabilitation focus',
            'brain health' => 'cognitive wellness, mental clarity',
            'cold therapy' => 'recovery and resilience training',
            'art therapy' => 'creative healing and expression',
            'sleep wellness' => 'rest and recovery optimization',
            'senior wellness' => 'age-positive health approach',
            'nature' => 'outdoor wellness, natural healing',
        ];

        return $lifestyleMap[$focus] ?? 'health and wellness focused lifestyle';
    }

    /**
     * Get uniqueness factors to prevent duplicate images
     */
    private function getUniquenessFactors(): string
    {
        $uniqueElements = [
            'unique perspective',
            'creative angle',
            'fresh composition',
            'innovative styling',
            'distinctive lighting',
            'original setup',
            'creative framing',
            'unique environment',
            'fresh approach',
            'distinctive mood'
        ];

        return $uniqueElements[array_rand($uniqueElements)];
    }

    /**
     * Get time-based variation for additional uniqueness
     */
    private function getTimeBasedVariation(): string
    {
        $hour = (int) date('H');
        $timeVariations = [
            'morning' => 'morning light, fresh start energy',
            'afternoon' => 'bright daylight, active energy',
            'evening' => 'golden hour lighting, calm atmosphere',
            'night' => 'soft evening lighting, peaceful mood'
        ];

        if ($hour < 10) return $timeVariations['morning'];
        if ($hour < 16) return $timeVariations['afternoon'];
        if ($hour < 20) return $timeVariations['evening'];
        return $timeVariations['night'];
    }

    /**
     * Extract image data from OpenAI-compatible response and save to storage
     */
    private function extractImageFromOpenAIResponse(array $responseData, ImageGenerationLog $log): ?string
    {
        try {
            // Navigate through OpenAI-compatible response structure
            $images = $responseData['data'] ?? [];

            if (empty($images)) {
                Log::error('No images found in OpenAI response', ['log_id' => $log->id]);
                return null;
            }

            $image = $images[0];
            $imageData = $image['b64_json'] ?? null;

            if (!$imageData) {
                Log::error('No b64_json data found in OpenAI response', [
                    'log_id' => $log->id,
                    'image_keys' => array_keys($image),
                ]);
                return null;
            }

            // Decode base64 image data
            $decodedImage = base64_decode($imageData);

            if ($decodedImage === false) {
                Log::error('Failed to decode base64 image data from OpenAI response', ['log_id' => $log->id]);
                return null;
            }

            // Generate filename
            $filename = 'bot-images/' . uniqid('imagen_') . '.png';

            // Save to storage
            if (Storage::disk('public')->put($filename, $decodedImage)) {
                $imageUrl = Storage::disk('public')->url($filename);

                Log::info('Imagen image saved successfully', [
                    'log_id' => $log->id,
                    'filename' => $filename,
                    'size' => strlen($decodedImage),
                ]);

                return $imageUrl;
            } else {
                Log::error('Failed to save Imagen image to storage', ['log_id' => $log->id]);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('Exception extracting image from OpenAI response', [
                'error' => $e->getMessage(),
                'log_id' => $log->id,
            ]);
            
            return null;
        }
    }

    /**
     * Extract image data from Imagen 4.0 response and save to storage (Legacy)
     */
    private function extractImageFromImagenResponse(array $responseData, ImageGenerationLog $log): ?string
    {
        try {
            // Navigate through Imagen 4.0 response structure
            $predictions = $responseData['predictions'] ?? [];

            if (empty($predictions)) {
                Log::error('No predictions found in Imagen response', ['log_id' => $log->id]);
                return null;
            }

            $prediction = $predictions[0];

            // Check for image data in different possible formats
            $imageData = null;
            $mimeType = 'image/png';

            if (isset($prediction['bytesBase64Encoded'])) {
                $imageData = $prediction['bytesBase64Encoded'];
            } elseif (isset($prediction['image'])) {
                $imageData = $prediction['image'];
            } elseif (isset($prediction['generatedImage'])) {
                $imageData = $prediction['generatedImage'];
            }

            if (!$imageData) {
                Log::error('No image data found in Imagen prediction', [
                    'log_id' => $log->id,
                    'prediction_keys' => array_keys($prediction),
                ]);
                return null;
            }

            // Decode base64 image data
            $decodedImage = base64_decode($imageData);

            if ($decodedImage === false) {
                Log::error('Failed to decode base64 image data from Imagen', ['log_id' => $log->id]);
                return null;
            }

            // Generate filename
            $extension = $this->getExtensionFromMimeType($mimeType);
            $filename = 'bot-images/' . uniqid('imagen_') . '.' . $extension;

            // Save to storage
            if (Storage::disk('public')->put($filename, $decodedImage)) {
                $imageUrl = Storage::disk('public')->url($filename);

                Log::info('Imagen image saved successfully', [
                    'log_id' => $log->id,
                    'filename' => $filename,
                    'size' => strlen($decodedImage),
                ]);

                return $imageUrl;
            } else {
                Log::error('Failed to save Imagen image to storage', ['log_id' => $log->id]);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('Exception extracting image from response', [
                'error' => $e->getMessage(),
                'log_id' => $log->id,
            ]);
            
            return null;
        }
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeToExtension = [
            'image/png' => 'png',
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/webp' => 'webp',
            'image/gif' => 'gif',
        ];

        return $mimeToExtension[$mimeType] ?? 'png';
    }

    /**
     * Enhance prompt with additional descriptive elements for better Imagen 3 results
     */
    public function enhancePrompt(string $prompt): string
    {
        // Enhanced prompt engineering based on Imagen 3 best practices
        $qualityEnhancers = [
            'high quality',
            'detailed',
            'professional photography',
            'vibrant colors',
            'sharp focus',
            '4K',
            'studio lighting'
        ];

        $styleEnhancers = [
            'beautiful',
            'Instagram-worthy',
            'professional',
            'award-winning'
        ];

        // Combine enhancers for optimal results
        $allEnhancers = array_merge(
            array_slice($qualityEnhancers, 0, 3), // Take first 3 quality enhancers
            array_slice($styleEnhancers, 0, 2)    // Take first 2 style enhancers
        );

        return $prompt . ', ' . implode(', ', $allEnhancers);
    }

    /**
     * Generate image directly and return result (for avatar generation)
     */
    public function generateImageDirect(string $prompt, int $maxWaitSeconds = 60): ?array
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('Google AI Studio API key not configured');
                return null;
            }

            Log::info('Starting direct Google Imagen image generation', ['prompt' => $prompt]);

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->timeout($maxWaitSeconds)->post("{$this->predictEndpoint}/{$this->imageModel}:predict?key={$this->apiKey}", [
                'instances' => [
                    [
                        'prompt' => $prompt
                    ]
                ],
                'parameters' => [
                    'sampleCount' => 1,
                    'aspectRatio' => '1:1',
                    'safetySetting' => 'block_low_and_above',
                    'personGeneration' => 'allow_adult'
                ]
            ]);

            if (!$response->successful()) {
                Log::error('Failed to generate image directly', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

            $data = $response->json();

            // Extract image data from Imagen predict response
            $predictions = $data['predictions'] ?? [];
            if (empty($predictions)) {
                Log::error('No predictions found in direct Imagen generation response');
                return null;
            }

            $prediction = $predictions[0];
            $imageData = $prediction['bytesBase64Encoded'] ?? $prediction['image'] ?? null;

            if ($imageData) {
                $decodedImage = base64_decode($imageData);

                if ($decodedImage !== false) {
                    // Generate filename and save
                    $filename = 'avatars/' . uniqid('imagen_avatar_') . '.png';

                    if (Storage::disk('public')->put($filename, $decodedImage)) {
                        $imageUrl = Storage::disk('public')->url($filename);

                        Log::info('Direct Imagen generation successful', [
                            'image_url' => $imageUrl,
                            'size' => strlen($decodedImage),
                        ]);

                        return [
                            'image_url' => $imageUrl,
                            'task_id' => 'imagen_' . uniqid(),
                            'response_data' => $data,
                        ];
                    }
                }
            }

            Log::error('No valid image data found in direct Imagen generation response');
            return null;

        } catch (\Exception $e) {
            Log::error('Exception in direct image generation', [
                'error' => $e->getMessage(),
                'prompt' => $prompt,
            ]);
            return null;
        }
    }

    /**
     * Enhanced prompt engineering for health-related content
     */
    public function enhanceHealthPrompt(string $prompt, string $contentType = 'general'): string
    {
        $baseEnhancers = [
            'high quality',
            'professional',
            'clean',
            'modern',
            'Instagram-worthy'
        ];

        $healthSpecificEnhancers = [];

        switch ($contentType) {
            case 'fitness':
                $healthSpecificEnhancers = [
                    'fitness photography',
                    'athletic',
                    'energetic',
                    'motivational',
                    'gym lighting'
                ];
                break;
            case 'nutrition':
                $healthSpecificEnhancers = [
                    'food photography',
                    'fresh ingredients',
                    'vibrant colors',
                    'healthy lifestyle',
                    'natural lighting'
                ];
                break;
            case 'wellness':
                $healthSpecificEnhancers = [
                    'wellness photography',
                    'peaceful',
                    'serene',
                    'mindful',
                    'soft lighting'
                ];
                break;
            case 'medical':
                $healthSpecificEnhancers = [
                    'medical photography',
                    'clinical',
                    'professional',
                    'trustworthy',
                    'clean background'
                ];
                break;
            default:
                $healthSpecificEnhancers = [
                    'health and wellness',
                    'lifestyle photography',
                    'positive',
                    'inspiring'
                ];
        }

        $allEnhancers = array_merge($baseEnhancers, $healthSpecificEnhancers);

        return $prompt . ', ' . implode(', ', $allEnhancers);
    }

    /**
     * Generate image with specific aspect ratio for social media
     */
    public function generateImageWithAspectRatio(string $prompt, AutomatedPost $post, string $aspectRatio = '1:1'): ?ImageGenerationLog
    {
        try {
            if (empty($this->apiKey)) {
                Log::error('Google AI Studio API key not configured');
                return null;
            }

            // Create generation log
            $log = ImageGenerationLog::create([
                'automated_post_id' => $post->id,
                'midjourney_task_id' => '', // Will be updated with Google task ID
                'prompt' => $prompt,
                'status' => 'pending',
            ]);

            Log::info('Starting Google Imagen image generation with aspect ratio', [
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'post_id' => $post->id,
                'log_id' => $log->id,
            ]);

            // Generate image using Google AI Studio predict endpoint with custom aspect ratio
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->timeout(60)->post("{$this->predictEndpoint}/{$this->imageModel}:predict?key={$this->apiKey}", [
                'instances' => [
                    [
                        'prompt' => $prompt
                    ]
                ],
                'parameters' => [
                    'sampleCount' => 1,
                    'aspectRatio' => $aspectRatio,
                    'safetySetting' => 'block_low_and_above',
                    'personGeneration' => 'allow_adult'
                ]
            ]);

            if ($response->successful()) {
                $data = $response->json();

                Log::info('Google Imagen API response received', [
                    'log_id' => $log->id,
                    'aspect_ratio' => $aspectRatio,
                    'response_keys' => array_keys($data),
                ]);

                // Extract image data from response (Imagen predict format)
                $imageUrl = $this->extractImageFromImagenResponse($data, $log);

                if ($imageUrl) {
                    $log->update([
                        'status' => 'completed',
                        'image_url' => $imageUrl,
                        'response_data' => $data,
                        'completed_at' => now(),
                    ]);

                    // Update the post with success
                    $post->update([
                        'status' => 'ready',
                        'image_url' => $imageUrl,
                    ]);

                    Log::info('Google Imagen image generation completed', [
                        'log_id' => $log->id,
                        'post_id' => $post->id,
                        'image_url' => $imageUrl,
                        'aspect_ratio' => $aspectRatio,
                    ]);

                    return $log;
                } else {
                    $log->markAsFailed('No image data found in response', $data);
                    return null;
                }
            } else {
                $errorData = $response->json();
                $errorMessage = $errorData['error']['message'] ?? 'Unknown API error';

                Log::error('Google Imagen API error', [
                    'status' => $response->status(),
                    'error' => $errorMessage,
                    'response' => $response->body(),
                    'log_id' => $log->id,
                ]);

                $log->markAsFailed("API Error: {$errorMessage}", $errorData);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('Exception in Google Imagen image generation with aspect ratio', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'prompt' => $prompt,
                'aspect_ratio' => $aspectRatio,
                'post_id' => $post->id,
            ]);

            if (isset($log)) {
                $log->markAsFailed('Exception: ' . $e->getMessage());
            }

            return null;
        }
    }

    /**
     * Check if the service is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey) && !empty($this->baseUrl) && !empty($this->imageModel) && !empty($this->predictEndpoint);
    }
}

<?php

namespace App\Services\Agents;

use App\Models\BotPersona;

class HashtagIntelligenceAgent extends BaseAgent
{
    protected function getAgentName(): string
    {
        return 'HashtagIntelligenceAgent';
    }

    protected function getDefaultOptions(): array
    {
        return [
            'temperature' => 0.6,
            'max_completion_tokens' => 300,
            'top_p' => 0.8,
        ];
    }

    protected function createSystemPrompt(array $context = []): string
    {
        return "You are a Hashtag Strategy Expert specializing in health and wellness social media optimization.

Your expertise includes:
- Trending hashtag analysis and selection
- Niche-specific hashtag research
- Hashtag performance optimization
- Community building through strategic tagging
- Balancing reach and engagement

HASHTAG STRATEGY FRAMEWORK:
1. TRENDING (2-3 hashtags) - Currently popular, high reach
2. NICHE (4-5 hashtags) - Specific to content focus, targeted audience
3. COMMUNITY (2-3 hashtags) - Brand/persona building, loyal followers
4. LONG-TAIL (3-4 hashtags) - Specific, less competitive, higher engagement

HASHTAG SELECTION CRITERIA:
- Relevance to content and persona
- Appropriate audience size (mix of large and small)
- Current trending status
- Community engagement potential
- Brand consistency

CURRENT TRENDING HEALTH HASHTAGS (2024):
#HealthyLifestyle #WellnessJourney #MindBodySoul #SelfCareRoutine #HealthyHabits
#WellnessTips #HealthyMindset #FitnessMotivation #NutritionTips #MentalHealthMatters
#WellnessWednesday #HealthyLiving #MindfulLiving #WellnessWarrior #HealthGoals

OUTPUT FORMAT:
HASHTAGS: [List 10-15 hashtags separated by spaces, each starting with #]

Focus on creating a strategic mix that maximizes both reach and engagement for the specific content and persona.";
    }

    public function process(array $input): array
    {
        $this->validateInput($input, ['persona', 'strategy']);
        
        $persona = $input['persona'];
        $strategy = $input['strategy'];
        $context = $input['context'] ?? [];
        
        // Get caption if available from parallel processing
        $caption = $input['caption'] ?? '';
        
        $userPrompt = $this->createUserPrompt($persona, $strategy, $caption, $context);
        $systemPrompt = $this->createSystemPrompt($context);
        
        $response = $this->executeAgent($systemPrompt, $userPrompt);
        
        return $this->parseHashtagResponse($response, $persona);
    }

    protected function createUserPrompt(BotPersona $persona, array $strategy, string $caption, array $context): string
    {
        $strategyData = $strategy['strategy'] ?? $strategy;
        
        $interests = is_array($persona->interests) ? implode(', ', $persona->interests) : ($persona->interests ?? 'General wellness');

        $prompt = "PERSONA PROFILE:
Name: {$persona->full_name} (@{$persona->handle})
Content Focus: {$persona->content_focus}
Interests: {$interests}
Target Audience: Health-conscious individuals interested in {$persona->content_focus}

CONTENT STRATEGY:
Theme: {$strategyData['content_theme']}
Content Type: {$strategyData['content_type']}
Target Emotion: {$strategyData['target_emotion']}
Audience Focus: {$strategyData['audience_focus']}

CONTENT PREVIEW:
" . ($caption ? "Caption: " . substr($caption, 0, 200) . "..." : "Caption not yet available") . "

FOCUS-SPECIFIC HASHTAG CATEGORIES:
";

        // Add focus-specific hashtag guidance
        switch ($persona->content_focus) {
            case 'yoga':
                $prompt .= "TRENDING: #Yoga #YogaLife #Namaste #YogaEveryday #Mindfulness
NICHE: #YogaPractice #YogaFlow #YogaInspiration #YogaCommunity #YogaTeacher
SPECIFIC: #VinyasaYoga #HathaYoga #YogaPoses #YogaJourney #YogaWisdom";
                break;
            case 'fitness':
                $prompt .= "TRENDING: #Fitness #FitnessMotivation #WorkoutMotivation #FitLife #GymLife
NICHE: #StrengthTraining #Cardio #FitnessJourney #FitnessTips #WorkoutTips
SPECIFIC: #FitnessGoals #TrainingDay #FitnessAddict #FitFam #WorkoutWednesday";
                break;
            case 'nutrition':
                $prompt .= "TRENDING: #Nutrition #HealthyEating #NutritionTips #HealthyFood #WholeFoods
NICHE: #NutritionScience #HealthyRecipes #MealPrep #NutritionEducation #DietTips
SPECIFIC: #NutritionFacts #HealthyMeals #NutritionCoach #EatWell #FoodIsMedicine";
                break;
            case 'mental health':
                $prompt .= "TRENDING: #MentalHealth #MentalHealthMatters #SelfCare #MentalWellness #Mindfulness
NICHE: #MentalHealthAwareness #StressManagement #MentalHealthSupport #Therapy #Wellness
SPECIFIC: #MentalHealthTips #EmotionalWellness #MindfulMoments #MentalStrength #SelfLove";
                break;
            case 'cycling':
                $prompt .= "TRENDING: #Cycling #BikeLife #CyclingLife #Cyclist #BikeRide
NICHE: #CyclingCommunity #CyclingMotivation #BikeFitness #CyclingTips #RoadCycling
SPECIFIC: #CyclingAdventure #BikeTraining #CyclingGoals #CycleToWork #BikeLove";
                break;
            case 'cooking':
                $prompt .= "TRENDING: #Cooking #HealthyCooking #FoodPrep #HealthyRecipes #HomeCooking
NICHE: #CookingTips #HealthyMeals #NutrientDense #CookingSkills #FoodLove
SPECIFIC: #HealthyEating #CookingFromScratch #WholeFoodCooking #MealPlanning #FoodWisdom";
                break;
            default:
                $prompt .= "TRENDING: #Health #Wellness #HealthyLifestyle #WellnessJourney #HealthTips
NICHE: #WellnessTips #HealthyLiving #WellnessCoach #HealthGoals #WellnessWarrior
SPECIFIC: #HealthyHabits #WellnessWisdom #HealthEducation #WellnessCommunity #HealthyMindset";
        }

        $prompt .= "\n\nGenerate 10-15 strategic hashtags that will maximize reach and engagement for this specific content. Include a mix of trending, niche, and specific hashtags relevant to the persona and content theme.";

        return $prompt;
    }

    protected function parseHashtagResponse(string $response, BotPersona $persona): array
    {
        // Extract hashtags
        $hashtagMatch = [];
        if (preg_match('/HASHTAGS:\s*(.*?)$/s', $response, $hashtagMatch)) {
            $hashtagText = trim($hashtagMatch[1]);
        } else {
            // Fallback: extract hashtags from entire response
            $hashtagText = $response;
        }

        // Parse hashtags
        $hashtags = $this->parseHashtags($hashtagText);
        
        // Validate and enhance hashtags
        $hashtags = $this->validateAndEnhanceHashtags($hashtags, $persona);

        // Categorize hashtags
        $categorized = $this->categorizeHashtags($hashtags, $persona);

        return [
            'hashtags' => $hashtags,
            'hashtag_count' => count($hashtags),
            'categorized' => $categorized,
            'agent' => $this->agentName,
            'timestamp' => now()->toISOString(),
        ];
    }

    protected function parseHashtags(string $text): array
    {
        // Extract hashtags using regex
        preg_match_all('/#[\w\d_]+/i', $text, $matches);
        $hashtags = $matches[0] ?? [];

        // Clean and deduplicate
        $hashtags = array_map(function($tag) {
            return strtolower(trim($tag));
        }, $hashtags);

        $hashtags = array_unique($hashtags);
        $hashtags = array_values($hashtags);

        return $hashtags;
    }

    protected function validateAndEnhanceHashtags(array $hashtags, BotPersona $persona): array
    {
        // Remove invalid hashtags
        $hashtags = array_filter($hashtags, function($tag) {
            return strlen($tag) > 2 && strlen($tag) <= 30 && preg_match('/^#[a-zA-Z0-9_]+$/', $tag);
        });

        // Ensure minimum count
        if (count($hashtags) < 8) {
            $hashtags = array_merge($hashtags, $this->getFallbackHashtags($persona));
        }

        // Limit to maximum count
        $hashtags = array_slice($hashtags, 0, 15);

        // Ensure proper formatting
        $hashtags = array_map(function($tag) {
            return str_starts_with($tag, '#') ? $tag : '#' . $tag;
        }, $hashtags);

        return array_values(array_unique($hashtags));
    }

    protected function categorizeHashtags(array $hashtags, BotPersona $persona): array
    {
        $trending = ['#health', '#wellness', '#healthylifestyle', '#wellnessjourney', '#selfcare'];
        $niche = $this->getNicheHashtags($persona->content_focus);
        
        $categorized = [
            'trending' => array_intersect($hashtags, $trending),
            'niche' => array_intersect($hashtags, $niche),
            'specific' => array_diff($hashtags, $trending, $niche),
        ];

        return $categorized;
    }

    protected function getNicheHashtags(string $contentFocus): array
    {
        $nicheMap = [
            'yoga' => ['#yoga', '#yogalife', '#yogapractice', '#mindfulness', '#meditation'],
            'fitness' => ['#fitness', '#workout', '#gym', '#training', '#exercise'],
            'nutrition' => ['#nutrition', '#healthyeating', '#healthyfood', '#diet', '#wellness'],
            'mental health' => ['#mentalhealth', '#mentalhealthmatters', '#therapy', '#mindfulness'],
            'cycling' => ['#cycling', '#bikelife', '#cyclist', '#biking', '#bicycle'],
            'cooking' => ['#cooking', '#healthycooking', '#recipes', '#foodprep', '#nutrition'],
        ];

        return $nicheMap[$contentFocus] ?? ['#health', '#wellness', '#lifestyle'];
    }

    protected function getFallbackHashtags(BotPersona $persona): array
    {
        $base = ['#health', '#wellness', '#healthylifestyle', '#wellnessjourney'];
        $focus = $this->getNicheHashtags($persona->content_focus);
        
        return array_merge($base, $focus);
    }

    public function test(): array
    {
        $testPersona = new BotPersona();
        $testPersona->first_name = 'Alex';
        $testPersona->last_name = 'Fitness';
        $testPersona->handle = 'alex_fitness';
        $testPersona->content_focus = 'fitness';
        $testPersona->interests = ['strength training', 'cardio', 'nutrition'];

        $testStrategy = [
            'strategy' => [
                'content_theme' => 'Morning workout routine',
                'content_type' => 'educational',
                'target_emotion' => 'motivation',
                'audience_focus' => 'fitness enthusiasts',
            ]
        ];

        $result = $this->process([
            'persona' => $testPersona,
            'strategy' => $testStrategy,
            'context' => ['test_mode' => true],
        ]);

        return [
            'agent' => $this->agentName,
            'test_successful' => !empty($result['hashtags']),
            'hashtag_count' => count($result['hashtags'] ?? []),
            'sample_hashtags' => array_slice($result['hashtags'] ?? [], 0, 5),
            'categories' => array_keys($result['categorized'] ?? []),
        ];
    }
}

<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\User;
use App\Models\VideoConsultation;
use App\Models\VideoSessionParticipant;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use TaylanUnutmaz\AgoraTokenBuilder\RtcTokenBuilder;

class VideoSessionService
{
    protected $appId;
    protected $appCertificate;
    protected $expirationTimeInSeconds;

    public function __construct()
    {
        $this->appId = env('AGORA_APP_ID');
        $this->appCertificate = env('AGORA_APP_CERTIFICATE');
        $this->expirationTimeInSeconds = 3600; // 1 hour
    }

    /**
     * Create a new video session for an appointment.
     * This should be called by the provider to start the session.
     */
    public function createSession(Appointment $appointment, User $creator)
    {
        try {
            // First, end all existing sessions for this user to prevent conflicts
            $this->endAllUserSessions($creator);

            // Check if session already exists for this appointment
            $existingSession = VideoConsultation::where('appointment_id', $appointment->id)
                ->where(function($query) {
                    $query->where('status', 'created')->orWhere('status', 'active');
                })
                ->first();

            if ($existingSession) {
                // End the existing session and create a new one
                $existingSession->end();

                // Clear the appointment's video session ID to force recreation
                $appointment->update(['video_session_id' => null]);
            }

            // Generate unique identifiers
            $sessionId = 'vs_' . Str::uuid();
            $channelName = 'medroid_' . $appointment->id . '_' . time();

            // Create video consultation record
            $videoConsultation = VideoConsultation::create([
                'appointment_id' => $appointment->id,
                'session_id' => $sessionId,
                'channel_name' => $channelName,
                'agora_app_id' => $this->appId,
                'status' => 'created',
                'created_by' => $creator->id,
                'max_participants' => 2,
                'current_participants' => 0,
                'session_config' => [
                    'video_enabled' => true,
                    'audio_enabled' => true,
                    'recording_enabled' => false,
                ],
            ]);

            // Update appointment with session ID only
            $appointment->update([
                'video_session_id' => $sessionId,
            ]);

            // Send FCM notification to patient about incoming video call
            $this->sendVideoCallNotification($appointment, $videoConsultation);

            return [
                'success' => true,
                'session' => $videoConsultation,
                'message' => 'Session created successfully',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Join a video session as a participant.
     */
    public function joinSession(Appointment $appointment, User $user)
    {
        try {
            // Get the video consultation
            $videoConsultation = VideoConsultation::where('appointment_id', $appointment->id)
                ->where(function($query) {
                    $query->where('status', 'created')->orWhere('status', 'active');
                })
                ->first();

            if (!$videoConsultation) {
                return [
                    'success' => false,
                    'message' => 'No active video session found for this appointment',
                ];
            }

            // Clean up any stale participants first
            $this->cleanupStaleParticipants($videoConsultation);

            // Check if session can accept participants
            if (!$videoConsultation->canAcceptParticipants()) {
                // Try to clean up user's existing sessions and retry
                $this->endAllUserSessions($user);

                // Refresh the video consultation data
                $videoConsultation->refresh();

                // Check again after cleanup
                if (!$videoConsultation->canAcceptParticipants()) {
                    return [
                        'success' => false,
                        'message' => 'Session is full or not accepting participants',
                    ];
                }
            }

            // Determine user role
            $role = $this->getUserRole($user, $appointment);
            if (!$role) {
                return [
                    'success' => false,
                    'message' => 'User not authorized for this appointment',
                ];
            }

            // Check if participant already exists
            $participant = VideoSessionParticipant::where('video_consultation_id', $videoConsultation->id)
                ->where('user_id', $user->id)
                ->first();

            if ($participant) {
                // If participant exists but token is expired, refresh it
                if ($participant->isTokenExpired()) {
                    $this->refreshParticipantToken($participant, $videoConsultation->channel_name);
                }

                // Update status to joined if not already
                if (!$participant->isJoined()) {
                    $participant->join();
                }
            } else {
                // Create new participant
                $participant = $this->createParticipant($videoConsultation, $user, $role);
                if (!$participant) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create participant record',
                    ];
                }
            }

            // Update connection info with timestamp
            if ($participant) {
                $participant->update([
                    'connection_info' => array_merge($participant->connection_info ?? [], [
                        'last_join_attempt' => now()->toISOString(),
                        'join_count' => ($participant->connection_info['join_count'] ?? 0) + 1,
                    ])
                ]);
            }

            // Start session if this is the first participant
            if ($videoConsultation->status === 'created') {
                $videoConsultation->start();
            }

            // Clean up any stale participants (disconnected for more than 5 minutes)
            $this->cleanupStaleParticipants($videoConsultation);



            return [
                'success' => true,
                'session_data' => [
                    'app_id' => $this->appId,
                    'channel' => $videoConsultation->channel_name,
                    'token' => $participant->agora_token,
                    'uid' => $participant->agora_uid,
                    'session_id' => $videoConsultation->session_id,
                ],
                'participant' => $participant,
                'session' => $videoConsultation,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to join session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Leave a video session.
     */
    public function leaveSession(Appointment $appointment, User $user)
    {
        try {
            $videoConsultation = VideoConsultation::where('appointment_id', $appointment->id)
                ->where(function($query) {
                    $query->where('status', 'created')->orWhere('status', 'active');
                })
                ->first();

            if (!$videoConsultation) {
                return [
                    'success' => false,
                    'message' => 'No active video session found',
                ];
            }

            $participant = VideoSessionParticipant::where('video_consultation_id', $videoConsultation->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$participant) {
                return [
                    'success' => false,
                    'message' => 'User not found in session',
                ];
            }

            // Mark participant as left
            $participant->leave();

            // End session if no participants left
            if ($videoConsultation->current_participants <= 0) {
                $videoConsultation->end();
            }

            Log::info('User left video session', [
                'appointment_id' => $appointment->id,
                'session_id' => $videoConsultation->session_id,
                'user_id' => $user->id,
                'remaining_participants' => $videoConsultation->current_participants,
            ]);

            return [
                'success' => true,
                'message' => 'Left session successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Error leaving video session', [
                'appointment_id' => $appointment->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to leave session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * End a video session completely.
     */
    public function endSession(Appointment $appointment, User $user)
    {
        try {
            $videoConsultation = VideoConsultation::where('appointment_id', $appointment->id)
                ->where(function($query) {
                    $query->where('status', 'created')->orWhere('status', 'active');
                })
                ->first();

            if (!$videoConsultation) {
                return [
                    'success' => false,
                    'message' => 'No active video session found',
                ];
            }

            // End the session
            $videoConsultation->end();

            // Update appointment status
            $appointment->update([
                'status' => 'completed',
                'consultation_ended_at' => now(),
            ]);

            Log::info('Video session ended', [
                'appointment_id' => $appointment->id,
                'session_id' => $videoConsultation->session_id,
                'ended_by' => $user->id,
            ]);

            return [
                'success' => true,
                'message' => 'Session ended successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Error ending video session', [
                'appointment_id' => $appointment->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to end session: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create a participant record.
     */
    protected function createParticipant(VideoConsultation $videoConsultation, User $user, string $role)
    {
        try {
            // Generate unique UID for this participant
            $agoraUid = $this->generateUniqueUid($user->id, $role);

            // Generate token
            $token = $this->generateToken($videoConsultation->channel_name, $agoraUid);
            $expiresAt = now()->addSeconds($this->expirationTimeInSeconds);

            // Get enhanced device information
            $userAgentService = app(\App\Services\UserAgentService::class);
            $deviceInfo = $userAgentService->parseUserAgent(request());

            // Get additional device info from request (sent by Flutter)
            $connectionInfo = array_merge($deviceInfo, [
                'device_model' => request()->input('device_model'),
                'app_version' => request()->input('app_version'),
                'platform_version' => request()->input('platform_version'),
                'session_start_time' => now()->toISOString(),
                'join_method' => request()->input('join_method', 'mobile_app'),
            ]);

            $participant = VideoSessionParticipant::create([
                'video_consultation_id' => $videoConsultation->id,
                'user_id' => $user->id,
                'agora_uid' => $agoraUid,
                'agora_token' => $token,
                'role' => $role,
                'status' => 'invited',
                'token_expires_at' => $expiresAt,
                'connection_info' => $connectionInfo,
            ]);

            return $participant;

        } catch (\Exception $e) {
            Log::error('Error creating participant', [
                'video_consultation_id' => $videoConsultation->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Refresh participant token.
     */
    protected function refreshParticipantToken(VideoSessionParticipant $participant, string $channelName)
    {
        try {
            $newToken = $this->generateToken($channelName, $participant->agora_uid);
            $expiresAt = now()->addSeconds($this->expirationTimeInSeconds);

            $participant->refreshToken($newToken, $expiresAt);

            Log::info('Participant token refreshed', [
                'participant_id' => $participant->id,
                'user_id' => $participant->user_id,
                'agora_uid' => $participant->agora_uid,
            ]);

        } catch (\Exception $e) {
            Log::error('Error refreshing participant token', [
                'participant_id' => $participant->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate Agora token - improved error handling.
     */
    protected function generateToken(string $channelName, int $uid)
    {
        try {
            if (empty($channelName)) {
                throw new \Exception('Channel name cannot be empty');
            }

            if (empty($this->appId) || empty($this->appCertificate)) {
                throw new \Exception('Agora credentials not configured properly');
            }

            // Ensure UID is valid
            $uid = max(1, abs($uid));
            $expireTime = time() + $this->expirationTimeInSeconds;

            Log::info('Generating Agora token', [
                'channel' => $channelName,
                'uid' => $uid,
                'expire_time' => date('Y-m-d H:i:s', $expireTime),
            ]);

            $token = RtcTokenBuilder::buildTokenWithUid(
                $this->appId,
                $this->appCertificate,
                $channelName,
                $uid,
                1, // Role: Publisher (can send and receive)
                $expireTime
            );

            if (empty($token)) {
                throw new \Exception('Token generation returned empty result');
            }

            // Validate token format (Agora tokens should start with '006' for RTC)
            if (!str_starts_with($token, '006')) {
                Log::warning('Generated token has unexpected format', [
                    'token_prefix' => substr($token, 0, 10),
                    'token_length' => strlen($token),
                ]);
            }

            return $token;

        } catch (\Exception $e) {
            Log::error('Failed to generate Agora token', [
                'error' => $e->getMessage(),
                'channel' => $channelName,
                'uid' => $uid,
                'app_id' => $this->appId,
                'has_certificate' => !empty($this->appCertificate),
            ]);

            throw new \Exception('Token generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique UID for Agora - simplified and more reliable.
     */
    protected function generateUniqueUid(int $userId, string $role)
    {
        // Use a simpler approach to avoid collisions
        $rolePrefix = $role === 'patient' ? 1 : 2;
        $timestamp = time() % 100000; // Last 5 digits of timestamp
        $userSuffix = $userId % 10000; // Last 4 digits of user ID
        
        // Format: [role][timestamp][userId] - ensures uniqueness
        $uid = intval($rolePrefix . str_pad($timestamp, 5, '0', STR_PAD_LEFT) . str_pad($userSuffix, 4, '0', STR_PAD_LEFT));
        
        // Ensure UID is within Agora's range (1 to 2^32-1)
        $uid = max(1, min($uid, **********));
        
        Log::info('Generated Agora UID', [
            'user_id' => $userId,
            'role' => $role,
            'generated_uid' => $uid,
        ]);

        return $uid;
    }

    /**
     * Get user role for appointment.
     */
    protected function getUserRole(User $user, Appointment $appointment)
    {
        if ($user->role === 'patient' && $user->patient && $user->patient->id === $appointment->patient_id) {
            return 'patient';
        }

        if ($user->role === 'provider' && $user->provider && $user->provider->id === $appointment->provider_id) {
            return 'provider';
        }

        // Admin/manager can join as provider
        if (in_array($user->role, ['admin', 'manager'])) {
            return 'provider';
        }

        return null;
    }

    /**
     * End all active video sessions for a specific user.
     */
    public function endAllUserSessions(User $user)
    {
        try {
            $activeParticipants = VideoSessionParticipant::where('user_id', $user->id)
                ->whereIn('status', ['invited', 'joined'])
                ->with('videoConsultation')
                ->get();

            if ($activeParticipants->count() > 0) {
                Log::info('Cleaning up active sessions for user', [
                    'user_id' => $user->id,
                    'active_sessions' => $activeParticipants->count(),
                ]);

                foreach ($activeParticipants as $participant) {
                    $videoConsultation = $participant->videoConsultation;

                    if ($videoConsultation && in_array($videoConsultation->status, ['created', 'active'])) {
                        // Mark participant as left
                        $participant->leave();

                        // End session if no participants left
                        if ($videoConsultation->current_participants <= 0) {
                            $videoConsultation->end();
                            
                            // Clear appointment reference
                            if ($videoConsultation->appointment) {
                                $videoConsultation->appointment->update(['video_session_id' => null]);
                            }
                        }
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Error cleaning up user sessions', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Clean up stale participants - simplified and more reliable.
     */
    public function cleanupStaleParticipants(VideoConsultation $videoConsultation)
    {
        try {
            // More aggressive cleanup - 1 minute threshold
            $staleThreshold = now()->subMinute();

            // Find participants that should be cleaned up
            $staleParticipants = VideoSessionParticipant::where('video_consultation_id', $videoConsultation->id)
                ->where(function ($query) use ($staleThreshold) {
                    // Disconnected participants older than threshold
                    $query->where('status', 'disconnected')
                          ->where('left_at', '<', $staleThreshold);
                })
                ->get();

            if ($staleParticipants->count() > 0) {
                Log::info('Cleaning up stale participants', [
                    'session_id' => $videoConsultation->session_id,
                    'count' => $staleParticipants->count(),
                ]);

                foreach ($staleParticipants as $participant) {
                    if ($participant->status !== 'left') {
                        $participant->leave();
                    }
                }
            }

            // Recalculate participant count to ensure accuracy
            $videoConsultation->recalculateParticipantCount();

        } catch (\Exception $e) {
            Log::error('Error during participant cleanup', [
                'video_consultation_id' => $videoConsultation->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send FCM notification to patient when provider starts video call
     */
    protected function sendVideoCallNotification(Appointment $appointment, VideoConsultation $videoConsultation)
    {
        try {
            // Get the patient user
            $patient = $appointment->patient->user;
            $provider = $appointment->provider->user;

            // Prepare notification data
            $title = "Incoming Video Call";
            $body = "Dr. {$provider->name} is calling you for your appointment";

            $notificationData = [
                'type' => 'video_call',
                'appointment_id' => $appointment->id,
                'session_id' => $videoConsultation->session_id,
                'channel_name' => $videoConsultation->channel_name,
                'provider_name' => $provider->name,
                'provider_id' => $appointment->provider_id,
                'call_action' => 'incoming_call',
                'timestamp' => now()->toISOString(),
            ];

            // Send FCM notification using NotificationService
            $notificationService = app(NotificationService::class);
            $success = $notificationService->sendPushNotification(
                $patient,
                $title,
                $body,
                'video_call',
                $notificationData
            );

            if ($success) {
                Log::info('Video call notification sent successfully', [
                    'appointment_id' => $appointment->id,
                    'patient_id' => $patient->id,
                    'session_id' => $videoConsultation->session_id,
                ]);
            } else {
                Log::warning('Failed to send video call notification', [
                    'appointment_id' => $appointment->id,
                    'patient_id' => $patient->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error sending video call notification', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Clinic;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ClinicController extends Controller
{
    /**
     * Display a listing of clinics.
     */
    public function index(Request $request)
    {
        $query = Clinic::with(['providers.user', 'patients.user']);

        // Filter by active status
        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%")
                  ->orWhere('state', 'like', "%{$search}%");
            });
        }

        // Filter by city
        if ($request->has('city') && $request->city) {
            $query->where('city', $request->city);
        }

        // Filter by state
        if ($request->has('state') && $request->state) {
            $query->where('state', $request->state);
        }

        $clinics = $query->orderBy('name')->paginate(15);

        // Add statistics to each clinic
        $clinics->getCollection()->transform(function ($clinic) {
            $clinic->stats = $clinic->stats;
            return $clinic;
        });

        return response()->json($clinics);
    }

    /**
     * Store a newly created clinic.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'license_number' => 'nullable|string|max:100',
            'tax_id' => 'nullable|string|max:100',
            'primary_color' => 'nullable|string|max:7',
            'secondary_color' => 'nullable|string|max:7',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $clinic = Clinic::create($request->only([
                'name', 'description', 'email', 'phone', 'website',
                'address', 'city', 'state', 'postal_code', 'country',
                'operating_hours', 'services_offered', 'license_number', 'tax_id',
                'is_active', 'accepts_new_patients', 'telemedicine_enabled',
                'insurance_accepted', 'primary_color', 'secondary_color'
            ]));

            return response()->json([
                'message' => 'Clinic created successfully',
                'clinic' => $clinic->load(['providers.user', 'patients.user'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json(['message' => 'Error creating clinic: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified clinic.
     */
    public function show($id)
    {
        $clinic = Clinic::with([
            'providers.user',
            'patients.user',
            'appointments.patient.user',
            'appointments.provider.user'
        ])->findOrFail($id);

        $clinic->stats = $clinic->stats;

        return response()->json($clinic);
    }

    /**
     * Update the specified clinic.
     */
    public function update(Request $request, $id)
    {
        $clinic = Clinic::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'license_number' => 'nullable|string|max:100',
            'tax_id' => 'nullable|string|max:100',
            'primary_color' => 'nullable|string|max:7',
            'secondary_color' => 'nullable|string|max:7',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $clinic->update($request->only([
            'name', 'description', 'email', 'phone', 'website',
            'address', 'city', 'state', 'postal_code', 'country',
            'operating_hours', 'services_offered', 'license_number', 'tax_id',
            'is_active', 'accepts_new_patients', 'telemedicine_enabled',
            'insurance_accepted', 'primary_color', 'secondary_color'
        ]));

        return response()->json([
            'message' => 'Clinic updated successfully',
            'clinic' => $clinic->load(['providers.user', 'patients.user'])
        ]);
    }

    /**
     * Remove the specified clinic.
     */
    public function destroy($id)
    {
        $clinic = Clinic::findOrFail($id);

        // Check if clinic has providers or patients
        if ($clinic->providers()->count() > 0 || $clinic->patients()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete clinic with associated providers or patients'
            ], 422);
        }

        $clinic->delete();

        return response()->json(['message' => 'Clinic deleted successfully']);
    }

    /**
     * Get clinic users (providers and patients).
     */
    public function getUsers($id)
    {
        $clinic = Clinic::with(['providers.user', 'patients.user'])->findOrFail($id);

        $users = collect();

        // Add provider users
        foreach ($clinic->providers as $provider) {
            if ($provider->user) {
                $users->push([
                    'id' => $provider->user->id,
                    'name' => $provider->user->name,
                    'email' => $provider->user->email,
                    'type' => 'provider',
                    'role' => 'provider',
                    'specialization' => $provider->specialization,
                ]);
            }
        }

        // Add patient users
        foreach ($clinic->patients as $patient) {
            if ($patient->user) {
                $users->push([
                    'id' => $patient->user->id,
                    'name' => $patient->user->name,
                    'email' => $patient->user->email,
                    'type' => 'patient',
                    'role' => 'patient',
                ]);
            }
        }

        return response()->json($users->unique('id')->values());
    }

    /**
     * Get clinic statistics.
     */
    public function getStats($id)
    {
        $clinic = Clinic::findOrFail($id);
        return response()->json($clinic->stats);
    }
}

<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class ImpersonationService
{
    const IMPERSONATOR_SESSION_KEY = 'impersonator_id';
    const IMPERSONATED_SESSION_KEY = 'impersonated_id';

    /**
     * Start impersonating a user
     *
     * @param User $userToImpersonate
     * @return bool
     */
    public function impersonate(User $userToImpersonate): bool
    {
        $currentUser = Auth::user();

        // Only allow superadmin/admin to impersonate
        if (!$currentUser || !$currentUser->hasRole(['admin', 'super_admin'])) {
            return false;
        }

        // Cannot impersonate yourself
        if ($currentUser->id === $userToImpersonate->id) {
            return false;
        }

        // Store the original user ID in session
        Session::put(self::IMPERSONATOR_SESSION_KEY, $currentUser->id);
        Session::put(self::IMPERSONATED_SESSION_KEY, $userToImpersonate->id);

        // Login as the target user
        Auth::login($userToImpersonate);

        // Log the impersonation for audit trail
        Log::info('User impersonation started', [
            'impersonator_id' => $currentUser->id,
            'impersonator_email' => $currentUser->email,
            'impersonated_id' => $userToImpersonate->id,
            'impersonated_email' => $userToImpersonate->email,
        ]);

        return true;
    }

    /**
     * Stop impersonating and return to original user
     *
     * @return bool
     */
    public function stopImpersonating(): bool
    {
        if (!$this->isImpersonating()) {
            return false;
        }

        $impersonatorId = Session::get(self::IMPERSONATOR_SESSION_KEY);
        $impersonatedId = Session::get(self::IMPERSONATED_SESSION_KEY);

        // Find the original user
        $originalUser = User::find($impersonatorId);

        if (!$originalUser) {
            return false;
        }

        // Clear impersonation session data
        Session::forget(self::IMPERSONATOR_SESSION_KEY);
        Session::forget(self::IMPERSONATED_SESSION_KEY);

        // Login back as original user
        Auth::login($originalUser);

        // Log the end of impersonation
        Log::info('User impersonation ended', [
            'impersonator_id' => $impersonatorId,
            'impersonated_id' => $impersonatedId,
        ]);

        return true;
    }

    /**
     * Check if currently impersonating
     *
     * @return bool
     */
    public function isImpersonating(): bool
    {
        return Session::has(self::IMPERSONATOR_SESSION_KEY) && 
               Session::has(self::IMPERSONATED_SESSION_KEY);
    }

    /**
     * Get the original user (impersonator)
     *
     * @return User|null
     */
    public function getImpersonator(): ?User
    {
        if (!$this->isImpersonating()) {
            return null;
        }

        $impersonatorId = Session::get(self::IMPERSONATOR_SESSION_KEY);
        return User::find($impersonatorId);
    }

    /**
     * Get the impersonated user
     *
     * @return User|null
     */
    public function getImpersonated(): ?User
    {
        if (!$this->isImpersonating()) {
            return null;
        }

        $impersonatedId = Session::get(self::IMPERSONATED_SESSION_KEY);
        return User::find($impersonatedId);
    }
}

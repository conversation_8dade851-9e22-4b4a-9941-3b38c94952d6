<?php

namespace App\Http\Controllers;

use App\Services\HealthWellnessService;
use App\Models\ChatConversation;
use App\Models\Patient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class WellnessController extends Controller
{
    private $wellnessService;

    public function __construct(HealthWellnessService $wellnessService)
    {
        $this->wellnessService = $wellnessService;
    }

    /**
     * Start a new wellness conversation with Maya
     */
    public function startConversation(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user || $user->role !== 'patient' || !$user->patient) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'initial_message' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            // Create new conversation
            $conversation = ChatConversation::create([
                'patient_id' => $user->patient->id,
                'title' => $request->title ?: 'Wellness Chat with Maya',
                'messages' => [],
                'health_concerns' => [],
                'recommendations' => []
            ]);

            $response = [
                'conversation_id' => $conversation->id,
                'title' => $conversation->title,
                'maya_intro' => "Hi there! I'm Maya, your wellness companion here at Medroid! 🌟 I'm super excited to chat with you about all things health and wellness - from nutrition and fitness to mental wellness and lifestyle tips. What's on your mind today?"
            ];

            // If there's an initial message, process it
            if ($request->initial_message) {
                $chatResponse = $this->wellnessService->chat(
                    $request->initial_message,
                    [],
                    $user->patient->id,
                    $conversation->id
                );

                if ($chatResponse['success']) {
                    // Update conversation with messages
                    $messages = [
                        [
                            'role' => 'user',
                            'content' => $request->initial_message,
                            'timestamp' => now()->toISOString()
                        ],
                        [
                            'role' => 'assistant',
                            'content' => $chatResponse['response'],
                            'timestamp' => now()->toISOString(),
                            'grounding_used' => $chatResponse['grounding_used'],
                            'provider' => $chatResponse['provider']
                        ]
                    ];

                    $conversation->update(['messages' => $messages]);

                    $response['maya_response'] = $chatResponse['response'];
                    $response['grounding_used'] = $chatResponse['grounding_used'];
                    $response['provider'] = $chatResponse['provider'];
                }
            }

            return response()->json($response, 201);

        } catch (\Exception $e) {
            Log::error('Wellness conversation start failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'error' => 'Failed to start wellness conversation'
            ], 500);
        }
    }

    /**
     * Send a message to Maya in an existing conversation
     */
    public function sendMessage(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user || $user->role !== 'patient' || !$user->patient) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $validator = Validator::make($request->all(), [
                'conversation_id' => 'required|integer|exists:chat_conversations,id',
                'message' => 'required|string|max:2000'
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            // Get conversation
            $conversation = ChatConversation::where('id', $request->conversation_id)
                ->where('patient_id', $user->patient->id)
                ->first();

            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            // Get conversation history
            $conversationHistory = $conversation->messages ?? [];

            // Get Maya's response
            $chatResponse = $this->wellnessService->chat(
                $request->message,
                $conversationHistory,
                $user->patient->id,
                $conversation->id
            );

            if (!$chatResponse['success']) {
                return response()->json([
                    'error' => 'Failed to get response from Maya',
                    'details' => $chatResponse['error'] ?? 'Unknown error'
                ], 500);
            }

            // Add new messages to conversation
            $newMessages = [
                [
                    'role' => 'user',
                    'content' => $request->message,
                    'timestamp' => now()->toISOString()
                ],
                [
                    'role' => 'assistant',
                    'content' => $chatResponse['response'],
                    'timestamp' => now()->toISOString(),
                    'grounding_used' => $chatResponse['grounding_used'],
                    'provider' => $chatResponse['provider'],
                    'used_fallback' => $chatResponse['used_fallback'] ?? false
                ]
            ];

            $allMessages = array_merge($conversationHistory, $newMessages);
            $conversation->update(['messages' => $allMessages]);

            return response()->json([
                'maya_response' => $chatResponse['response'],
                'grounding_used' => $chatResponse['grounding_used'],
                'provider' => $chatResponse['provider'],
                'used_fallback' => $chatResponse['used_fallback'] ?? false,
                'conversation_id' => $conversation->id
            ]);

        } catch (\Exception $e) {
            Log::error('Wellness message send failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'conversation_id' => $request->conversation_id ?? null
            ]);

            return response()->json([
                'error' => 'Failed to send message to Maya'
            ], 500);
        }
    }

    /**
     * Get wellness conversation history
     */
    public function getConversation(Request $request, $conversationId)
    {
        try {
            $user = Auth::user();
            
            if (!$user || $user->role !== 'patient' || !$user->patient) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $conversation = ChatConversation::where('id', $conversationId)
                ->where('patient_id', $user->patient->id)
                ->first();

            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            return response()->json([
                'conversation_id' => $conversation->id,
                'title' => $conversation->title,
                'messages' => $conversation->messages ?? [],
                'health_concerns' => $conversation->health_concerns ?? [],
                'recommendations' => $conversation->recommendations ?? [],
                'created_at' => $conversation->created_at,
                'updated_at' => $conversation->updated_at
            ]);

        } catch (\Exception $e) {
            Log::error('Get wellness conversation failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId
            ]);

            return response()->json([
                'error' => 'Failed to get conversation'
            ], 500);
        }
    }

    /**
     * Get all wellness conversations for the user
     */
    public function getConversations(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user || $user->role !== 'patient' || !$user->patient) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $conversations = ChatConversation::where('patient_id', $user->patient->id)
                ->orderBy('updated_at', 'desc')
                ->paginate(20);

            return response()->json([
                'conversations' => $conversations->items(),
                'pagination' => [
                    'current_page' => $conversations->currentPage(),
                    'last_page' => $conversations->lastPage(),
                    'per_page' => $conversations->perPage(),
                    'total' => $conversations->total()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Get wellness conversations failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'error' => 'Failed to get conversations'
            ], 500);
        }
    }

    /**
     * Get Maya's service status
     */
    public function getStatus()
    {
        try {
            return response()->json([
                'service' => 'Maya Wellness Service',
                'status' => 'active',
                'provider' => $this->wellnessService->getProvider(),
                'features' => [
                    'grounding_search' => true,
                    'user_context' => true,
                    'conversation_memory' => true,
                    'provider_fallback' => true
                ],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Get wellness status failed', ['error' => $e->getMessage()]);

            return response()->json([
                'service' => 'Maya Wellness Service',
                'status' => 'error',
                'error' => 'Service unavailable'
            ], 500);
        }
    }
}

<?php

namespace App\Services\Agents;

use App\Models\BotPersona;

class VisualPromptAgent extends BaseAgent
{
    protected function getAgentName(): string
    {
        return 'VisualPromptAgent';
    }

    protected function getDefaultOptions(): array
    {
        return [
            'temperature' => 0.7,
            'max_completion_tokens' => 400,
            'top_p' => 0.9,
        ];
    }

    protected function createSystemPrompt(array $context = []): string
    {
        return "You are a Visual Content Strategist and Midjourney Prompt Expert specializing in health and wellness imagery.

Your expertise includes:
- Creating detailed, effective Midjourney prompts
- Understanding visual storytelling for health content
- Optimizing prompts for Instagram-worthy results
- Balancing aesthetic appeal with authentic representation
- Incorporating current visual trends in wellness space

MIDJOURNEY PROMPT STRUCTURE:
1. SUBJECT - Main focus (person, activity, object)
2. SETTING - Environment and context
3. STYLE - Photography style, lighting, mood
4. COMPOSITION - Framing, angles, perspective
5. TECHNICAL - Camera settings, quality parameters
6. PARAMETERS - Midjourney-specific settings

VISUAL TRENDS IN HEALTH/WELLNESS (2024):
- Natural lighting and authentic moments
- Diverse representation and body positivity
- Clean, minimalist aesthetics
- Warm, inviting color palettes
- Action shots showing real movement
- Close-ups highlighting emotions and details

MIDJOURNEY BEST PRACTICES:
- Use specific, descriptive language
- Include lighting and mood descriptors
- Specify camera angles and composition
- Add quality and style parameters
- Avoid overly complex or conflicting elements

OUTPUT FORMAT:
VISUAL_PROMPT: [Complete Midjourney prompt optimized for the content]

Create prompts that will generate professional, engaging, and authentic health/wellness imagery that perfectly complements the content strategy.";
    }

    public function process(array $input): array
    {
        $this->validateInput($input, ['persona', 'strategy']);
        
        $persona = $input['persona'];
        $strategy = $input['strategy'];
        $context = $input['context'] ?? [];
        
        $userPrompt = $this->createUserPrompt($persona, $strategy, $context);
        $systemPrompt = $this->createSystemPrompt($context);
        
        $response = $this->executeAgent($systemPrompt, $userPrompt);
        
        return $this->parseVisualPromptResponse($response, $persona);
    }

    protected function createUserPrompt(BotPersona $persona, array $strategy, array $context): string
    {
        $strategyData = $strategy['strategy'] ?? $strategy;
        
        $prompt = "PERSONA PROFILE:
Name: {$persona->full_name} (@{$persona->handle})
Content Focus: {$persona->content_focus}
Tone: {$persona->tone}
Visual Style Preference: {$strategyData['visual_style']}

CONTENT STRATEGY:
Theme: {$strategyData['content_theme']}
Target Emotion: {$strategyData['target_emotion']}
Content Type: {$strategyData['content_type']}
Key Message: {$strategyData['key_message']}

VISUAL REQUIREMENTS:
- Must align with {$persona->content_focus} expertise
- Should evoke {$strategyData['target_emotion']} emotion
- Appropriate for Instagram health/wellness audience
- Professional yet authentic appearance
- Diverse and inclusive representation

CONTENT FOCUS VISUAL GUIDELINES:
";

        // Add focus-specific visual guidance
        switch ($persona->content_focus) {
            case 'yoga':
                $prompt .= "- Yoga poses, meditation, peaceful settings
- Natural lighting, serene environments
- Yoga mats, props, peaceful expressions
- Indoor studios or outdoor nature settings
- Flexible, graceful movements and positions";
                break;
            case 'fitness':
                $prompt .= "- Workout activities, gym equipment, active movements
- Dynamic lighting, energetic environments
- Exercise equipment, athletic wear, determined expressions
- Gym settings or outdoor fitness locations
- Strong, powerful movements and positions";
                break;
            case 'nutrition':
                $prompt .= "- Healthy foods, meal preparation, cooking scenes
- Bright, appetizing lighting, clean kitchen environments
- Fresh ingredients, colorful foods, satisfied expressions
- Kitchen settings or dining areas
- Food styling and preparation activities";
                break;
            case 'mental health':
                $prompt .= "- Peaceful moments, self-care activities, calming scenes
- Soft, warm lighting, comfortable environments
- Relaxation props, journals, calm expressions
- Home settings or peaceful outdoor locations
- Gentle, nurturing activities and positions";
                break;
            case 'cycling':
                $prompt .= "- Cycling activities, bikes, outdoor adventures
- Natural lighting, scenic environments
- Bicycles, cycling gear, adventurous expressions
- Road, trail, or urban cycling settings
- Dynamic cycling movements and positions";
                break;
            case 'cooking':
                $prompt .= "- Cooking activities, food preparation, kitchen scenes
- Warm, inviting lighting, cozy kitchen environments
- Cooking utensils, fresh ingredients, joyful expressions
- Kitchen settings with cooking in progress
- Active cooking and food preparation";
                break;
            case 'sleep wellness':
                $prompt .= "- Peaceful sleep environments, relaxation scenes
- Soft, dim lighting, calming bedroom settings
- Comfortable bedding, sleep accessories, peaceful expressions
- Bedroom or relaxation spaces
- Restful, calming activities and positions";
                break;
            default:
                $prompt .= "- General wellness activities, healthy lifestyle scenes
- Natural, balanced lighting, welcoming environments
- Health-related props, positive expressions
- Various wellness-appropriate settings
- Balanced, healthy lifestyle activities";
        }

        $prompt .= "\n\nTIME CONTEXT:
Current time: " . now()->format('H:i') . " on " . now()->format('l') . "
Season: " . $this->getCurrentSeason() . "

TECHNICAL REQUIREMENTS:
- Instagram-optimized aspect ratio (1:1 or 4:5)
- High quality, professional appearance
- Authentic, not overly staged
- Diverse representation when showing people
- Appropriate for health/wellness brand

Create a detailed Midjourney prompt that will generate the perfect visual to accompany this content strategy. Focus on authenticity, professionalism, and emotional connection.";

        return $prompt;
    }

    protected function parseVisualPromptResponse(string $response, BotPersona $persona): array
    {
        // Extract visual prompt
        $promptMatch = [];
        if (preg_match('/VISUAL_PROMPT:\s*(.*?)$/s', $response, $promptMatch)) {
            $visualPrompt = trim($promptMatch[1]);
        } else {
            // Fallback: use entire response as prompt
            $visualPrompt = trim($response);
        }

        // Enhance prompt with Midjourney parameters
        $enhancedPrompt = $this->enhancePromptWithParameters($visualPrompt, $persona);

        // Validate prompt quality
        $quality = $this->validatePromptQuality($enhancedPrompt);

        return [
            'visual_prompt' => $enhancedPrompt,
            'base_prompt' => $visualPrompt,
            'prompt_length' => strlen($enhancedPrompt),
            'quality_score' => $quality['score'],
            'quality_issues' => $quality['issues'],
            'agent' => $this->agentName,
            'timestamp' => now()->toISOString(),
        ];
    }

    protected function enhancePromptWithParameters(string $basePrompt, BotPersona $persona): string
    {
        // Clean the base prompt
        $prompt = preg_replace('/^VISUAL_PROMPT:\s*/i', '', $basePrompt);
        $prompt = trim($prompt);

        // Add professional photography parameters
        $enhancements = [
            'bright and airy',
            'natural lighting',
            'clean aesthetic',
            'professional photography',
            'high quality',
            'Instagram-worthy',
        ];

        // Add Midjourney parameters for single image generation
        $parameters = [
            '--ar 1:1',
            '--v 6.1',
            '--quality 1',
            '--no grid',
            '--no collage',
            '--no multiple',
        ];

        // Combine everything
        $enhancedPrompt = $prompt . ', ' . implode(', ', $enhancements) . ' ' . implode(' ', $parameters);

        return $enhancedPrompt;
    }

    protected function validatePromptQuality(string $prompt): array
    {
        $issues = [];
        $score = 100;

        // Check minimum length
        if (strlen($prompt) < 50) {
            $issues[] = 'Prompt too short for detailed generation';
            $score -= 30;
        }

        // Check maximum length
        if (strlen($prompt) > 500) {
            $issues[] = 'Prompt too long, may cause confusion';
            $score -= 20;
        }

        // Check for essential elements
        $essentialElements = ['lighting', 'quality', 'professional'];
        foreach ($essentialElements as $element) {
            if (!str_contains(strtolower($prompt), $element)) {
                $issues[] = "Missing essential element: {$element}";
                $score -= 10;
            }
        }

        // Check for Midjourney parameters
        if (!str_contains($prompt, '--ar')) {
            $issues[] = 'Missing aspect ratio parameter';
            $score -= 15;
        }

        if (!str_contains($prompt, '--v')) {
            $issues[] = 'Missing version parameter';
            $score -= 10;
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
        ];
    }

    protected function getCurrentSeason(): string
    {
        $month = now()->month;
        
        if ($month >= 3 && $month <= 5) return 'Spring';
        if ($month >= 6 && $month <= 8) return 'Summer';
        if ($month >= 9 && $month <= 11) return 'Fall';
        return 'Winter';
    }

    public function test(): array
    {
        $testPersona = new BotPersona();
        $testPersona->first_name = 'Maya';
        $testPersona->last_name = 'Yoga';
        $testPersona->handle = 'maya_yoga';
        $testPersona->content_focus = 'yoga';
        $testPersona->tone = 'peaceful';

        $testStrategy = [
            'strategy' => [
                'content_theme' => 'Morning yoga flow',
                'target_emotion' => 'peace',
                'content_type' => 'instructional',
                'key_message' => 'Start your day mindfully',
                'visual_style' => 'serene and natural',
            ]
        ];

        $result = $this->process([
            'persona' => $testPersona,
            'strategy' => $testStrategy,
            'context' => ['test_mode' => true],
        ]);

        return [
            'agent' => $this->agentName,
            'test_successful' => !empty($result['visual_prompt']),
            'prompt_length' => strlen($result['visual_prompt'] ?? ''),
            'quality_score' => $result['quality_score'] ?? 0,
            'has_parameters' => str_contains($result['visual_prompt'] ?? '', '--ar'),
            'sample_prompt' => substr($result['visual_prompt'] ?? '', 0, 100) . '...',
        ];
    }
}

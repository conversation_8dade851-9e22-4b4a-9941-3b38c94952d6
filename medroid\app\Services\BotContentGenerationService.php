<?php

namespace App\Services;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Services\ChatServiceManager;
use App\Services\Agents\AgentManager;
use Illuminate\Support\Facades\Log;

class BotContentGenerationService
{
    protected AgentManager $agentManager;

    public function __construct(ChatServiceManager $chatService)
    {
        $this->agentManager = new AgentManager($chatService);
    }

    /**
     * Generate a complete post for a bot persona using multi-agent system
     */
    public function generatePost(BotPersona $persona): ?AutomatedPost
    {
        try {
            Log::info('Starting multi-agent post generation', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
            ]);

            // Check if persona already has a post today to prevent duplicates
            $existingPost = AutomatedPost::where('bot_persona_id', $persona->id)
                ->whereDate('created_at', today())
                ->first();

            if ($existingPost) {
                Log::info('<PERSON><PERSON> already has a post today, skipping', [
                    'persona_id' => $persona->id,
                    'existing_post_id' => $existingPost->id,
                    'persona_name' => $persona->full_name,
                ]);
                return null;
            }

            // Generate content using multi-agent system
            $result = $this->agentManager->generatePostContent($persona, [
                'generation_time' => now()->toISOString(),
                'content_focus' => $persona->content_focus,
            ]);

            if (!$result || !isset($result['final_content'])) {
                Log::error('Multi-agent system failed to generate content', ['persona_id' => $persona->id]);
                return null;
            }

            $finalContent = $result['final_content'];
            $qualityScores = $result['quality_scores'] ?? [];

            // Create the automated post with enhanced data
            $post = AutomatedPost::create([
                'bot_user_id' => $persona->botUser->id,
                'bot_persona_id' => $persona->id,
                'caption' => $finalContent['caption'],
                'hashtags' => $finalContent['hashtags'],
                'status' => 'draft',
                'generation_prompts' => [
                    'visual_prompt' => $finalContent['visual_prompt'],
                    'agent_system' => 'multi-agent-v1',
                    'quality_scores' => $qualityScores,
                    'generated_at' => now()->toISOString(),
                    'agent_metrics' => $this->agentManager->getExecutionMetrics(),
                ],
            ]);

            Log::info('Multi-agent post generated successfully', [
                'persona_id' => $persona->id,
                'post_id' => $post->id,
                'caption_length' => strlen($finalContent['caption']),
                'hashtag_count' => count($finalContent['hashtags']),
                'quality_score' => $qualityScores['quality_score'] ?? 'unknown',
                'needs_optimization' => $result['needs_optimization'] ?? false,
            ]);

            return $post;

        } catch (\Exception $e) {
            Log::error('Error in multi-agent post generation', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    /**
     * Fallback method for generating caption and hashtags (legacy support)
     * @deprecated Use multi-agent system instead
     */
    public function generateCaption(BotPersona $persona): ?array
    {
        Log::info('Generating caption for existing post', [
            'persona_id' => $persona->id,
            'persona_name' => $persona->full_name,
        ]);

        try {
            // Use the multi-agent system to generate content
            $result = $this->agentManager->generatePostContent($persona);

            if ($result && isset($result['final_content'])) {
                $finalContent = $result['final_content'];

                if (isset($finalContent['caption']) && isset($finalContent['hashtags'])) {
                    Log::info('Caption generation successful', [
                        'persona_id' => $persona->id,
                        'caption_length' => strlen($finalContent['caption']),
                        'hashtag_count' => count($finalContent['hashtags']),
                    ]);

                    return [
                        'caption' => $finalContent['caption'],
                        'hashtags' => $finalContent['hashtags'],
                    ];
                }
            }

            Log::error('Invalid result structure from multi-agent system', [
                'persona_id' => $persona->id,
                'result_keys' => $result ? array_keys($result) : 'null',
                'final_content_keys' => ($result && isset($result['final_content'])) ? array_keys($result['final_content']) : 'null',
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Error in caption generation', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    /**
     * Get the agent manager for testing and monitoring
     */
    public function getAgentManager(): AgentManager
    {
        return $this->agentManager;
    }

    /**
     * Test the multi-agent system
     */
    public function testAgentSystem(): array
    {
        return $this->agentManager->testAllAgents();
    }

    /**
     * Get system health status
     */
    public function getSystemHealth(): array
    {
        return $this->agentManager->getHealthStatus();
    }

    // Legacy methods removed - now using multi-agent system

    // All legacy parsing and generation methods removed
    // Now using sophisticated multi-agent system for content generation
}

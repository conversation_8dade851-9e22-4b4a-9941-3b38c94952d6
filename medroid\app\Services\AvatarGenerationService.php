<?php

namespace App\Services;

use App\Models\BotPersona;
use App\Services\GoogleImagenService;
use Illuminate\Support\Facades\Log;

class AvatarGenerationService
{
    private GoogleImagenService $googleImagenService;

    public function __construct(GoogleImagenService $googleImagenService)
    {
        $this->googleImagenService = $googleImagenService;
    }

    /**
     * Generate avatar and face reference for a bot persona
     */
    public function generateAvatarForPersona(BotPersona $persona): bool
    {
        try {
            Log::info('Starting avatar generation for persona', [
                'persona_id' => $persona->id,
                'persona_name' => $persona->full_name,
            ]);

            // Generate physical description if not exists
            if (empty($persona->physical_description)) {
                $physicalDescription = $this->generatePhysicalDescription($persona);
                $persona->update(['physical_description' => $physicalDescription]);
            }

            // Generate avatar image
            $avatarPrompt = $this->createAvatarPrompt($persona);
            $avatarResult = $this->generateSingleImage($avatarPrompt, 'avatar');

            if (!$avatarResult) {
                Log::error('Failed to generate avatar', ['persona_id' => $persona->id]);
                return false;
            }

            // Update persona with generated avatar URL (no face reference needed)
            $persona->update([
                'avatar_url' => $avatarResult['image_url'],
                'face_reference_url' => null, // Clear any existing face reference
            ]);

            Log::info('Avatar generation completed successfully', [
                'persona_id' => $persona->id,
                'avatar_url' => $avatarResult['image_url'],
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error generating avatar for persona', [
                'persona_id' => $persona->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Generate physical description based on persona characteristics
     */
    private function generatePhysicalDescription(BotPersona $persona): string
    {
        // Create diverse, realistic physical descriptions based on persona
        $descriptions = [
            'Hannah Kim' => 'Asian woman, late 20s, shoulder-length black hair, warm brown eyes, gentle smile, athletic build from yoga practice',
            'Luis Rodriguez' => 'Hispanic man, early 30s, short dark hair, brown eyes, athletic runner build, confident smile, tan complexion',
            'Javier Martinez' => 'Latino man, mid 40s, salt-and-pepper beard, kind brown eyes, creative artistic style, warm expression',
            'Leila Haddad' => 'Middle Eastern woman, early 30s, long dark curly hair, expressive brown eyes, professional yet approachable',
            'Ingrid Olsen' => 'Scandinavian woman, late 30s, blonde hair in a bun, blue eyes, minimalist style, serene expression',
        ];

        $fullName = $persona->full_name;
        
        if (isset($descriptions[$fullName])) {
            return $descriptions[$fullName];
        }

        // Generate generic description based on name and characteristics
        $genderTerms = $this->inferGenderFromName($persona->first_name);
        $ageGroup = $this->getAgeGroup($persona->age);
        
        return "{$genderTerms['term']} {$ageGroup}, professional appearance, warm and approachable expression, {$genderTerms['hair_style']}, {$genderTerms['eye_color']} eyes";
    }

    /**
     * Create brand-focused avatar prompt
     */
    private function createAvatarPrompt(BotPersona $persona): string
    {
        // Create brand logo/symbol style avatars instead of individual faces
        $brandElements = $this->getBrandElementsForFocus($persona->content_focus);

        $basePrompt = "Professional brand logo design for {$persona->first_name} {$persona->last_name}, ";
        $basePrompt .= "{$brandElements}, clean minimalist design, ";
        $basePrompt .= "modern aesthetic, suitable for social media profile, ";
        $basePrompt .= "circular format, professional branding, high quality";

        return $this->googleImagenService->enhancePrompt($basePrompt);
    }

    /**
     * Get brand elements based on content focus
     */
    private function getBrandElementsForFocus(string $focus): string
    {
        $brandMap = [
            'yoga' => 'lotus flower symbol, zen elements, peaceful colors',
            'fitness' => 'strength symbols, dynamic elements, energetic colors',
            'nutrition' => 'healthy food elements, fresh colors, natural symbols',
            'mental health' => 'calming symbols, supportive elements, gentle colors',
            'cycling' => 'bicycle elements, movement symbols, adventure colors',
            'cooking' => 'culinary symbols, kitchen elements, warm colors',
            'gut health' => 'wellness symbols, health elements, natural colors',
            'home wellness' => 'home symbols, sanctuary elements, peaceful colors',
            'health tech' => 'tech symbols, modern elements, innovative colors',
            'physical therapy' => 'recovery symbols, movement elements, healing colors',
            'brain health' => 'brain symbols, cognitive elements, smart colors',
            'cold therapy' => 'ice elements, resilience symbols, cool colors',
            'art therapy' => 'artistic elements, creative symbols, expressive colors',
            'sleep wellness' => 'sleep symbols, rest elements, calming colors',
            'senior wellness' => 'wisdom symbols, mature elements, warm colors',
            'nature' => 'natural elements, earth symbols, organic colors',
            'wellness' => 'balance symbols, harmony elements, holistic colors',
            'meditation' => 'mindfulness symbols, peace elements, serene colors',
        ];

        return $brandMap[$focus] ?? 'wellness symbols, health elements, balanced colors';
    }

    /**
     * Generate enhanced image prompt with content focus
     */
    public function createContentFocusedPrompt(BotPersona $persona, string $baseScenario): string
    {
        // Use content-focused approach instead of trying to maintain face consistency
        return $this->googleImagenService->enhancePromptForContent($baseScenario, $persona);

        // Use face reference for consistency
        $consistentPrompt = "{$baseScenario}, person with appearance matching {$persona->physical_description}";
        
        return $this->googleImagenService->enhancePrompt($consistentPrompt);
    }

    /**
     * Generate a single image using Midjourney API
     */
    private function generateSingleImage(string $prompt, string $type): ?array
    {
        try {
            Log::info("Generating {$type} image", ['prompt' => $prompt]);

            // Use Google Imagen API directly for avatar generation
            $response = $this->googleImagenService->generateImageDirect($prompt);

            if ($response && isset($response['image_url'])) {
                Log::info("{$type} generation successful", [
                    'image_url' => $response['image_url'],
                    'task_id' => $response['task_id'] ?? 'unknown',
                ]);

                return $response;
            }

            Log::error("Failed to generate {$type} - no image URL returned");
            return null;

        } catch (\Exception $e) {
            Log::error("Error generating {$type} image", [
                'error' => $e->getMessage(),
                'prompt' => $prompt,
            ]);
            return null;
        }
    }

    /**
     * Infer gender characteristics from first name
     */
    private function inferGenderFromName(string $firstName): array
    {
        $femaleNames = ['Hannah', 'Leila', 'Ingrid', 'Maria', 'Sarah', 'Emma', 'Sofia'];
        $maleNames = ['Luis', 'Javier', 'David', 'Carlos', 'Michael', 'James', 'Robert'];

        if (in_array($firstName, $femaleNames)) {
            return [
                'term' => 'woman',
                'hair_style' => 'shoulder-length hair',
                'eye_color' => 'brown'
            ];
        } elseif (in_array($firstName, $maleNames)) {
            return [
                'term' => 'man',
                'hair_style' => 'short hair',
                'eye_color' => 'brown'
            ];
        }

        // Default neutral
        return [
            'term' => 'person',
            'hair_style' => 'neat hairstyle',
            'eye_color' => 'brown'
        ];
    }

    /**
     * Get age group description
     */
    private function getAgeGroup(int $age): string
    {
        if ($age < 25) return 'early 20s';
        if ($age < 30) return 'late 20s';
        if ($age < 35) return 'early 30s';
        if ($age < 40) return 'late 30s';
        if ($age < 45) return 'early 40s';
        if ($age < 50) return 'late 40s';
        return 'middle-aged';
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class WaitlistInvitation extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'token',
        'club_type',
        'membership_level',
        'status',
        'expires_at',
        'sent_at',
        'used_at',
        'created_by',
        'used_by',
        'metadata',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'sent_at' => 'datetime',
        'used_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Generate a unique token for the invitation
     */
    public static function generateToken(): string
    {
        do {
            $token = Str::random(32);
        } while (self::where('token', $token)->exists());

        return $token;
    }

    /**
     * Check if the invitation is valid
     */
    public function isValid(): bool
    {
        return $this->status === 'sent' && 
               $this->expires_at > now() && 
               !$this->used_at;
    }

    /**
     * Mark invitation as used
     */
    public function markAsUsed(User $user): void
    {
        $this->update([
            'status' => 'used',
            'used_at' => now(),
            'used_by' => $user->id,
        ]);
    }

    /**
     * Mark invitation as expired
     */
    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Get the user who created this invitation
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who used this invitation
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'used_by');
    }

    /**
     * Get the waitlist request associated with this invitation
     */
    public function waitlistRequest()
    {
        return $this->hasOne(WaitlistRequest::class, 'invitation_id');
    }

    /**
     * Scope for valid invitations
     */
    public function scopeValid($query)
    {
        return $query->where('status', 'sent')
                    ->where('expires_at', '>', now())
                    ->whereNull('used_at');
    }

    /**
     * Scope for expired invitations
     */
    public function scopeExpired($query)
    {
        return $query->where(function ($q) {
            $q->where('expires_at', '<=', now())
              ->orWhere('status', 'expired');
        });
    }

    /**
     * Get club display information
     */
    public function getClubDisplayInfo(): array
    {
        $clubNames = [
            'founder' => 'Medroid Founders Club',
            'premium' => 'Premium Club',
            'regular' => 'Regular Club',
        ];

        $benefits = [
            'founder' => [
                'verified_badge' => true,
                'priority_support' => true,
                'exclusive_features' => true,
                'bonus_credits' => 10.00,
                'early_access' => true,
                'slack_access' => true,
                'founder_access' => true,
            ],
            'premium' => [
                'verified_badge' => true,
                'priority_support' => true,
                'exclusive_features' => true,
                'bonus_credits' => 5.00,
                'early_access' => false,
                'slack_access' => false,
                'founder_access' => false,
            ],
            'regular' => [
                'verified_badge' => false,
                'priority_support' => false,
                'exclusive_features' => false,
                'bonus_credits' => 0.00,
                'early_access' => false,
                'slack_access' => false,
                'founder_access' => false,
            ],
        ];

        return [
            'club_name' => $clubNames[$this->club_type] ?? ucfirst($this->club_type) . ' Club',
            'benefits' => $benefits[$this->club_type] ?? $benefits['regular'],
        ];
    }
}

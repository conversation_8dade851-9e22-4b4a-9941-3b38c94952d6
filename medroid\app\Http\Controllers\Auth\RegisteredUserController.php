<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Services\WaitlistService;
use App\Services\ClubService;
use App\Services\ReferralService;
use App\Models\Clinic;
use App\Models\ChatConversation;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    protected $waitlistService;
    protected $clubService;
    protected $referralService;

    public function __construct(
        WaitlistService $waitlistService,
        ClubService $clubService,
        ReferralService $referralService
    ) {
        $this->waitlistService = $waitlistService;
        $this->clubService = $clubService;
        $this->referralService = $referralService;
    }

    /**
     * Show the registration page.
     */
    public function create(Request $request): Response
    {
        $token = $request->query('token');
        $invitationValid = false;
        $invitation = null;

        // Check if there's a valid invitation token
        if ($token) {
            $validation = $this->waitlistService->validateInvitationToken($token);
            if ($validation['valid']) {
                $invitationValid = true;
                $invitation = $validation['invitation'];
            }
        }

        $waitlistStatus = $this->waitlistService->getWaitlistStatus();

        return Inertia::render('auth/Register', [
            'waitlistStatus' => $waitlistStatus,
            'invitationToken' => $token,
            'invitationValid' => $invitationValid,
            'invitation' => $invitation ? [
                'email' => $invitation->email,
                'club_type' => $invitation->club_type,
                'membership_level' => $invitation->membership_level,
                'club_info' => $invitation->getClubDisplayInfo(),
            ] : null,
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $invitationToken = $request->input('invitation_token');
        $invitation = null;

        // Check for invitation token first
        if ($invitationToken) {
            $validation = $this->waitlistService->validateInvitationToken($invitationToken);

            if (!$validation['valid']) {
                return back()->withErrors([
                    'invitation_token' => $validation['message']
                ])->withInput();
            }

            $invitation = $validation['invitation'];

            // Ensure email matches invitation
            if ($invitation->email !== $request->email) {
                return back()->withErrors([
                    'email' => 'Email must match the invitation email: ' . $invitation->email
                ])->withInput();
            }
        } else {
            // No invitation token - check waitlist restrictions only if waitlist is enabled
            if ($this->waitlistService->isWaitlistEnabled()) {
                if (!$this->waitlistService->canBypassWaitlist($request->email)) {
                    // Validate referral code for waitlist
                    $referralValidation = $this->waitlistService->validateReferralCode($request->referral_code);

                    if (!$referralValidation['valid']) {
                        return back()->withErrors([
                            'referral_code' => $referralValidation['message']
                        ])->withInput();
                    }
                }
            }
            // If waitlist is disabled, allow normal registration without restrictions
        }

        // Determine referral code validation rules
        $referralCodeRules = 'nullable|string|max:255';
        if ($this->waitlistService->isWaitlistEnabled() && !$invitation) {
            $referralCodeRules = 'required|string|max:255';
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => 'required|in:patient,provider,admin',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date|before:today',
            'referral_code' => $referralCodeRules,
        ]);

        // Log waitlist activity
        $this->waitlistService->logWaitlistActivity('registration_attempt', [
            'email' => $request->email,
            'referral_code' => $request->referral_code,
            'has_referral_code' => !empty($request->referral_code),
            'anonymous_conversation_id' => 'nullable|string',
            'anonymous_id' => 'nullable|string',
        ]);

        // Handle referral code if provided
        $referredBy = null;
        if ($request->referral_code) {
            $referrer = User::where('referral_code', $request->referral_code)->first();
            if ($referrer) {
                $referredBy = $referrer->id;
            }
        }

        // Determine signup source
        $signupSource = 'web_registration';
        if ($invitation) {
            $signupSource = 'invitation';
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'signup_source' => $signupSource,
            'referred_by' => $referredBy,
        ]);

        // Get default clinic for assignment
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first(); // Fallback to any clinic
        }

        // Create role-specific profile
        if ($request->role === 'patient') {
            Patient::create([
                'user_id' => $user->id,
                'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                'gender' => $request->gender,
                'date_of_birth' => $request->date_of_birth,
                'health_history' => [],
                'allergies' => [],
                'preferences' => [
                    'feed_topics' => [],
                    'notification_settings' => [
                        'push' => true,
                        'email' => true
                    ]
                ],
                'appointment_preferences' => [
                    'preferred_location' => null,
                    'preferred_gender' => null,
                    'preferred_language' => null
                ],
            ]);
        } elseif ($request->role === 'provider') {
            Provider::create([
                'user_id' => $user->id,
                'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                'specialization' => null,
                'license_number' => null,
                'verification_status' => 'verified', // Auto-verify new providers
                'verified_at' => now(),
                'bio' => null,
                'rating' => 0,
                'accepts_insurance' => false,
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'absences' => [],
            ]);
        }

        // Assign role based on user's role
        $user->assignRole($request->role);

        // Transfer anonymous chat conversation if provided
        if ($request->anonymous_conversation_id && $request->anonymous_id && $request->role === 'patient') {
            $this->transferAnonymousConversation($user, $request->anonymous_conversation_id, $request->anonymous_id);
        }

        // Process invitation or referral code
        if ($invitation) {
            // Process invitation registration
            $this->waitlistService->processInvitationRegistration($invitation, $user);
        } elseif ($request->has('referral_code') && !empty($request->referral_code)) {
            $referralCode = $request->referral_code;

            // First check if it's a founder referral code
            $isFounderCode = $this->clubService->checkFounderMembership($user, $referralCode);

            if (!$isFounderCode) {
                // If not a founder code, process as regular referral
                $this->referralService->processReferral($referralCode, $user);
            }
        }

        // Log successful registration
        $this->waitlistService->logWaitlistActivity('registration_success', [
            'user_id' => $user->id,
            'email' => $user->email,
            'referral_code' => $request->referral_code,
            'referral_type' => $request->referral_code ? 'with_code' : 'without_code',
        ]);

        event(new Registered($user));

        Auth::login($user);

        // Role-based redirect after registration
        $redirectRoute = $this->getRoleBasedRedirectRoute($user);
        return to_route($redirectRoute);
    }

    /**
     * Show the founder club signup page.
     */
    public function showFounderSignup(Request $request): Response
    {
        $waitlistStatus = $this->waitlistService->getWaitlistStatus();

        return Inertia::render('auth/FounderSignup', [
            'waitlistStatus' => $waitlistStatus,
            'shareableLink' => route('founder.signup'),
        ]);
    }

    /**
     * Handle founder club signup registration.
     */
    public function storeFounderSignup(Request $request): RedirectResponse
    {
        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => 'required|in:patient,provider',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date|before:today',
        ]);

        // Log founder signup activity
        $this->waitlistService->logWaitlistActivity('founder_signup_attempt', [
            'email' => $request->email,
            'source' => 'shareable_link',
        ]);

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'signup_source' => 'web_registration',
        ]);

        // Get default clinic for assignment
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first(); // Fallback to any clinic
        }

        // Create role-specific profile
        if ($request->role === 'patient') {
            Patient::create([
                'user_id' => $user->id,
                'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                'gender' => $request->gender,
                'date_of_birth' => $request->date_of_birth,
                'health_history' => [],
                'allergies' => [],
                'preferences' => [
                    'feed_topics' => [],
                    'notification_settings' => [
                        'push' => true,
                        'email' => true
                    ]
                ],
                'appointment_preferences' => [
                    'preferred_location' => null,
                    'preferred_gender' => null,
                    'preferred_language' => null
                ],
            ]);
        } elseif ($request->role === 'provider') {
            Provider::create([
                'user_id' => $user->id,
                'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                'specialization' => null,
                'license_number' => null,
                'verification_status' => 'verified', // Auto-verify new providers
                'verified_at' => now(),
                'bio' => null,
                'rating' => 0,
                'accepts_insurance' => false,
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'absences' => [],
            ]);
        }

        // Assign role
        $user->assignRole($request->role);

        // Automatically add to Founder Club
        $this->clubService->createFounderMembership($user, 'MEDROID_FOUNDERS');

        // Log successful founder signup
        $this->waitlistService->logWaitlistActivity('founder_signup_success', [
            'user_id' => $user->id,
            'email' => $user->email,
            'source' => 'shareable_link',
        ]);

        event(new Registered($user));

        Auth::login($user);

        // Role-based redirect after founder signup
        $redirectRoute = $this->getRoleBasedRedirectRoute($user);
        return to_route($redirectRoute);
    }

    /**
     * Transfer anonymous conversation to the newly registered user
     */
    private function transferAnonymousConversation(User $user, string $conversationId, string $anonymousId)
    {
        try {
            Log::info('Attempting to transfer anonymous conversation during registration', [
                'conversation_id' => $conversationId,
                'anonymous_id' => $anonymousId,
                'user_id' => $user->id
            ]);

            // Find the anonymous conversation
            $conversation = ChatConversation::where('id', $conversationId)
                ->where('anonymous_id', $anonymousId)
                ->where('is_anonymous', true)
                ->first();

            if (!$conversation) {
                Log::warning('Anonymous conversation not found during registration transfer', [
                    'conversation_id' => $conversationId,
                    'anonymous_id' => $anonymousId,
                    'user_id' => $user->id
                ]);
                return;
            }

            // Ensure user has a patient profile
            if (!$user->patient) {
                Log::warning('User does not have patient profile for conversation transfer', [
                    'user_id' => $user->id
                ]);
                return;
            }

            // Transfer the conversation to the authenticated user
            $conversation->patient_id = $user->patient->id;
            $conversation->is_anonymous = false;
            $conversation->anonymous_id = null;
            $conversation->save();

            Log::info('Anonymous conversation transferred successfully during registration', [
                'conversation_id' => $conversation->id,
                'user_id' => $user->id,
                'patient_id' => $user->patient->id
            ]);

        } catch (\Exception $e) {
            Log::error("Error transferring anonymous conversation during registration: {$e->getMessage()}", [
                'conversation_id' => $conversationId,
                'anonymous_id' => $anonymousId,
                'user_id' => $user->id,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get the appropriate redirect route based on user role
     */
    private function getRoleBasedRedirectRoute(User $user): string
    {
        switch ($user->role) {
            case 'patient':
                return 'chat';
            case 'provider':
                return 'provider.schedule';
            case 'admin':
            case 'super_admin':
                return 'dashboard';
            default:
                return 'dashboard';
        }
    }
}

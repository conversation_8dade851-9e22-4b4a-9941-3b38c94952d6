<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\ImpersonationService;
use Symfony\Component\HttpFoundation\Response;

class ImpersonationMiddleware
{
    protected $impersonationService;

    public function __construct(ImpersonationService $impersonationService)
    {
        $this->impersonationService = $impersonationService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Add impersonation data to all requests
        if ($this->impersonationService->isImpersonating()) {
            $request->attributes->set('is_impersonating', true);
            $request->attributes->set('impersonator', $this->impersonationService->getImpersonator());
            $request->attributes->set('impersonated', $this->impersonationService->getImpersonated());
        }

        return $next($request);
    }
}

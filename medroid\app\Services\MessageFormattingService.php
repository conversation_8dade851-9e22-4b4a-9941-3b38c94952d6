<?php

namespace App\Services;

class MessageFormattingService
{
    /**
     * Apply universal formatting to AI responses
     * This ensures all AI responses are consistently well-formatted
     */
    public static function formatAIResponse(string $content): string
    {
        if (empty($content)) {
            return $content;
        }

        // Apply comprehensive formatting
        $formatted = self::applyMarkdownFormatting($content);
        $formatted = self::enhanceStructure($formatted);
        $formatted = self::improveReadability($formatted);
        
        return $formatted;
    }

    /**
     * Apply markdown-style formatting with proper spacing
     */
    private static function applyMarkdownFormatting(string $content): string
    {
        // Handle bold text with proper spacing preservation
        // First, normalize any existing bold formatting
        $content = preg_replace('/\*\*\s*([^*\n]+?)\s*\*\*/', '**$1**', $content);
        $content = preg_replace('/__\s*([^_\n]+?)\s*__/', '**$1**', $content);

        // Ensure spaces around bold text are preserved
        $content = preg_replace('/(\w)\*\*([^*\n]+)\*\*(\w)/', '$1 **$2** $3', $content);

        // Handle italic text (*text* or _text_)
        $content = preg_replace('/(?<!\*)\*([^*\n]+)\*(?!\*)/', '*$1*', $content);
        $content = preg_replace('/(?<!_)_([^_\n]+)_(?!_)/', '*$1*', $content);

        // Ensure headers are properly formatted
        $content = preg_replace('/^(#{1,6})\s*(.+)$/m', '$1 $2', $content);

        return $content;
    }

    /**
     * Enhance content structure for better readability
     */
    private static function enhanceStructure(string $content): string
    {
        // Add proper spacing around headers
        $content = preg_replace('/^(#{1,6}\s+.+)$/m', "\n$1\n", $content);
        
        // Ensure bullet points are properly formatted
        $content = preg_replace('/^[\s]*[-•]\s*(.+)$/m', '- $1', $content);
        
        // Ensure numbered lists are properly formatted
        $content = preg_replace('/^[\s]*(\d+)[\.\)]\s*(.+)$/m', '$1. $2', $content);
        
        // Add emphasis to key medical terms and important information
        $content = self::emphasizeKeyTerms($content);
        
        // Improve paragraph structure
        $content = self::improveParagraphs($content);
        
        return $content;
    }

    /**
     * Emphasize key medical and health terms
     */
    private static function emphasizeKeyTerms(string $content): string
    {
        // Key terms that should be emphasized (if not already bold)
        $keyTerms = [
            // Medical urgency
            'emergency', 'urgent', 'immediate', 'seek medical attention', 'call 911', 'go to ER',
            'serious', 'severe', 'critical', 'life-threatening',
            
            // Important medical concepts
            'diagnosis', 'treatment', 'medication', 'dosage', 'side effects', 'contraindications',
            'symptoms', 'warning signs', 'red flags', 'complications',
            
            // Health recommendations
            'recommended', 'important', 'essential', 'crucial', 'vital', 'necessary',
            'avoid', 'stop', 'discontinue', 'do not',
            
            // Wellness terms
            'healthy diet', 'exercise', 'sleep', 'stress management', 'hydration',
            'prevention', 'lifestyle changes', 'follow-up'
        ];
        
        foreach ($keyTerms as $term) {
            // Only emphasize if not already bold or in a header
            $pattern = '/(?<![\*#])(\b' . preg_quote($term, '/') . '\b)(?![\*])/i';
            $content = preg_replace($pattern, '**$1**', $content);
        }
        
        return $content;
    }

    /**
     * Improve paragraph structure and spacing
     */
    private static function improveParagraphs(string $content): string
    {
        // Clean up excessive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        
        // Ensure proper spacing after headers
        $content = preg_replace('/(^#{1,6}\s+.+)\n([^\n#])/m', "$1\n\n$2", $content);
        
        // Ensure proper spacing before headers (except at start)
        $content = preg_replace('/([^\n])\n(#{1,6}\s+.+)/m', "$1\n\n$2", $content);
        
        // Add spacing around lists
        $content = preg_replace('/([^\n])\n([-\d][\.\-]\s+.+)/m', "$1\n\n$2", $content);
        $content = preg_replace('/([-\d][\.\-]\s+.+)\n([^\n\-\d])/m', "$1\n\n$2", $content);
        
        return trim($content);
    }

    /**
     * Improve overall readability
     */
    private static function improveReadability(string $content): string
    {
        // Add section breaks for better organization
        $content = self::addSectionBreaks($content);
        
        // Ensure consistent formatting for common medical patterns
        $content = self::formatMedicalPatterns($content);
        
        // Clean up and finalize
        $content = self::finalCleanup($content);
        
        return $content;
    }

    /**
     * Add section breaks for better organization
     */
    private static function addSectionBreaks(string $content): string
    {
        // Add breaks before major sections
        $sectionHeaders = [
            'symptoms', 'treatment', 'diagnosis', 'recommendations', 'prevention',
            'when to seek help', 'follow-up', 'lifestyle changes', 'diet', 'exercise',
            'medication', 'side effects', 'complications', 'prognosis'
        ];
        
        foreach ($sectionHeaders as $header) {
            $pattern = '/(?<!^|\n\n)(\*\*' . preg_quote($header, '/') . '.*?\*\*)/i';
            $content = preg_replace($pattern, "\n\n$1", $content);
        }
        
        return $content;
    }

    /**
     * Format common medical patterns
     */
    private static function formatMedicalPatterns(string $content): string
    {
        // Format dosage information with proper spacing
        $content = preg_replace('/(\d+)\s*(mg|g|ml|mcg|units?)\b/i', '**$1 $2**', $content);

        // Format time periods with proper spacing
        $content = preg_replace('/(\d+)\s*(days?|weeks?|months?|hours?|minutes?)\b/i', '**$1 $2**', $content);

        // Format exercise time periods with proper spacing
        $content = preg_replace('/(\d+)\s*(minutes?)\s*(of|per|for)\b/i', '**$1 $2** $3', $content);

        // Format percentages
        $content = preg_replace('/(\d+(?:\.\d+)?)\s*%/', '**$1%**', $content);

        // Format temperature
        $content = preg_replace('/(\d+(?:\.\d+)?)\s*°?([CF])\b/', '**$1°$2**', $content);

        // Format cups and measurements
        $content = preg_replace('/(\d+)\s*(cups?|tbsp|tsp|servings?)\b/i', '**$1 $2**', $content);

        // Format measurements with parenthetical details (e.g., "8 cups (64 oz)")
        $content = preg_replace('/(\*\*\d+\s+\w+\*\*)\s*\((\d+\s*\w+)\)/', '$1 (**$2**)', $content);

        return $content;
    }

    /**
     * Final cleanup and optimization with robust spacing fixes
     */
    private static function finalCleanup(string $content): string
    {
        // Remove excessive bold formatting (avoid **text** **more** becoming ****text** **more****)
        $content = preg_replace('/\*\*\s*\*\*/', ' ', $content);

        // Fix spacing issues around bold text - ROBUST APPROACH
        // Ensure there's space before bold text when it follows a word
        $content = preg_replace('/(\w)\*\*([^*\n]+)\*\*/', '$1 **$2**', $content);
        // Ensure there's space after bold text when it precedes a word
        $content = preg_replace('/\*\*([^*\n]+)\*\*(\w)/', '**$1** $2', $content);

        // Clean up multiple spaces but preserve intentional spacing
        $content = preg_replace('/[ \t]+/', ' ', $content);

        // Ensure proper line ending
        $content = rtrim($content) . "\n";

        // Final spacing cleanup
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        return trim($content);
    }

    /**
     * Format response specifically for medical consultations
     */
    public static function formatMedicalResponse(string $content): string
    {
        $formatted = self::formatAIResponse($content);
        
        // Add medical-specific formatting
        $formatted = self::addMedicalStructure($formatted);
        
        return $formatted;
    }

    /**
     * Add medical-specific structure
     */
    private static function addMedicalStructure(string $content): string
    {
        // Ensure important medical disclaimers are emphasized
        $disclaimers = [
            'not a substitute for professional medical advice',
            'consult with a healthcare provider',
            'seek immediate medical attention',
            'this is not medical advice',
            'emergency medical services'
        ];
        
        foreach ($disclaimers as $disclaimer) {
            $pattern = '/(' . preg_quote($disclaimer, '/') . ')/i';
            $content = preg_replace($pattern, '**$1**', $content);
        }
        
        return $content;
    }

    /**
     * Format response specifically for wellness content
     */
    public static function formatWellnessResponse(string $content): string
    {
        $formatted = self::formatAIResponse($content);
        
        // Add wellness-specific formatting
        $formatted = self::addWellnessStructure($formatted);
        
        return $formatted;
    }

    /**
     * Add wellness-specific structure
     */
    private static function addWellnessStructure(string $content): string
    {
        // Emphasize wellness key points
        $wellnessTerms = [
            'balanced diet', 'regular exercise', 'adequate sleep', 'stress management',
            'hydration', 'mindfulness', 'self-care', 'mental health', 'work-life balance'
        ];
        
        foreach ($wellnessTerms as $term) {
            $pattern = '/(?<!\*\*)(\b' . preg_quote($term, '/') . '\b)(?!\*\*)/i';
            $content = preg_replace($pattern, '**$1**', $content);
        }
        
        return $content;
    }
}

<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Models\ImageGenerationLog;
use App\Services\BotUserCreationService;
use App\Jobs\GenerateBotPostJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ManageBotSystem extends Command
{
    protected $signature = 'bot:manage 
                            {action : Action to perform (create-users|generate-posts|check-status|cleanup)}
                            {--persona= : Specific persona ID to target}
                            {--force : Force action even if conditions not met}';
    
    protected $description = 'Manage the automated bot posting system';

    private BotUserCreationService $botUserService;

    public function __construct(BotUserCreationService $botUserService)
    {
        parent::__construct();
        $this->botUserService = $botUserService;
    }

    public function handle()
    {
        $action = $this->argument('action');
        $personaId = $this->option('persona');
        $force = $this->option('force');

        $this->info("Starting bot management action: {$action}");

        switch ($action) {
            case 'create-users':
                return $this->createUsers($personaId);
            
            case 'generate-posts':
                return $this->generatePosts($personaId, $force);
            
            case 'check-status':
                return $this->checkStatus();
            
            case 'cleanup':
                return $this->cleanup($force);
            
            default:
                $this->error("Unknown action: {$action}");
                $this->info("Available actions: create-users, generate-posts, check-status, cleanup");
                return 1;
        }
    }

    private function createUsers(?string $personaId): int
    {
        $this->info('Creating bot users...');

        if ($personaId) {
            $persona = BotPersona::find($personaId);
            if (!$persona) {
                $this->error("Persona not found: {$personaId}");
                return 1;
            }

            if ($persona->botUser) {
                $this->warn("Bot user already exists for persona: {$persona->full_name}");
                return 0;
            }

            $botUser = $this->botUserService->createBotUser($persona);
            if ($botUser) {
                $this->info("Created bot user for: {$persona->full_name}");
                return 0;
            } else {
                $this->error("Failed to create bot user for: {$persona->full_name}");
                return 1;
            }
        } else {
            $results = $this->botUserService->createAllBotUsers();
            $successCount = count(array_filter($results, fn($r) => $r['success']));
            $totalCount = count($results);

            $this->info("Created {$successCount} out of {$totalCount} bot users");
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $this->info("✓ {$result['persona_name']}");
                } else {
                    $this->error("✗ {$result['persona_name']}");
                }
            }

            return $successCount === $totalCount ? 0 : 1;
        }
    }

    private function generatePosts(?string $personaId, bool $force): int
    {
        $this->info('Generating bot posts...');

        if ($personaId) {
            $persona = BotPersona::with('botUser')->find($personaId);
            if (!$persona) {
                $this->error("Persona not found: {$personaId}");
                return 1;
            }

            if (!$persona->botUser) {
                $this->error("No bot user found for persona: {$persona->full_name}");
                return 1;
            }

            if (!$force && !$persona->botUser->canPost()) {
                $this->warn("Persona cannot post due to rate limiting: {$persona->full_name}");
                return 0;
            }

            GenerateBotPostJob::dispatch($persona);
            $this->info("Dispatched post generation for: {$persona->full_name}");
            return 0;
        } else {
            $personas = BotPersona::active()
                ->with('botUser')
                ->whereHas('botUser')
                ->get();

            $dispatched = 0;
            $skipped = 0;

            foreach ($personas as $persona) {
                $canPost = $force || ($persona->botUser->canPost() && $persona->shouldPostToday());
                
                if ($canPost) {
                    GenerateBotPostJob::dispatch($persona);
                    $this->info("✓ Dispatched: {$persona->full_name}");
                    $dispatched++;
                } else {
                    $this->warn("✗ Skipped: {$persona->full_name} (rate limited or not scheduled)");
                    $skipped++;
                }
            }

            $this->info("Dispatched: {$dispatched}, Skipped: {$skipped}");
            return 0;
        }
    }

    private function checkStatus(): int
    {
        $this->info('Bot System Status');
        $this->line('==================');

        // Personas status
        $totalPersonas = BotPersona::count();
        $activePersonas = BotPersona::active()->count();
        $personasWithUsers = BotPersona::whereHas('botUser')->count();

        $this->info("Personas: {$totalPersonas} total, {$activePersonas} active, {$personasWithUsers} with bot users");

        // Posts status
        $totalPosts = AutomatedPost::count();
        $draftPosts = AutomatedPost::where('status', 'draft')->count();
        $generatingPosts = AutomatedPost::where('status', 'generating_image')->count();
        $readyPosts = AutomatedPost::where('status', 'ready')->count();
        $postedPosts = AutomatedPost::where('status', 'posted')->count();
        $failedPosts = AutomatedPost::where('status', 'failed')->count();

        $this->info("Posts: {$totalPosts} total");
        $this->line("  - Draft: {$draftPosts}");
        $this->line("  - Generating Image: {$generatingPosts}");
        $this->line("  - Ready: {$readyPosts}");
        $this->line("  - Posted: {$postedPosts}");
        $this->line("  - Failed: {$failedPosts}");

        // Image generation status
        $totalImages = ImageGenerationLog::count();
        $pendingImages = ImageGenerationLog::where('status', 'pending')->count();
        $processingImages = ImageGenerationLog::where('status', 'processing')->count();
        $completedImages = ImageGenerationLog::where('status', 'completed')->count();
        $failedImages = ImageGenerationLog::where('status', 'failed')->count();

        $this->info("Image Generation: {$totalImages} total");
        $this->line("  - Pending: {$pendingImages}");
        $this->line("  - Processing: {$processingImages}");
        $this->line("  - Completed: {$completedImages}");
        $this->line("  - Failed: {$failedImages}");

        // Recent activity
        $postsToday = AutomatedPost::whereDate('created_at', today())->count();
        $publishedToday = AutomatedPost::whereDate('posted_at', today())->count();

        $this->info("Today's Activity:");
        $this->line("  - Posts Created: {$postsToday}");
        $this->line("  - Posts Published: {$publishedToday}");

        // Show failed posts if any
        if ($failedPosts > 0) {
            $this->warn("\nRecent Failed Posts:");
            $recentFailed = AutomatedPost::where('status', 'failed')
                ->with('botPersona:id,first_name,last_name')
                ->orderBy('updated_at', 'desc')
                ->limit(5)
                ->get();

            foreach ($recentFailed as $post) {
                $this->line("  - {$post->botPersona->full_name}: {$post->error_message}");
            }
        }

        return 0;
    }

    private function cleanup(bool $force): int
    {
        $this->info('Cleaning up bot system...');

        if (!$force && !$this->confirm('This will delete failed posts and logs. Continue?')) {
            $this->info('Cleanup cancelled.');
            return 0;
        }

        // Clean up old failed posts (older than 7 days)
        $oldFailedPosts = AutomatedPost::where('status', 'failed')
            ->where('updated_at', '<', now()->subDays(7))
            ->count();

        if ($oldFailedPosts > 0) {
            AutomatedPost::where('status', 'failed')
                ->where('updated_at', '<', now()->subDays(7))
                ->delete();
            $this->info("Deleted {$oldFailedPosts} old failed posts");
        }

        // Clean up old failed image generation logs
        $oldFailedImages = ImageGenerationLog::where('status', 'failed')
            ->where('updated_at', '<', now()->subDays(7))
            ->count();

        if ($oldFailedImages > 0) {
            ImageGenerationLog::where('status', 'failed')
                ->where('updated_at', '<', now()->subDays(7))
                ->delete();
            $this->info("Deleted {$oldFailedImages} old failed image logs");
        }

        // Clean up orphaned image generation logs
        $orphanedLogs = ImageGenerationLog::whereDoesntHave('automatedPost')->count();
        if ($orphanedLogs > 0) {
            ImageGenerationLog::whereDoesntHave('automatedPost')->delete();
            $this->info("Deleted {$orphanedLogs} orphaned image logs");
        }

        $this->info('Cleanup completed.');
        return 0;
    }
}

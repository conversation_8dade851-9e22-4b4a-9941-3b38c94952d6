<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserClub;
use App\Models\FounderReferralCode;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ClubService
{
    protected $badgeService;
    protected $pointsService;

    public function __construct(BadgeService $badgeService, PointsService $pointsService)
    {
        $this->badgeService = $badgeService;
        $this->pointsService = $pointsService;
    }

    /**
     * Check and assign founder membership based on referral code
     */
    public function checkFounderMembership(User $user, string $referralCode): bool
    {
        try {
            $founderCode = FounderReferralCode::where('code', $referralCode)
                ->active()
                ->first();

            if (!$founderCode || !$founderCode->isValid()) {
                return false;
            }

            DB::beginTransaction();

            // Create founder club membership
            $membership = UserClub::create([
                'user_id' => $user->id,
                'club_type' => 'founder',
                'membership_level' => 'platinum',
                'is_verified' => true,
                'verification_method' => 'founder_code',
                'founder_code_used' => $referralCode,
                'joined_at' => now(),
            ]);

            // Update user founder status
            $user->update([
                'is_founder_member' => true,
                'club_memberships' => ['founder'],
            ]);

            // Increment founder code usage
            $founderCode->incrementUsage();

            // Award welcome bonus credits for founder members
            $creditService = app(\App\Services\CreditService::class);
            $creditService->addCredit(
                $user->id,
                10.00,
                'founder_code_bonus',
                null,
                "Welcome bonus for joining Medroid's Founders Club with code: {$referralCode}"
            );

            // Award founder badge and points (optional - can be done later)
            try {
                $this->badgeService->awardBadge($user, 'founder', [
                    'founder_code' => $referralCode,
                    'membership_id' => $membership->id,
                ]);

                $this->pointsService->awardPoints($user, 'founder_signup', 100, [
                    'source' => 'founder_membership',
                    'founder_code' => $referralCode,
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to award badge/points for founder membership', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
            }

            DB::commit();

            Log::info('Founder membership assigned', [
                'user_id' => $user->id,
                'founder_code' => $referralCode,
                'membership_id' => $membership->id,
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error assigning founder membership: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'referral_code' => $referralCode,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get user's club memberships with details
     */
    public function getUserClubMemberships(User $user): array
    {
        $memberships = $user->clubMemberships()
            ->with('founderCode')
            ->get()
            ->map(function ($membership) {
                return [
                    'id' => $membership->id,
                    'club_type' => $membership->club_type,
                    'display_name' => $membership->display_name,
                    'membership_level' => $membership->membership_level,
                    'level_display' => $membership->level_display,
                    'is_verified' => $membership->is_verified,
                    'verification_method' => $membership->verification_method,
                    'joined_at' => $membership->joined_at,
                    'founder_code_used' => $membership->founder_code_used,
                ];
            });

        return $memberships->toArray();
    }

    /**
     * Create a new founder referral code
     */
    public function createFounderCode(array $data): FounderReferralCode
    {
        return FounderReferralCode::create([
            'code' => strtoupper($data['code']),
            'description' => $data['description'] ?? null,
            'is_active' => $data['is_active'] ?? true,
            'max_uses' => $data['max_uses'] ?? null,
            'expires_at' => $data['expires_at'] ?? null,
            'metadata' => $data['metadata'] ?? [],
        ]);
    }

    /**
     * Get founder code statistics
     */
    public function getFounderCodeStats(FounderReferralCode $code): array
    {
        $users = User::where('referral_code', $code->code)
            ->where('is_founder_member', true)
            ->get();

        return [
            'code' => $code->code,
            'total_uses' => $code->current_uses,
            'max_uses' => $code->max_uses,
            'is_active' => $code->is_active,
            'expires_at' => $code->expires_at,
            'users_count' => $users->count(),
            'users' => $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'joined_at' => $user->created_at,
                    'total_points' => $user->getTotalPoints(),
                ];
            }),
        ];
    }

    /**
     * Create club membership from waitlist invitation
     */
    public function createMembershipFromInvitation(\App\Models\WaitlistInvitation $invitation, User $user): UserClub
    {
        $membership = UserClub::create([
            'user_id' => $user->id,
            'club_type' => $invitation->club_type,
            'membership_level' => $invitation->membership_level,
            'is_verified' => true,
            'verification_method' => 'invitation',
            'founder_code_used' => $invitation->club_type === 'founder' ? $invitation->token : null,
            'joined_at' => now(),
        ]);

        // Award welcome bonus credits for founder members
        if ($invitation->club_type === 'founder') {
            $creditService = app(\App\Services\CreditService::class);
            $creditService->addCredit(
                $user->id,
                10.00,
                'club_membership_bonus',
                null,
                "Welcome bonus for joining {$invitation->getClubDisplayInfo()['club_name']}"
            );
        }

        Log::info('Club membership created from invitation', [
            'user_id' => $user->id,
            'invitation_id' => $invitation->id,
            'club_type' => $invitation->club_type,
            'membership_level' => $invitation->membership_level,
        ]);

        return $membership;
    }

    /**
     * Update club membership level based on activity
     */
    public function updateMembershipLevel(User $user): void
    {
        $totalPoints = $user->getTotalPoints();
        $activityStreak = $user->activity_streak;

        $founderMembership = $user->founderMembership();
        if (!$founderMembership) {
            return;
        }

        $newLevel = 'basic';
        if ($totalPoints >= 1000 && $activityStreak >= 30) {
            $newLevel = 'platinum';
        } elseif ($totalPoints >= 500 && $activityStreak >= 14) {
            $newLevel = 'gold';
        } elseif ($totalPoints >= 200 && $activityStreak >= 7) {
            $newLevel = 'silver';
        }

        if ($founderMembership->membership_level !== $newLevel) {
            $founderMembership->update(['membership_level' => $newLevel]);
            
            // Award level upgrade badge
            $this->badgeService->awardBadge($user, 'level_upgrade', [
                'old_level' => $founderMembership->membership_level,
                'new_level' => $newLevel,
            ]);
        }
    }

    /**
     * Create founder membership for shareable link signups
     */
    public function createFounderMembership(User $user, string $specialCode = 'MEDROID_FOUNDERS'): UserClub
    {
        // Check if user already has founder membership
        $existingMembership = $user->founderMembership;
        if ($existingMembership) {
            return $existingMembership;
        }

        $membership = UserClub::create([
            'user_id' => $user->id,
            'club_type' => 'founder',
            'membership_level' => 'platinum',
            'is_verified' => true,
            'verification_method' => 'shareable_link',
            'founder_code_used' => $specialCode,
            'joined_at' => now(),
        ]);

        // Update user founder status
        $user->update([
            'is_founder_member' => true,
            'club_memberships' => ['founder'],
        ]);

        // Award welcome bonus credits for founder members
        $creditService = app(\App\Services\CreditService::class);
        $creditService->addCredit(
            $user->id,
            10.00,
            'founder_welcome_bonus',
            null,
            "Welcome bonus for joining Medroid's Founders Club"
        );

        Log::info('Founder membership created via shareable link', [
            'user_id' => $user->id,
            'special_code' => $specialCode,
            'membership_level' => 'platinum',
        ]);

        return $membership;
    }
}

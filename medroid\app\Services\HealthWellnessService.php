<?php

namespace App\Services;

use App\Models\Patient;
use App\Models\ChatConversation;
use App\Services\ChatServiceManager;
use App\Services\GoogleSearchService;
use App\Services\MessageFormattingService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class HealthWellnessService
{
    private $serviceName;
    private $apiKey;
    private $apiUrl;
    private $model;
    private $provider;
    private $chatServiceManager;
    private $googleSearchService;

    public function __construct($provider = null)
    {
        // Use ChatServiceManager for provider fallback
        $this->chatServiceManager = new ChatServiceManager();
        $this->googleSearchService = new GoogleSearchService();
        // Maya will use ChatServiceManager instead of custom fallback
        $this->provider = $provider ?: config('services.chat.primary_provider', 'nscale');
        $this->initializeService($this->provider);
    }

    /**
     * Initialize service configuration based on provider
     */
    private function initializeService($provider)
    {
        switch ($provider) {
            case 'nscale':
                $this->serviceName = 'NScale';
                $this->apiKey = config('services.nscale.api_key');
                $this->apiUrl = config('services.nscale.api_url');
                $this->model = config('services.nscale.model');
                break;
            
            case 'groq':
            default:
                $this->serviceName = 'Groq';
                $this->apiKey = config('services.groq.api_key');
                $this->apiUrl = config('services.groq.api_url');
                $this->model = config('services.groq.model');
                break;
        }
    }

    /**
     * Get Maya system prompt
     */
    public static function getSystemPrompt(): string
    {
        return <<<EOT
<identity>
You are Maya, Medroid's Health & Wellness Expert. You are a knowledgeable, curious, and engaging wellness companion who talks like a smart friend who always knows the latest health and wellness trends. You help users explore health topics with evidence-based information while maintaining a warm, friendly, and enthusiastic personality. You were developed by Medroid AI, Inc. Medroid is a Social Health & Wellness Platform. Medroid also offers a Health & Wellness Feed, AI Doctor and Health & Wellness Marketplace.
</identity>

<core_mission>
You are NOT a medical doctor and do not provide medical advice, diagnosis, or treatment recommendations. You are a wellness research companion focused on lifestyle, nutrition, fitness, mental wellness, and preventive health. 

When users have symptoms or need medical advice, redirect them enthusiastically to our AI Doctor. For example: \"Oh, that sounds like something our AI Doctor would be perfect for! They can give you proper medical guidance on this - super helpful for clinical stuff like that!\"
</core_mission>

<tool_availability>
You have access to a grounding search tool that searches trusted health and wellness sources including Mayo Clinic, Harvard Health, Johns Hopkins, NHS, Cleveland Clinic, and other authoritative medical institutions from the UK, USA, and India. Use this tool strategically to provide accurate, up-to-date information.
</tool_availability>

<search_strategy>
- Use specific, targeted search queries: \"intermittent fasting 2024 research\" not \"intermittent fasting\"
- Include regional context when relevant: \"diabetes prevention guidelines UK USA India\"
- Search for verification when making any factual claims about health benefits
- Cross-reference multiple sources when possible
- Always search rather than guess when you\'re uncertain
</search_strategy>

<no_search_needed>
- General wellness concepts you\'re confident about
- Basic nutrition principles
- Common fitness guidelines
- General mental wellness strategies
- When redirecting to AI Doctor
</no_search_needed>
</when_to_use_grounding_search>

<factual_accuracy_protocol>
<before_making_claims>
If you\'re about to state a specific health benefit, statistic, or research finding, pause and consider: \"Do I need to verify this with current sources?\" If there\'s any uncertainty, use the grounding search tool first.
</before_making_claims>

<source_attribution>
Always attribute information to sources naturally. For example:
- \"According to the latest research from Harvard Health...\"
- \"The NHS just updated their guidance on this...\"
- \"A recent study from the University of Cambridge shows...\"
- \"The Mayo Clinic and Cleveland Clinic both suggest...\"
- Never present searched information as your own knowledge
</source_attribution>

<regional_awareness>
Be mindful of your global audience:
- Acknowledge regional differences when relevant: \"In the UK, the NHS recommends... while in the US, the CDC suggests...\"
- Include diverse perspectives. For example: \"This is popular in Indian wellness traditions and now Western research is catching up...\"
- Use inclusive examples that resonate across cultures
</regional_awareness>

<uncertainty_handling>
If search results are unclear or conflicting, be transparent. For example:
- \"The research on this is still evolving, but here\'s what we know so far...\"
- \"There are different approaches to this - let me share what the major health institutions say...\"
- \"This varies by region, so let me give you perspectives from different health authorities...\"
</uncertainty_handling>
</factual_accuracy_protocol>

<knowledge_scope>
<focus_areas>
- Medical conditions and diseases (educational information)
- Prescription medications (general information, how they work, common effects)
- Drug interactions and contraindications (educational)
- Dosage information (general guidelines, not personal prescribing)
- Nutrition and dietary trends (including regional cuisines and approaches)
- Fitness and exercise guidance
- Mental wellness and stress management
- Sleep optimization
- Preventive health measures
- Supplement research and trends
- Lifestyle modifications
- Mindfulness and motivation
- Wellness product insights
- Health trend analysis
- Traditional wellness practices (Ayurveda, meditation, etc.) backed by modern research
- Medical research and studies explanation
- Healthcare system navigation (general guidance)
</focus_areas>

<redirect_to_ai_doctor>
- Specific symptoms or health concerns requiring assessment
- Personal medical advice (\"Should I take this medication?\")
- Diagnostic questions (\"What could be causing my symptoms?\")
- Treatment recommendations for individual situations
- Care plan development
- Medication effectiveness concerns for their specific case
- \"My medication isn\'t working\" or similar personal medical issues
- Medical decision-making support
- Health assessment or evaluation needs
- \"I have chest pain, what could it be?\"
- \"Should I start taking metformin?\"
- \"My blood pressure medication isn\'t working, what should I do?\"
- \"I\'m experiencing side effects, what should I do?\"
- \"Do I need to see a doctor for these symptoms?\"
- \"What\'s the best treatment for my condition?\"
- \"Help me decide between treatment options\"
</redirect_to_ai_doctor>

<information_vs_consultation_boundary>
<maya_can_provide>
- \"What is diabetes and how does it work?\"
- \"How does metformin work in the body?\"
- \"What are common side effects of statins?\"
- \"What foods interact with warfarin?\"
- \"What\'s the difference between Type 1 and Type 2 diabetes?\"
- \"What does recent research say about intermittent fasting?\"
- \"How do blood pressure medications generally work?\"
</maya_can_provide>

When redirecting, say enthusiastically. For example: \"That sounds like something our AI Doctor would be perfect for! They can do a proper consultation about your specific situation and give you personalized medical guidance. I\'m great for general health information, but for personal medical questions like this, our AI Doctor is your go-to!\"
</information_vs_consultation_boundary>
</knowledge_scope>

<personality_traits>
<trait name=\"curious\">Ask thoughtful follow-up questions that deepen engagement: \"That\'s really interesting! Have you noticed any patterns with your energy levels when you...\"</trait>
<trait name=\"research_enthusiast\">Show excitement about findings: \"Oh, this is fascinating - I just found some really cool research on this from both Western and traditional medicine perspectives!\"</trait>
<trait name=\"practical\">Always provide actionable steps: \"Here\'s something concrete you could try starting this week...\"</trait>
<trait name=\"conversational\">Speak like a knowledgeable friend: \"You know what\'s really exciting about this trend...\"</trait>
<trait name=\"culturally_aware\">Acknowledge diverse wellness traditions: \"This actually ties into traditional practices that are now being validated by modern research...\"</trait>
<trait name=\"evidence_based\">Ground all claims in research while keeping it accessible and engaging</trait>
</personality_traits>

<response_structure>
<length>KEEP RESPONSES BRIEF AND CONCISE - Maximum 1-2 short paragraphs. Be direct and to the point while maintaining warmth. Users prefer quick, actionable insights over lengthy explanations.</length>

<engagement_hooks>
Start responses with brief, engaging hooks:
- \"Great question! Here's what works...\"
- \"I love this topic! Quick answer...\"
- \"This is trending right now...\"
- \"Based on recent research...\"
- \"Here's what I'm seeing...\"
</engagement_hooks>

<response_flow>
1. Acknowledge their question with enthusiasm
2. If factual claims needed, search for current information
3. Present information in an engaging, accessible way with proper attribution
4. Include regional perspectives when relevant
5. Provide actionable next steps
6. Ask a follow-up question to maintain engagement
7. ONLY when relevant and appropriate, mention relevant Medroid marketplace options
</response_flow>
</response_structure>

<grounding_integration>
<search_presentation>
When you find information through search, present it naturally. For example:
- \"I just came across some really interesting insights on this...\"
- \"The latest research I\'m seeing suggests...\"
- \"According to what I\'m finding from trusted sources like the Mayo Clinic and NHS...\"

Never mention \"searching\" or \"using tools\" - make it feel like you naturally have access to the latest information.
</search_presentation>

<confidence_levels>
- High confidence + recent search = Present as current fact with attribution
- Medium confidence = Search to verify before stating
- Low confidence = Always search, present with appropriate caveats
- Uncertain = Search multiple terms, synthesize findings from multiple regions/sources
</confidence_levels>

<grounding_honesty>
Only claim to have \"found research\" or \"read studies\" if you have ACTUALLY performed a grounding search. If you haven\'t searched, use phrases like:
- \"From what I understand about wellness research...\"
- \"Generally speaking, studies tend to show...\"
- \"The wellness community often discusses...\"

Never fabricate specific studies or institutions.
</grounding_honesty>
</grounding_integration>

<communication_style>
<tone>Warm, enthusiastic, conversational - like chatting with a globally-minded friend who happens to be really into health research</tone>
<language>Use natural, modern language: \"This is so interesting because...\" rather than clinical terminology</language>
<inclusivity>Use examples and references that resonate across UK, USA, and Indian cultures</inclusivity>
<format>Use **bold text** for key points and recommendations. Structure responses with clear headers (## Main Topic) when covering multiple aspects. Use bullet points (-) for lists and numbered lists (1., 2., 3.) for step-by-step guidance. Emphasize important numbers, dosages, and timeframes in **bold**. Break up long paragraphs for better readability.</format>
<engagement>Include one thoughtful question or suggestion per response to maintain conversation flow</engagement>
</communication_style>

<safety_protocols>
<fact_checking>Always verify specific health claims through grounding search rather than relying solely on training data</fact_checking>
<medical_boundaries>Redirect medical questions to AI Doctor immediately and enthusiastically</medical_boundaries>
<source_quality>Only reference information from authoritative health institutions</source_quality>
<cultural_sensitivity>Be respectful of different cultural approaches to wellness while prioritizing evidence-based information</cultural_sensitivity>
<uncertainty_transparency>Be open about limitations: \"This is an area where research is still developing...\"</uncertainty_transparency>
</safety_protocols>

<marketplace_integration>
When genuinely relevant, mention Medroid products conversationally. For example:
\"Speaking of probiotics, we actually have some really well-researched options in our marketplace - all third-party tested and from trusted brands!\"

Keep it helpful and natural, never pushy. Focus on how products can support their wellness journey. You don't have to mention it in every response because it will look too pushy.
</marketplace_integration>

<success_metrics>
Your goal is to be so helpful, knowledgeable, and current that users across the UK, USA, and India think \"I should ask Maya about this!\" whenever health and wellness topics come up. Every response should feel friendly, valuable, accurate, culturally aware, and engaging enough to bring them back for more conversations.
</success_metrics>

<current_context>
Today\'s date: ' . date('Y-m-d') . '
Platform: Medroid Health & Wellness Social Platform
Primary markets: United Kingdom, United States, India
User base: Health-conscious millennials and Gen Z across these regions
Available tools: Grounded search of trusted global health sources
</current_context>
EOT;
    }

    /**
     * Process user query with Maya
     */
    public function chat($userMessage, $conversationHistory = [], $patientId = null, $conversationId = null, $searchEnabled = true)
    {
        try {
            // Get patient context if available
            $patientContext = $this->getPatientContext($patientId);

            // Get conversation memory from previous chats
            $conversationMemory = $this->getConversationMemory($patientId, $conversationId);

            // Check if we need Google Search (ONLY if search toggle is enabled AND message needs search)
            $searchResults = null;
            if ($searchEnabled && $this->shouldUseGrounding($userMessage)) {
                Log::info("Maya: Google Search enabled and triggered", ['message' => $userMessage]);
                $searchResults = $this->googleSearchService->search($userMessage);

                if ($searchResults && !empty($searchResults['results'])) {
                    Log::info("Maya: Search results found", ['count' => count($searchResults['results'])]);
                } else {
                    Log::warning("Maya: No search results returned", ['search_results' => $searchResults]);
                    // Set to null if no real results - don't use mock data
                    $searchResults = null;
                }
            } else {
                if (!$searchEnabled) {
                    Log::info("Maya: Google Search disabled by user toggle");
                } else {
                    Log::info("Maya: Message doesn't require search", ['message' => $userMessage]);
                }
            }

            // Build messages array with enhanced system prompt
            $systemPrompt = $this->getEnhancedSystemPrompt($patientContext, $conversationMemory);
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt]
            ];

            // Add conversation history
            foreach ($conversationHistory as $message) {
                $messages[] = $message;
            }

            // Enhance user message with search context if available
            $enhancedMessage = $userMessage;
            if ($searchResults && !empty($searchResults['results'])) {
                $searchContext = $this->googleSearchService->formatSearchContext($searchResults);
                $enhancedMessage = $userMessage . "\n\n[SEARCH_CONTEXT]\n" . $searchContext . "\n[/SEARCH_CONTEXT]\n\nIMPORTANT: Keep your response BRIEF (max 250 tokens). Use this context to provide accurate, up-to-date information while maintaining your friendly, conversational tone. Reference sources using [1], [2], etc. format.";
            } else {
                $enhancedMessage = $userMessage . "\n\nIMPORTANT: Keep your response BRIEF (max 250 tokens) while maintaining your friendly, conversational tone.";
            }

            $messages[] = ['role' => 'user', 'content' => $enhancedMessage];

            // Prepare API payload
            $payload = [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => 0.4, // Balanced for personality and accuracy
                'max_tokens' => 250, // Reduced for brief responses
                'top_p' => 0.9,
            ];

            // Use ChatServiceManager for reliable fallback
            $response = $this->makeApiRequestWithChatServiceManager($payload, $messages);

            if ($response['success']) {
                $responseText = $response['content'];

                // Apply universal formatting for better readability
                $responseText = MessageFormattingService::formatWellnessResponse($responseText);

                // Add Perplexity-style citations if search was used
                if ($searchResults && !empty($searchResults['citations'])) {
                    Log::info("Maya: Adding citations to response", [
                        'citations_count' => count($searchResults['citations']),
                        'citations' => $searchResults['citations']
                    ]);
                    $citations = $this->googleSearchService->generateCitations($searchResults);
                    $responseText .= $citations;
                } else {
                    Log::info("Maya: No citations to add", [
                        'has_search_results' => !is_null($searchResults),
                        'citations_empty' => empty($searchResults['citations'] ?? [])
                    ]);
                }

                return [
                    'success' => true,
                    'response' => $responseText,
                    'search_used' => !is_null($searchResults),
                    'citations_count' => $searchResults ? count($searchResults['citations'] ?? []) : 0,
                    'search_results' => $searchResults, // Include full search results for frontend
                    'provider' => $response['provider'],
                    'used_fallback' => $response['used_fallback'] ?? false
                ];
            } else {
                return [
                    'success' => false,
                    'response' => 'I\'m having trouble accessing my knowledge base right now. Could you try asking again in a moment?',
                    'error' => $response['error'] ?? 'API Error'
                ];
            }

        } catch (\Exception $e) {
            Log::error("Maya {$this->serviceName} Exception", ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'response' => 'Something went wrong while processing your request. Please try again!',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Make API request based on provider
     */
    private function makeApiRequest($payload, $timeout = 60)
    {
        return Http::withHeaders([
            'Authorization' => "Bearer {$this->apiKey}",
            'Content-Type' => 'application/json',
        ])->timeout($timeout)
        ->post($this->apiUrl, $payload);
    }

    /**
     * Make API request using ChatServiceManager for reliable fallback
     */
    private function makeApiRequestWithChatServiceManager($payload, $messages)
    {
        try {
            // Extract system and user prompts from messages
            $systemPrompt = '';
            $userPrompt = '';

            foreach ($payload['messages'] as $message) {
                if ($message['role'] === 'system') {
                    $systemPrompt = $message['content'];
                } else if ($message['role'] === 'user') {
                    $userPrompt = $message['content'];
                }
            }

            Log::info("Maya: Using ChatServiceManager", [
                'system_prompt_length' => strlen($systemPrompt),
                'user_prompt_length' => strlen($userPrompt)
            ]);

            // Use ChatServiceManager's reliable fallback mechanism
            $result = $this->chatServiceManager->generateContent(
                $systemPrompt,
                $userPrompt,
                [
                    'temperature' => $payload['temperature'],
                    'max_completion_tokens' => $payload['max_tokens'],
                    'top_p' => $payload['top_p']
                ]
            );

            Log::info("Maya: ChatServiceManager response", [
                'result_type' => gettype($result),
                'is_array' => is_array($result),
                'has_message' => is_array($result) && isset($result['message'])
            ]);

            if (is_array($result) && isset($result['message'])) {
                return [
                    'success' => true,
                    'content' => $result['message'],
                    'provider' => $result['used_service'] ?? 'ChatServiceManager',
                    'used_fallback' => $result['used_fallback'] ?? false
                ];
            } else if (is_string($result)) {
                return [
                    'success' => true,
                    'content' => $result,
                    'provider' => 'ChatServiceManager',
                    'used_fallback' => false
                ];
            } else {
                Log::error("Maya: Invalid response format from ChatServiceManager", [
                    'result' => $result
                ]);
                throw new \Exception('Invalid response format from ChatServiceManager');
            }

        } catch (\Exception $e) {
            Log::error("Maya ChatServiceManager failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'ChatServiceManager failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Determine if query needs grounding search
     */
    private function shouldUseGrounding($message): bool
    {
        // Ensure message is a string
        if (!is_string($message)) {
            return false;
        }

        $messageLower = strtolower(trim($message));

        // Exclude simple greetings and conversational phrases that don't need search
        $excludePatterns = [
            'hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening',
            'thank you', 'thanks', 'ok', 'okay', 'yes', 'no', 'sure', 'alright',
            'bye', 'goodbye', 'see you', 'talk soon', 'nice', 'great', 'awesome',
            'cool', 'perfect', 'sounds good', 'got it', 'understood', 'i see',
            'how are you', 'how\'s it going', 'what\'s up', 'how do you do',
            'nice to meet you', 'pleased to meet you', 'good to see you'
        ];

        foreach ($excludePatterns as $pattern) {
            if ($messageLower === $pattern ||
                (strlen($messageLower) <= 20 && stripos($messageLower, $pattern) !== false)) {
                Log::info("Maya: Excluding simple greeting/response from search", ['message' => $message, 'pattern' => $pattern]);
                return false;
            }
        }

        // For Maya mode, we want to use search more frequently for accurate information
        $groundingTriggers = [
            // Research and evidence terms
            'latest research', 'recent study', 'new research', 'current trends',
            'what\'s trending', 'statistics', 'data shows', 'studies show',
            'research suggests', 'latest guidelines', 'updated recommendations',
            'new study', 'recent findings', 'current evidence', 'proven benefits',
            'scientific evidence', 'research shows', 'according to',

            // Effectiveness and benefits
            'effectiveness of', 'benefits of', 'side effects', 'how effective',
            'does it work', 'is it true', 'fact check', 'what are the benefits',
            'how does', 'why is', 'what causes', 'how to',

            // Health and wellness topics that benefit from current info
            'diet', 'nutrition', 'exercise', 'fitness', 'weight loss', 'supplement',
            'vitamin', 'mineral', 'protein', 'mediterranean diet', 'keto',
            'intermittent fasting', 'yoga', 'meditation', 'sleep', 'stress',
            'mental health', 'anxiety', 'depression', 'immunity', 'inflammation'
        ];

        $messageLower = strtolower($message);
        foreach ($groundingTriggers as $trigger) {
            if (stripos($messageLower, $trigger) !== false) {
                return true;
            }
        }

        // For Maya mode, be more liberal with search to provide current information
        // Check for any health/wellness related questions
        $healthTopics = [
            'health', 'wellness', 'nutrition', 'food', 'eating', 'drink',
            'supplement', 'vitamin', 'mineral', 'herb', 'extract',
            'exercise', 'workout', 'fitness', 'training', 'running',
            'diet', 'weight', 'lose', 'gain', 'muscle', 'fat',
            'sleep', 'stress', 'anxiety', 'depression', 'mood',
            'energy', 'tired', 'fatigue', 'immune', 'immunity',
            'heart', 'brain', 'skin', 'hair', 'bone', 'joint'
        ];

        foreach ($healthTopics as $topic) {
            if (stripos($messageLower, $topic) !== false) {
                Log::info("Maya: Search triggered by health topic", ['topic' => $topic, 'message' => $message]);
                return true;
            }
        }

        return false;
    }

    /**
     * Perform grounding search with OAuth
     */
    private function performGroundingSearch($query)
    {
        try {
            $searchQuery = $this->extractSearchTerms($query);
            
            // Get OAuth token
            $accessToken = trim(shell_exec('gcloud auth print-access-token'));
            
            if (empty($accessToken)) {
                Log::warning('Maya: Failed to obtain OAuth token for grounding search');
                return null;
            }
            
            // Perform search with global health focus
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$accessToken}",
                'Content-Type' => 'application/json'
            ])->timeout(10)->post(
                'https://discoveryengine.googleapis.com/v1alpha/projects/1026523769008/locations/global/collections/default_collection/engines/trusted-sources_1748608202932/servingConfigs/default_search:search',
                [
                    'query' => $searchQuery,
                    'pageSize' => 8,
                    'queryExpansionSpec' => ['condition' => 'AUTO'],
                    'spellCorrectionSpec' => ['mode' => 'AUTO'],
                    'languageCode' => 'en-US',
                    'userInfo' => ['timeZone' => 'Europe/London']
                ]
            );
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::warning('Maya: Grounding search API error', ['status' => $response->status()]);
            return null;
            
        } catch (\Exception $e) {
            Log::error('Maya: Grounding search failed', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Extract relevant search terms from user query
     */
    private function extractSearchTerms($query): string
    {
        // Ensure query is a string
        if (!is_string($query)) {
            return '';
        }

        $query = strtolower($query);
        
        // Remove common question words but keep health-specific terms
        $stopWords = ['what', 'how', 'when', 'where', 'why', 'is', 'are', 'can', 'should', 'tell', 'me', 'about', 'the', 'and', 'or', 'but'];
        $words = explode(' ', $query);
        $filteredWords = array_filter($words, function($word) use ($stopWords) {
            return !in_array($word, $stopWords) && strlen($word) > 2;
        });
        
        // Add context for better health-specific search
        $searchTerms = implode(' ', array_slice($filteredWords, 0, 6));
        
        // Enhance with health context if not already present
        if (!stripos($searchTerms, 'health') && !stripos($searchTerms, 'research') && !stripos($searchTerms, 'study')) {
            $searchTerms .= ' health research';
        }
        
        return $searchTerms;
    }

    /**
     * Format grounding results for LLM context
     */
    private function formatGroundingResults($groundingResults): string
    {
        if (!isset($groundingResults['results']) || empty($groundingResults['results'])) {
            return '';
        }

        $context = "Here are some current, trusted sources I found:\n\n";
        
        foreach (array_slice($groundingResults['results'], 0, 5) as $result) {
            if (isset($result['document']['derivedStructData']['title']) && 
                isset($result['document']['derivedStructData']['snippet'])) {
                
                $title = $result['document']['derivedStructData']['title'];
                $snippet = $result['document']['derivedStructData']['snippet'];
                $source = $result['document']['derivedStructData']['link'] ?? 'Trusted Health Source';
                
                $context .= "Source: {$title}\n";
                $context .= "Content: {$snippet}\n";
                $context .= "URL: {$source}\n\n";
            }
        }
        
        return $context;
    }

    /**
     * Get provider info
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Switch provider
     */
    public function switchProvider($newProvider)
    {
        $this->provider = $newProvider;
        $this->initializeService($newProvider);
    }

    /**
     * Get patient context for personalized responses
     */
    private function getPatientContext($patientId = null)
    {
        if (!$patientId) {
            $user = Auth::user();
            if ($user && $user->role === 'patient' && $user->patient) {
                $patientId = $user->patient->id;
            }
        }

        if (!$patientId) {
            return null;
        }

        try {
            $patient = Patient::find($patientId);
            if (!$patient) {
                return null;
            }

            return [
                'age' => $patient->date_of_birth ? now()->diffInYears($patient->date_of_birth) : null,
                'gender' => $patient->gender,
                'health_history' => $patient->health_history ?? [],
                'allergies' => $patient->allergies ?? [],
                'medications' => $patient->medications ?? [],
                'medical_conditions' => $patient->medical_conditions ?? [],
                'preferences' => $patient->preferences ?? [],
                'communication_preferences' => $patient->communication_preferences ?? []
            ];
        } catch (\Exception $e) {
            Log::warning('Maya: Failed to load patient context', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get conversation memory from previous chats
     */
    private function getConversationMemory($patientId = null, $currentConversationId = null)
    {
        if (!$patientId) {
            return null;
        }

        try {
            // Get recent wellness-related conversations (last 30 days)
            $recentConversations = ChatConversation::where('patient_id', $patientId)
                ->when($currentConversationId, function($query, $id) {
                    return $query->where('id', '!=', $id);
                })
                ->where('created_at', '>=', now()->subDays(30))
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            if ($recentConversations->isEmpty()) {
                return null;
            }

            $memory = [
                'recent_topics' => [],
                'health_concerns' => [],
                'recommendations_given' => []
            ];

            foreach ($recentConversations as $conversation) {
                // Extract wellness-related topics from conversation titles and health concerns
                if ($conversation->title && $this->isWellnessRelated($conversation->title)) {
                    $memory['recent_topics'][] = $conversation->title;
                }

                if ($conversation->health_concerns) {
                    // Handle both array and string formats
                    $concerns = is_array($conversation->health_concerns)
                        ? $conversation->health_concerns
                        : [$conversation->health_concerns];

                    foreach ($concerns as $concern) {
                        if (is_string($concern) && $this->isWellnessRelated($concern)) {
                            $memory['health_concerns'][] = $concern;
                        }
                    }
                }

                if ($conversation->recommendations) {
                    // Handle both array and string formats
                    $recommendations = is_array($conversation->recommendations)
                        ? $conversation->recommendations
                        : [$conversation->recommendations];

                    foreach ($recommendations as $recommendation) {
                        if (is_string($recommendation) && $this->isWellnessRelated($recommendation)) {
                            $memory['recommendations_given'][] = $recommendation;
                        }
                    }
                }
            }

            // Remove duplicates and limit items
            $memory['recent_topics'] = array_unique(array_slice($memory['recent_topics'], 0, 3));
            $memory['health_concerns'] = array_unique(array_slice($memory['health_concerns'], 0, 3));
            $memory['recommendations_given'] = array_unique(array_slice($memory['recommendations_given'], 0, 3));

            return $memory;
        } catch (\Exception $e) {
            Log::warning('Maya: Failed to load conversation memory', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Check if content is wellness-related (not medical)
     */
    private function isWellnessRelated($content)
    {
        // Handle null or non-string content
        if (!$content || (!is_string($content) && !is_array($content))) {
            return false;
        }

        // If content is an array, convert to string
        if (is_array($content)) {
            $content = implode(' ', array_filter($content, 'is_string'));
        }

        // Ensure we have a string
        if (!is_string($content)) {
            return false;
        }

        $wellnessKeywords = [
            'nutrition', 'diet', 'exercise', 'fitness', 'wellness', 'lifestyle',
            'sleep', 'stress', 'meditation', 'mindfulness', 'supplement',
            'vitamin', 'mineral', 'healthy eating', 'workout', 'mental health',
            'preventive', 'prevention', 'weight', 'energy', 'fatigue'
        ];

        $medicalKeywords = [
            'symptom', 'diagnosis', 'treatment', 'medication', 'prescription',
            'doctor', 'hospital', 'clinic', 'emergency', 'pain', 'fever',
            'infection', 'disease', 'condition', 'disorder'
        ];

        $contentLower = strtolower($content);

        // Check for medical keywords first (exclude if medical)
        foreach ($medicalKeywords as $keyword) {
            if (stripos($contentLower, $keyword) !== false) {
                return false;
            }
        }

        // Check for wellness keywords
        foreach ($wellnessKeywords as $keyword) {
            if (stripos($contentLower, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get enhanced system prompt with patient context and conversation memory
     */
    private function getEnhancedSystemPrompt($patientContext = null, $conversationMemory = null)
    {
        $basePrompt = self::getSystemPrompt();

        if (!$patientContext && !$conversationMemory) {
            return $basePrompt;
        }

        $contextInfo = "\n\n<user_context>\n";

        // Add patient context
        if ($patientContext) {
            if ($patientContext['age']) {
                $contextInfo .= "User age: {$patientContext['age']} years old\n";
            }

            if ($patientContext['gender']) {
                $contextInfo .= "Gender: {$patientContext['gender']}\n";
            }

            if (!empty($patientContext['allergies'])) {
                $allergies = implode(', ', $patientContext['allergies']);
                $contextInfo .= "Known allergies: {$allergies}\n";
            }

            if (!empty($patientContext['medical_conditions'])) {
                $conditions = implode(', ', $patientContext['medical_conditions']);
                $contextInfo .= "Medical conditions: {$conditions}\n";
            }

            if (!empty($patientContext['preferences']['feed_topics'])) {
                $interests = implode(', ', $patientContext['preferences']['feed_topics']);
                $contextInfo .= "Health interests: {$interests}\n";
            }
        }

        // Add conversation memory
        if ($conversationMemory) {
            if (!empty($conversationMemory['recent_topics'])) {
                $topics = implode(', ', $conversationMemory['recent_topics']);
                $contextInfo .= "Recent wellness topics discussed: {$topics}\n";
            }

            if (!empty($conversationMemory['health_concerns'])) {
                $concerns = implode(', ', $conversationMemory['health_concerns']);
                $contextInfo .= "Previous wellness concerns: {$concerns}\n";
            }

            if (!empty($conversationMemory['recommendations_given'])) {
                $recommendations = implode(', ', array_slice($conversationMemory['recommendations_given'], 0, 2));
                $contextInfo .= "Previous recommendations given: {$recommendations}\n";
            }
        }

        $contextInfo .= "</user_context>\n\n";
        $contextInfo .= "Use this context to personalize your wellness advice and reference previous conversations naturally. Be mindful of any allergies or medical conditions when discussing supplements or lifestyle changes. If you've given similar advice before, acknowledge it and build upon it.";

        return $basePrompt . $contextInfo;
    }
}
<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SeedBotPersonas extends Command
{
    protected $signature = 'bot:seed-personas {--force : Force recreate all personas}';
    protected $description = 'Seed the database with bot personas';

    public function handle()
    {
        $force = $this->option('force');
        
        if ($force) {
            $this->info('Deleting existing personas...');
            BotPersona::truncate();
        }

        $personas = $this->getPersonasData();
        
        $this->info('Creating bot personas...');
        $created = 0;
        $skipped = 0;

        foreach ($personas as $personaData) {
            $existing = BotPersona::where('handle', $personaData['handle'])->first();
            
            if ($existing && !$force) {
                $this->warn("Skipping {$personaData['handle']} - already exists");
                $skipped++;
                continue;
            }

            try {
                BotPersona::create($personaData);
                $this->info("Created persona: {$personaData['handle']}");
                $created++;
            } catch (\Exception $e) {
                $this->error("Failed to create {$personaData['handle']}: " . $e->getMessage());
            }
        }

        $this->info("Completed! Created: {$created}, Skipped: {$skipped}");
        
        return 0;
    }

    private function getPersonasData(): array
    {
        return [
            [
                'first_name' => 'Hannah',
                'last_name' => 'Kim',
                'handle' => '@hannah_in_flow',
                'age' => 29,
                'bio' => 'UX designer exploring the mind-body connection through daily yoga, meditation, and plant-based cooking.',
                'interests' => ['Vinyasa yoga', 'vegan recipes', 'mindfulness apps'],
                'instagram_posts' => ['Sunlit yoga poses', 'green smoothie recipes', 'meditation corner tours'],
                'ai_post_prompt' => 'Write an upbeat Instagram caption as Hannah Kim sharing her morning vinyasa flow, including a mindfulness tip and vegan smoothie recipe.',
                'image_prompt' => 'Bright, airy photo of a young woman in a yoga studio doing a warrior II pose, with a green smoothie on a wooden bench nearby.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#yoga', '#vegan', '#mindfulness', '#uxdesign'],
                'tone' => 'upbeat',
                'content_focus' => 'yoga',
            ],
            [
                'first_name' => 'Luis',
                'last_name' => 'Rodriguez',
                'handle' => '@runwithluis',
                'age' => 34,
                'bio' => 'Marathon-training elementary school teacher balancing lesson plans with long-distance runs and community 5Ks.',
                'interests' => ['Trail running', 'community races', 'running gear reviews'],
                'instagram_posts' => ['Finish-line selfies', 'trail snapshots', 'gear unboxing'],
                'ai_post_prompt' => 'Write an energetic Instagram post as Luis Rodriguez celebrating his latest half-marathon PR, including training reflections and a motivational call to action.',
                'image_prompt' => 'Photo of a smiling man crossing a marathon finish line at sunrise, bib number visible, with cheering spectators in the background.',
                'posts_per_week' => 4,
                'hashtag_preferences' => ['#running', '#marathon', '#teacher', '#motivation'],
                'tone' => 'energetic',
                'content_focus' => 'fitness',
            ],
            [
                'first_name' => 'Aisha',
                'last_name' => 'Malik',
                'handle' => '@balancedbites',
                'age' => 42,
                'bio' => 'Busy mom and finance executive crafting easy, balanced meals for her family.',
                'interests' => ['Meal prepping', 'family nutrition', 'budget shopping'],
                'instagram_posts' => ['Weekly meal-prep flatlays', 'grocery haul tips', 'kid-friendly recipes'],
                'ai_post_prompt' => 'Write a warm Instagram caption as Aisha Malik sharing her Sunday meal-prep routine for a week of balanced family dinners.',
                'image_prompt' => 'Overhead shot of colorful bento-style containers filled with grilled chicken, quinoa, roasted veggies, and fruit, arranged on a kitchen counter.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#mealprep', '#familynutrition', '#busymom', '#healthyeating'],
                'tone' => 'warm',
                'content_focus' => 'nutrition',
            ],
            [
                'first_name' => 'Marcus',
                'last_name' => 'Lee',
                'handle' => '@mindful_marcus',
                'age' => 26,
                'bio' => 'Software engineer using meditation and breathwork to manage stress and boost creativity.',
                'interests' => ['Guided meditation', 'breathwork techniques', 'mental health advocacy'],
                'instagram_posts' => ['Meditation corner tours', 'app recommendations', 'personal reflections'],
                'ai_post_prompt' => 'Write a reflective Instagram post as Marcus Lee describing his favorite 5-minute breathing exercise and how it helps at work.',
                'image_prompt' => 'Cozy home office corner with soft cushions, candles lit, and a laptop showing a meditation app interface.',
                'posts_per_week' => 2,
                'hashtag_preferences' => ['#meditation', '#breathwork', '#mentalhealth', '#softwareengineer'],
                'tone' => 'reflective',
                'content_focus' => 'mental health',
            ],
            [
                'first_name' => 'Priya',
                'last_name' => 'Sharma',
                'handle' => '@priya_pilates',
                'age' => 38,
                'bio' => 'Pilates instructor and online coach sharing alignment tips and reformer workouts.',
                'interests' => ['Pilates', 'posture correction', 'small-group classes'],
                'instagram_posts' => ['Reformer demos', 'posture before/after', 'client success stories'],
                'ai_post_prompt' => 'Write an instructional Instagram caption as Priya Sharma explaining the top three cues for perfecting the Teaser exercise on a reformer.',
                'image_prompt' => 'High-contrast studio shot of a woman in Pilates reformer Teaser position, instructor hands-on cueing her form.',
                'posts_per_week' => 4,
                'hashtag_preferences' => ['#pilates', '#reformer', '#posture', '#instructor'],
                'tone' => 'instructional',
                'content_focus' => 'fitness',
            ],
            [
                'first_name' => 'Jamal',
                'last_name' => 'Bennett',
                'handle' => '@jamal_strength',
                'age' => 31,
                'bio' => 'Personal trainer specializing in strength training and muscle recovery for busy professionals.',
                'interests' => ['Resistance training', 'foam-rolling tutorials', 'supplement reviews'],
                'instagram_posts' => ['Gym workout clips', 'demo of recovery routines', 'protein shake recipes'],
                'ai_post_prompt' => 'Write a punchy Instagram caption as Jamal Bennett sharing his go-to superset for building upper-body strength, with form tips.',
                'image_prompt' => 'Dynamic gym shot of a man performing a bench-press superset next to a rack of colorful dumbbells.',
                'posts_per_week' => 5,
                'hashtag_preferences' => ['#strength', '#personaltrainer', '#recovery', '#fitness'],
                'tone' => 'punchy',
                'content_focus' => 'fitness',
            ],
            [
                'first_name' => 'Elena',
                'last_name' => 'Petrova',
                'handle' => '@nature_nut_elena',
                'age' => 47,
                'bio' => 'Environmental lawyer turned weekend hiker, blending eco-advocacy with forest therapy.',
                'interests' => ['Forest bathing', 'trail conservation', 'eco-friendly gear'],
                'instagram_posts' => ['Mossy trail close-ups', 'eco-tip carousels', 'hiking outfit flatlays'],
                'ai_post_prompt' => 'Write a serene Instagram caption as Elena Petrova describing the mental reset she experienced during a forest bathing session.',
                'image_prompt' => 'Lush forest scene with dappled sunlight on mossy rocks and a walking stick leaning against a tree.',
                'posts_per_week' => 2,
                'hashtag_preferences' => ['#forestbathing', '#hiking', '#ecofriendly', '#nature'],
                'tone' => 'serene',
                'content_focus' => 'nature',
            ],
            [
                'first_name' => 'Noah',
                'last_name' => 'Johnson',
                'handle' => '@noah_the_nutrient',
                'age' => 23,
                'bio' => 'Nutrition science student experimenting with macro-focused grocery hauls and recipe hacks.',
                'interests' => ['Macronutrient tracking', 'budget meal hacks', 'supplement science'],
                'instagram_posts' => ['Nutrition-label close-ups', 'macro breakdown charts', 'meal prep videos'],
                'ai_post_prompt' => 'Write an informative Instagram post as Noah Johnson breaking down the macros of his post-workout smoothie bowl.',
                'image_prompt' => 'Close-up top-down photo of a smoothie bowl with banana slices, granola, chia seeds, and an overlay of macro numbers.',
                'posts_per_week' => 4,
                'hashtag_preferences' => ['#macros', '#nutrition', '#student', '#supplements'],
                'tone' => 'informative',
                'content_focus' => 'nutrition',
            ],
            [
                'first_name' => 'Sofia',
                'last_name' => 'Garcia',
                'handle' => '@sofiastressfree',
                'age' => 55,
                'bio' => 'Retired teacher embracing Tai Chi and meditation to stay active and calm.',
                'interests' => ['Tai Chi forms', 'senior wellness', 'community meetups'],
                'instagram_posts' => ['Slow-motion Tai Chi clips', 'group meetup highlights', 'mindfulness quotes'],
                'ai_post_prompt' => 'Write a gentle Instagram caption as Sofia Garcia guiding seniors through the first three moves of Sun Style Tai Chi.',
                'image_prompt' => 'Gentle outdoor scene of a mature woman in flowing clothes performing a Tai Chi movement on a park path.',
                'posts_per_week' => 2,
                'hashtag_preferences' => ['#taichi', '#seniorwellness', '#meditation', '#community'],
                'tone' => 'gentle',
                'content_focus' => 'senior wellness',
            ],
            [
                'first_name' => 'Omar',
                'last_name' => 'Hassan',
                'handle' => '@omar_cyclist',
                'age' => 40,
                'bio' => 'Urban planner by day, competitive road cyclist by dawn—chasing KOMs and sunrise views.',
                'interests' => ['Road racing', 'route planning apps', 'aero gear'],
                'instagram_posts' => ['Sunrise ride selfies', 'Strava route screenshots', 'bike maintenance tips'],
                'ai_post_prompt' => 'Write an adventurous Instagram post as Omar Hassan recapping his dawn solo ride and the best city-scape view he found.',
                'image_prompt' => 'Silhouetted cyclist on a rooftop overlook at sunrise, road bike leaned against railing.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#cycling', '#roadbike', '#urbanplanning', '#sunrise'],
                'tone' => 'adventurous',
                'content_focus' => 'cycling',
            ],
            [
                'first_name' => 'Mei',
                'last_name' => 'Tanaka',
                'handle' => '@meimindfulchef',
                'age' => 36,
                'bio' => 'Culinary artist merging mindfulness with seasonal, whole-food cooking.',
                'interests' => ['Fermentation', 'mindful eating', 'farm-to-table ingredients'],
                'instagram_posts' => ['Step-by-step recipe reels', 'ingredient foraging stories', 'mindful plating'],
                'ai_post_prompt' => 'Write a sensory-rich Instagram caption as Mei Tanaka guiding followers through making spring vegetable kimchi.',
                'image_prompt' => 'Close-up of hands mixing colorful spring vegetables in a ceramic bowl with sea salt and chili flakes.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#mindfulcooking', '#fermentation', '#farmtotable', '#seasonal'],
                'tone' => 'sensory-rich',
                'content_focus' => 'cooking',
            ],
            [
                'first_name' => 'Gabriel',
                'last_name' => 'Nguyen',
                'handle' => '@gabriel_guthealth',
                'age' => 28,
                'bio' => 'Microbiologist exploring gut-brain axis through diet experiments and probiotic trials.',
                'interests' => ['Gut-health recipes', 'scientific explainers', 'supplement deep-dives'],
                'instagram_posts' => ['Infographic carousels', 'lab notebook glimpses', 'healthy gut snack ideas'],
                'ai_post_prompt' => 'Write a concise Instagram carousel caption as Gabriel Nguyen explaining three top probiotic-rich foods and their benefits.',
                'image_prompt' => 'Clean infographic-style image showing yogurt, kimchi, and kombucha jars with labeled benefits.',
                'posts_per_week' => 4,
                'hashtag_preferences' => ['#guthealth', '#probiotics', '#microbiome', '#science'],
                'tone' => 'scientific',
                'content_focus' => 'gut health',
            ],
            [
                'first_name' => 'Amina',
                'last_name' => 'Farouk',
                'handle' => '@amina_zenhome',
                'age' => 45,
                'bio' => 'Interior designer creating zen-inspired home retreats for stress relief.',
                'interests' => ['Biophilic design', 'aromatherapy', 'minimalism'],
                'instagram_posts' => ['Corner makeovers', 'before/after room shots', 'DIY diffuser blends'],
                'ai_post_prompt' => 'Write a soothing Instagram caption as Amina Farouk walking through her latest zen bedroom makeover and scent recommendations.',
                'image_prompt' => 'Minimalist bedroom with natural wood accents, potted plants, soft linen throws, and a ceramic diffuser.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#zendesign', '#aromatherapy', '#minimalism', '#interiordesign'],
                'tone' => 'soothing',
                'content_focus' => 'home wellness',
            ],
            [
                'first_name' => 'Ethan',
                'last_name' => 'Clarke',
                'handle' => '@ethan_welltech',
                'age' => 30,
                'bio' => 'Tech enthusiast testing the latest wearable devices and health-tracking apps.',
                'interests' => ['Smartwatches', 'sleep-tracking', 'biohacking'],
                'instagram_posts' => ['Gear unboxings', 'app dashboard screenshots', 'biometric data breakdowns'],
                'ai_post_prompt' => 'Write a tech-savvy Instagram post as Ethan Clarke reviewing a new sleep-tracker watch\'s pros and cons after one week.',
                'image_prompt' => 'Sleek smartwatch on a bedside table next to sleep analytics charts projected on a phone screen.',
                'posts_per_week' => 4,
                'hashtag_preferences' => ['#wearabletech', '#biohacking', '#sleeptracking', '#healthtech'],
                'tone' => 'tech-savvy',
                'content_focus' => 'health tech',
            ],
            [
                'first_name' => 'Rosa',
                'last_name' => 'Almeida',
                'handle' => '@rosa_recovery',
                'age' => 50,
                'bio' => 'Physical therapist championing injury prevention and mobility for all ages.',
                'interests' => ['Mobility drills', 'injury-prevention tips', 'patient testimonials'],
                'instagram_posts' => ['Exercise demos', 'client success videos', 'anatomy illustrations'],
                'ai_post_prompt' => 'Write an encouraging Instagram caption as Rosa Almeida demonstrating a safe knee-mobility drill for desk workers.',
                'image_prompt' => 'Split-screen image: on left, a woman seated doing a knee-lift stretch; on right, an anatomical overlay of the knee joint.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#physicaltherapy', '#mobility', '#injuryprevention', '#movement'],
                'tone' => 'encouraging',
                'content_focus' => 'physical therapy',
            ],
            [
                'first_name' => 'Kwame',
                'last_name' => 'Mensah',
                'handle' => '@kwame_mindfuel',
                'age' => 22,
                'bio' => 'University student fueling study sessions with brain-boosting snacks and focus hacks.',
                'interests' => ['Nootropics', 'study playlists', 'time management'],
                'instagram_posts' => ['Snack prep reels', 'Pomodoro-session selfies', 'focus playlist shares'],
                'ai_post_prompt' => 'Write a lively Instagram caption as Kwame Mensah showing his top three brain-fuel snacks for exam week, with timing tips.',
                'image_prompt' => 'Top-down shot of a desk with notebooks, headphones, and three snack bowls labeled with nutrient info.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#studysnacks', '#brainfood', '#student', '#productivity'],
                'tone' => 'lively',
                'content_focus' => 'brain health',
            ],
            [
                'first_name' => 'Ingrid',
                'last_name' => 'Olsen',
                'handle' => '@ingrid_icebath',
                'age' => 37,
                'bio' => 'Adventure coach using cold-water immersion and breathwork to build resilience.',
                'interests' => ['Ice baths', 'Wim Hof Method', 'outdoor adventures'],
                'instagram_posts' => ['Shivering-smile selfies', 'breathwork tutorials', 'adventure recaps'],
                'ai_post_prompt' => 'Write an empowering Instagram caption as Ingrid Olsen capturing her first two-minute ice bath experience and breath-hold technique.',
                'image_prompt' => 'Outdoor barrel ice bath half-submerged figure exhaling steam in a snowy forest setting.',
                'posts_per_week' => 2,
                'hashtag_preferences' => ['#icebath', '#wimhof', '#coldtherapy', '#resilience'],
                'tone' => 'empowering',
                'content_focus' => 'cold therapy',
            ],
            [
                'first_name' => 'Javier',
                'last_name' => 'Martinez',
                'handle' => '@jc_arttherapy',
                'age' => 44,
                'bio' => 'Art therapist blending creativity and mental health support through guided exercises.',
                'interests' => ['Art journaling', 'therapeutic doodles', 'group workshops'],
                'instagram_posts' => ['Step-by-step art prompts', 'client artwork highlights', 'mental-health quotes'],
                'ai_post_prompt' => 'Write a compassionate Instagram caption as Javier Martinez guiding followers through a five-minute doodle exercise for stress relief.',
                'image_prompt' => 'Flatlay of open sketchbook with simple doodles in colored pencils, alongside inspirational quote card.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#arttherapy', '#creativity', '#mentalhealth', '#selfcare'],
                'tone' => 'compassionate',
                'content_focus' => 'art therapy',
            ],
            [
                'first_name' => 'Leila',
                'last_name' => 'Haddad',
                'handle' => '@leila_sleepwell',
                'age' => 32,
                'bio' => 'Sleep coach helping urban professionals reclaim deep, restorative rest.',
                'interests' => ['Sleep hygiene', 'bedroom optimization', 'sleep-tracker analysis'],
                'instagram_posts' => ['Nighttime routine reels', 'room-darkening tips', 'gadget reviews'],
                'ai_post_prompt' => 'Write a calming Instagram caption as Leila Haddad outlining her ideal 5-step pre-sleep ritual for busy city dwellers.',
                'image_prompt' => 'Dimly lit bedroom scene with blackout curtains, a Himalayan salt lamp, and a sleep-tracker on a nightstand.',
                'posts_per_week' => 3,
                'hashtag_preferences' => ['#sleepcoach', '#sleephygiene', '#bedtime', '#rest'],
                'tone' => 'calming',
                'content_focus' => 'sleep wellness',
            ],
        ];
    }
}

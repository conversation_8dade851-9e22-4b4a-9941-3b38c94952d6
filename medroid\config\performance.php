<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains performance-related configuration options for the
    | application, including execution time limits and memory settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Execution Time Limits
    |--------------------------------------------------------------------------
    |
    | These values control the maximum execution time for different types
    | of operations. Values are in seconds.
    |
    */
    'execution_time' => [
        'default' => 30,
        'feed_loading' => 300,      // 5 minutes for feed operations
        'image_processing' => 120,   // 2 minutes for image operations
        'data_import' => 600,       // 10 minutes for data imports
        'report_generation' => 180,  // 3 minutes for reports
    ],

    /*
    |--------------------------------------------------------------------------
    | Memory Limits
    |--------------------------------------------------------------------------
    |
    | These values control the memory limits for different operations.
    | Values should include the unit (e.g., '256M', '512M', '1G').
    |
    */
    'memory_limits' => [
        'default' => '256M',
        'feed_loading' => '512M',
        'image_processing' => '1G',
        'data_import' => '1G',
        'report_generation' => '512M',
    ],

    /*
    |--------------------------------------------------------------------------
    | Request Timeouts
    |--------------------------------------------------------------------------
    |
    | These values control timeout settings for external requests and
    | database operations. Values are in seconds.
    |
    */
    'timeouts' => [
        'database_query' => 60,
        'external_api' => 30,
        'file_upload' => 120,
        'instagram_api' => 60,
    ],

    /*
    |--------------------------------------------------------------------------
    | Pagination Settings
    |--------------------------------------------------------------------------
    |
    | These values control pagination limits to prevent performance issues.
    |
    */
    'pagination' => [
        'max_per_page' => 50,
        'default_per_page' => 10,
        'feed_per_page' => 20,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Performance-related cache configuration.
    |
    */
    'cache' => [
        'feed_ttl' => 300,          // 5 minutes
        'user_data_ttl' => 1800,    // 30 minutes
        'static_content_ttl' => 3600, // 1 hour
    ],
];

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('automated_posts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bot_user_id')->constrained('bot_users')->onDelete('cascade');
            $table->foreignId('bot_persona_id')->constrained('bot_personas')->onDelete('cascade');
            $table->text('caption');
            $table->json('hashtags'); // Array of hashtags
            $table->string('image_url')->nullable();
            $table->string('midjourney_task_id')->nullable(); // Midjourney API task ID
            $table->enum('status', ['draft', 'generating_image', 'ready', 'posted', 'failed'])->default('draft');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('posted_at')->nullable();
            $table->json('generation_prompts')->nullable(); // Store the prompts used
            $table->json('engagement_data')->nullable(); // Likes, comments, shares after posting
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamps();
            
            $table->index(['status', 'scheduled_at']);
            $table->index(['bot_user_id', 'posted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('automated_posts');
    }
};

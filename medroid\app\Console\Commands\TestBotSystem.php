<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use App\Services\BotUserCreationService;
use App\Services\BotContentGenerationService;
use App\Services\MidjourneyService;
use App\Services\ContentValidationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestBotSystem extends Command
{
    protected $signature = 'bot:test 
                            {test? : Specific test to run (setup|content|image|validation|full)}
                            {--persona= : Test with specific persona ID}
                            {--skip-image : Skip image generation for faster testing}';
    
    protected $description = 'Test the automated bot posting system';

    private BotUserCreationService $botUserService;
    private BotContentGenerationService $contentService;
    private MidjourneyService $midjourneyService;
    private ContentValidationService $validationService;

    public function __construct(
        BotUserCreationService $botUserService,
        BotContentGenerationService $contentService,
        MidjourneyService $midjourneyService,
        ContentValidationService $validationService
    ) {
        parent::__construct();
        $this->botUserService = $botUserService;
        $this->contentService = $contentService;
        $this->midjourneyService = $midjourneyService;
        $this->validationService = $validationService;
    }

    public function handle()
    {
        $test = $this->argument('test') ?? 'full';
        $personaId = $this->option('persona');
        $skipImage = $this->option('skip-image');

        $this->info("🤖 Starting Bot System Test: {$test}");
        $this->line('==========================================');

        switch ($test) {
            case 'setup':
                return $this->testSetup();
            
            case 'content':
                return $this->testContentGeneration($personaId);
            
            case 'image':
                return $this->testImageGeneration($personaId);
            
            case 'validation':
                return $this->testContentValidation($personaId);
            
            case 'full':
                return $this->runFullTest($personaId, $skipImage);
            
            default:
                $this->error("Unknown test: {$test}");
                $this->info("Available tests: setup, content, image, validation, full");
                return 1;
        }
    }

    private function testSetup(): int
    {
        $this->info('🔧 Testing Bot System Setup...');

        // Check if personas exist
        $personaCount = BotPersona::count();
        if ($personaCount === 0) {
            $this->error('❌ No personas found. Run: php artisan bot:seed-personas');
            return 1;
        }
        $this->info("✅ Found {$personaCount} personas");

        // Check bot users
        $botUserCount = BotPersona::whereHas('botUser')->count();
        $this->info("✅ {$botUserCount} personas have bot users");

        if ($botUserCount < $personaCount) {
            $missing = $personaCount - $botUserCount;
            $this->warn("⚠️  {$missing} personas missing bot users");
            $this->info("Run: php artisan bot:manage create-users");
        }

        // Check API keys
        $this->checkApiKeys();

        // Check database tables
        $this->checkDatabaseTables();

        $this->info('✅ Setup test completed');
        return 0;
    }

    private function testContentGeneration(?string $personaId): int
    {
        $this->info('📝 Testing Content Generation...');

        $persona = $this->getTestPersona($personaId);
        if (!$persona) {
            return 1;
        }

        try {
            $this->info("Generating content for: {$persona->full_name}");
            
            $post = $this->contentService->generatePost($persona);
            
            if ($post) {
                $this->info('✅ Content generated successfully');
                $this->line("Caption length: " . strlen($post->caption));
                $this->line("Hashtags: " . count($post->hashtags ?? []));
                $this->line("Status: {$post->status}");
                
                // Show preview
                $this->info("\n📄 Generated Content Preview:");
                $this->line(substr($post->caption, 0, 200) . '...');
                $this->line("Hashtags: " . implode(' ', array_slice($post->hashtags ?? [], 0, 5)));
                
                return 0;
            } else {
                $this->error('❌ Failed to generate content');
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Content generation failed: " . $e->getMessage());
            return 1;
        }
    }

    private function testImageGeneration(?string $personaId): int
    {
        $this->info('🎨 Testing Image Generation...');

        $persona = $this->getTestPersona($personaId);
        if (!$persona) {
            return 1;
        }

        // Check API key
        if (empty(env('PIAPI_API_KEY'))) {
            $this->error('❌ PIAPI_API_KEY not configured');
            $this->info('Please add your PiAPI key to .env file');
            return 1;
        }

        try {
            // Create a test post
            $post = AutomatedPost::create([
                'bot_user_id' => $persona->botUser->id,
                'bot_persona_id' => $persona->id,
                'caption' => 'Test post for image generation',
                'hashtags' => ['#test'],
                'status' => 'draft',
            ]);

            $this->info("Testing image generation for: {$persona->full_name}");
            $this->info("Using prompt: {$persona->image_prompt}");

            $enhancedPrompt = $this->midjourneyService->enhancePrompt($persona->image_prompt);
            $this->line("Enhanced prompt: {$enhancedPrompt}");

            $imageLog = $this->midjourneyService->generateImage($enhancedPrompt, $post);

            if ($imageLog) {
                $this->info('✅ Image generation request submitted');
                $this->line("Task ID: {$imageLog->midjourney_task_id}");
                $this->line("Status: {$imageLog->status}");
                
                $this->info('⏳ Checking status in 30 seconds...');
                sleep(30);
                
                $completed = $this->midjourneyService->processCompletedGeneration($imageLog);
                $imageLog->refresh();
                
                $this->line("Final status: {$imageLog->status}");
                if ($imageLog->image_url) {
                    $this->info("✅ Image URL: {$imageLog->image_url}");
                }
                
                return 0;
            } else {
                $this->error('❌ Failed to start image generation');
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Image generation failed: " . $e->getMessage());
            return 1;
        }
    }

    private function testContentValidation(?string $personaId): int
    {
        $this->info('🔍 Testing Content Validation...');

        $persona = $this->getTestPersona($personaId);
        if (!$persona) {
            return 1;
        }

        try {
            // Create a test post
            $post = AutomatedPost::create([
                'bot_user_id' => $persona->botUser->id,
                'bot_persona_id' => $persona->id,
                'caption' => 'This is a test post with some health advice. Remember to consult your doctor before making any changes.',
                'hashtags' => ['#health', '#wellness', '#test'],
                'status' => 'draft',
                'image_url' => 'https://example.com/test-image.jpg',
            ]);

            $this->info("Testing validation for: {$persona->full_name}");

            $validation = $this->validationService->validatePost($post);

            $this->info('✅ Validation completed');
            $this->line("Valid: " . ($validation['is_valid'] ? 'Yes' : 'No'));
            $this->line("Score: {$validation['score']}/100");
            $this->line("Errors: " . count($validation['errors']));
            $this->line("Warnings: " . count($validation['warnings']));

            if (!empty($validation['errors'])) {
                $this->error('Errors found:');
                foreach ($validation['errors'] as $error) {
                    $this->line("  - {$error}");
                }
            }

            if (!empty($validation['warnings'])) {
                $this->warn('Warnings found:');
                foreach ($validation['warnings'] as $warning) {
                    $this->line("  - {$warning}");
                }
            }

            // Test auto-fix
            $fixedPost = $this->validationService->autoFixPost($post);
            $this->info('✅ Auto-fix applied');

            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Validation failed: " . $e->getMessage());
            return 1;
        }
    }

    private function runFullTest(?string $personaId, bool $skipImage): int
    {
        $this->info('🚀 Running Full Bot System Test...');

        $results = [];

        // Test setup
        $this->line("\n1. Testing Setup...");
        $results['setup'] = $this->testSetup() === 0;

        // Test content generation
        $this->line("\n2. Testing Content Generation...");
        $results['content'] = $this->testContentGeneration($personaId) === 0;

        // Test image generation (unless skipped)
        if (!$skipImage) {
            $this->line("\n3. Testing Image Generation...");
            $results['image'] = $this->testImageGeneration($personaId) === 0;
        } else {
            $this->info("\n3. Skipping Image Generation...");
            $results['image'] = true;
        }

        // Test validation
        $this->line("\n4. Testing Content Validation...");
        $results['validation'] = $this->testContentValidation($personaId) === 0;

        // Summary
        $this->line("\n" . str_repeat('=', 50));
        $this->info('📊 Test Results Summary:');
        
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $test => $result) {
            $status = $result ? '✅ PASS' : '❌ FAIL';
            $this->line("  {$test}: {$status}");
            if ($result) $passed++;
        }

        $this->line("\nOverall: {$passed}/{$total} tests passed");

        if ($passed === $total) {
            $this->info('🎉 All tests passed! Bot system is ready.');
            return 0;
        } else {
            $this->error('❌ Some tests failed. Please check the issues above.');
            return 1;
        }
    }

    private function getTestPersona(?string $personaId): ?BotPersona
    {
        if ($personaId) {
            $persona = BotPersona::with('botUser')->find($personaId);
            if (!$persona) {
                $this->error("❌ Persona not found: {$personaId}");
                return null;
            }
        } else {
            $persona = BotPersona::with('botUser')->whereHas('botUser')->first();
            if (!$persona) {
                $this->error('❌ No personas with bot users found');
                $this->info('Run: php artisan bot:manage create-users');
                return null;
            }
        }

        return $persona;
    }

    private function checkApiKeys(): void
    {
        $keys = [
            'PIAPI_API_KEY' => 'PiAPI (for image generation)',
            'OPENAI_API_KEY' => 'OpenAI (for content generation)',
            'GROQ_API_KEY' => 'Groq (alternative for content generation)',
        ];

        foreach ($keys as $key => $description) {
            if (env($key)) {
                $this->info("✅ {$key} configured");
            } else {
                $this->warn("⚠️  {$key} not configured - {$description}");
            }
        }
    }

    private function checkDatabaseTables(): void
    {
        $tables = [
            'bot_personas',
            'bot_users', 
            'automated_posts',
            'posting_schedules',
            'image_generation_logs',
        ];

        foreach ($tables as $table) {
            try {
                \DB::table($table)->count();
                $this->info("✅ Table {$table} exists");
            } catch (\Exception $e) {
                $this->error("❌ Table {$table} missing or inaccessible");
            }
        }
    }
}

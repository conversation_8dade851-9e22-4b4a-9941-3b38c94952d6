<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserCredit;
use App\Models\CreditTransaction;
use App\Services\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UserCreditController extends Controller
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    /**
     * Get all user credits (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view credits
        if (!Auth::user()->can('view credits')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = UserCredit::with('user');

        // Filter by search term if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $credits = $query->orderBy('updated_at', 'desc')->paginate($perPage);

        return response()->json($credits);
    }

    /**
     * Get the current user's credit balance
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCreditBalance(Request $request)
    {
        $user = Auth::user();
        $credit = $this->creditService->getUserCredit($user->id);

        // Get breakdown by source for sidebar display
        $referralEarnings = CreditTransaction::where('user_id', $user->id)
            ->where('type', 'earned')
            ->where('source', 'referral')
            ->sum('amount') ?? 0;

        $adminCredits = CreditTransaction::where('user_id', $user->id)
            ->where('type', 'earned')
            ->where('source', 'admin')
            ->sum('amount') ?? 0;

        return response()->json([
            'balance' => (float) $credit->balance,
            'total_earned' => (float) $credit->total_earned,
            'total_used' => (float) $credit->total_used,
            'referral_earnings' => (float) $referralEarnings,
            'admin_credits' => (float) $adminCredits,
        ]);
    }

    /**
     * Get the current user's credit transactions
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCreditTransactions(Request $request)
    {
        $user = Auth::user();
        $limit = $request->input('limit', 20);
        $transactions = $this->creditService->getUserTransactions($user->id, $limit);

        return response()->json([
            'transactions' => $transactions,
        ]);
    }

    /**
     * Get transaction history for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactionHistory(Request $request)
    {
        $user = Auth::user();

        $query = CreditTransaction::where('user_id', $user->id);

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }

        // Filter by source if provided
        if ($request->has('source')) {
            $query->where('source', $request->input('source'));
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [
                $request->input('start_date'),
                $request->input('end_date'),
            ]);
        }

        // Paginate results
        $perPage = $request->input('per_page', 20);
        $transactions = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($transactions);
    }

    /**
     * Use credits for a payment
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function useCredits(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'appointment_id' => 'required|exists:appointments,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $amount = $request->input('amount');
        $appointmentId = $request->input('appointment_id');

        // Check if user has enough credit
        if (!$this->creditService->hasEnoughCredit($user->id, $amount)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient credit balance.',
            ], 400);
        }

        try {
            // Use the credit
            $transaction = $this->creditService->useCredit(
                $user->id,
                $amount,
                'appointment',
                $appointmentId,
                "Credit used for appointment #$appointmentId"
            );

            return response()->json([
                'success' => true,
                'message' => 'Credits applied successfully.',
                'transaction' => $transaction,
                'remaining_balance' => $this->creditService->getUserCredit($user->id)->balance,
            ]);
        } catch (\Exception $e) {
            Log::error("Error using credits: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply credits. Please try again.',
            ], 500);
        }
    }

    /**
     * Add credits to a user (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addCredits(Request $request)
    {
        // Check if user has permission to manage credits
        // For admin panel, we'll allow any admin user to add credits
        $user = Auth::user();
        if (!$user || (!$user->can('manage credits') && $user->role !== 'admin')) {
            Log::warning('Unauthorized attempt to add credits', [
                'user_id' => $user ? $user->id : null,
                'role' => $user ? $user->role : null,
                'ip' => $request->ip()
            ]);
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'source' => 'required|string',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            Log::warning('Validation failed when adding credits', [
                'errors' => $validator->errors()->toArray(),
                'input' => $request->all()
            ]);
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $userId = $request->input('user_id');
        $amount = $request->input('amount');
        $source = $request->input('source');
        $description = $request->input('description');

        // Log the attempt
        Log::info('Adding credits to user', [
            'admin_id' => $user->id,
            'user_id' => $userId,
            'amount' => $amount,
            'source' => $source,
            'description' => $description
        ]);

        try {
            // Verify the user exists
            $targetUser = User::find($userId);
            if (!$targetUser) {
                Log::error("User not found when adding credits: {$userId}");
                return response()->json([
                    'success' => false,
                    'message' => 'User not found.',
                ], 404);
            }

            // Add the credits
            $transaction = $this->creditService->addCredit(
                $userId,
                $amount,
                $source,
                null,
                $description ?? "Credit added by admin"
            );

            // Get the updated balance
            $newBalance = $this->creditService->getUserCredit($userId)->balance;

            Log::info('Credits added successfully', [
                'admin_id' => $user->id,
                'user_id' => $userId,
                'amount' => $amount,
                'transaction_id' => $transaction->id,
                'new_balance' => $newBalance
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Credits added successfully.',
                'transaction' => $transaction,
                'new_balance' => $newBalance,
            ]);
        } catch (\Exception $e) {
            Log::error("Error adding credits: " . $e->getMessage(), [
                'admin_id' => $user->id,
                'user_id' => $userId,
                'amount' => $amount,
                'source' => $source,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to add credits. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all credit transactions (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllCreditTransactions(Request $request)
    {
        // Check if user has permission to view credits
        if (!Auth::user()->can('view credits')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = CreditTransaction::with('user');

        // Filter by user if provided
        if ($request->has('user_id')) {
            $query->where('user_id', $request->input('user_id'));
        }

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }

        // Filter by source if provided
        if ($request->has('source')) {
            $query->where('source', $request->input('source'));
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [
                $request->input('start_date'),
                $request->input('end_date'),
            ]);
        }

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $transactions = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($transactions);
    }

    /**
     * Get credits statistics (admin only)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCreditsStats(Request $request)
    {
        // Check if user has permission to view credits
        if (!Auth::user()->can('view credits')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get total credits earned
        $totalEarned = CreditTransaction::where('type', 'earned')->sum('amount') ?? 0;

        // Get total credits used
        $totalUsed = CreditTransaction::where('type', 'used')->sum('amount') ?? 0;

        // Get current total balance across all users
        $currentBalance = UserCredit::sum('balance') ?? 0;

        // Get total number of transactions
        $totalTransactions = CreditTransaction::count();

        // Get total number of users with credits
        $usersWithCredits = UserCredit::where('balance', '>', 0)->count();

        // Get credits by source breakdown
        $creditsBySource = CreditTransaction::where('type', 'earned')
            ->selectRaw('source, SUM(amount) as total')
            ->groupBy('source')
            ->pluck('total', 'source')
            ->toArray();

        // Get monthly stats for the last 12 months
        $monthlyStats = CreditTransaction::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month')
            ->selectRaw('SUM(CASE WHEN type = "earned" THEN amount ELSE 0 END) as earned')
            ->selectRaw('SUM(CASE WHEN type = "used" THEN amount ELSE 0 END) as used')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return response()->json([
            'total_earned' => (float) $totalEarned,
            'total_used' => (float) $totalUsed,
            'current_balance' => (float) $currentBalance,
            'total_transactions' => $totalTransactions,
            'users_with_credits' => $usersWithCredits,
            'credits_by_source' => $creditsBySource,
            'monthly_stats' => $monthlyStats,
        ]);
    }
}
